package com.example.budgettracker

import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface ApiService {
    // General GET request
    @GET("/api/{table}")
    fun getRecords(
        @Path("table") table: String,
        @Query("monthyear") monthyear: String?,
        @Query("user_id") user_id: String
    ): Call<List<Record>>

    // General PUT request
    @PUT("/api/{table}")
    fun updateRecord(
        @Path("table") table: String,
        @Query("user_id") user_id: String,
        @Query("date") date: String,
        @Body data: Map<String, Any>
    ): Call<Void>

    // General DELETE request
    @DELETE("/api/{table}/{id}")
    fun deleteRecord(
        @Path("table") table: String,
        @Path("id") id: String
    ): Call<Void>

    // General POST request
    @POST("/api/{table}")
    fun createRecord(
        @Path("table") table: String,
        @Body data: Map<String, Any>
    ): Call<Void>

    @POST("/api/all/sync")
    fun syncAllData(@Body data: Map<String, Any>): Call<Void>
}
