// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SettingsBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final Button buttonClearUserData;

  @NonNull
  public final Button buttonLanguageSettings;

  @NonNull
  public final Button datePickerButton;

  @NonNull
  public final EditText editText2;

  @NonNull
  public final EditText editText4;

  @NonNull
  public final BaseBinding footer;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final Button saveButton;

  @NonNull
  public final RelativeLayout spinnerContainer;

  @NonNull
  public final TextView title;

  @NonNull
  public final TextView titleDate;

  @NonNull
  public final TextView transBalance;

  @NonNull
  public final TextView transBudget;

  @NonNull
  public final TextView transTarget;

  private SettingsBinding(@NonNull RelativeLayout rootView, @NonNull ImageButton backButton,
      @NonNull Button buttonClearUserData, @NonNull Button buttonLanguageSettings,
      @NonNull Button datePickerButton, @NonNull EditText editText2, @NonNull EditText editText4,
      @NonNull BaseBinding footer, @NonNull LinearLayout headerLayout,
      @NonNull RelativeLayout rootLayout, @NonNull Button saveButton,
      @NonNull RelativeLayout spinnerContainer, @NonNull TextView title,
      @NonNull TextView titleDate, @NonNull TextView transBalance, @NonNull TextView transBudget,
      @NonNull TextView transTarget) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.buttonClearUserData = buttonClearUserData;
    this.buttonLanguageSettings = buttonLanguageSettings;
    this.datePickerButton = datePickerButton;
    this.editText2 = editText2;
    this.editText4 = editText4;
    this.footer = footer;
    this.headerLayout = headerLayout;
    this.rootLayout = rootLayout;
    this.saveButton = saveButton;
    this.spinnerContainer = spinnerContainer;
    this.title = title;
    this.titleDate = titleDate;
    this.transBalance = transBalance;
    this.transBudget = transBudget;
    this.transTarget = transTarget;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.buttonClearUserData;
      Button buttonClearUserData = ViewBindings.findChildViewById(rootView, id);
      if (buttonClearUserData == null) {
        break missingId;
      }

      id = R.id.buttonLanguageSettings;
      Button buttonLanguageSettings = ViewBindings.findChildViewById(rootView, id);
      if (buttonLanguageSettings == null) {
        break missingId;
      }

      id = R.id.date_picker_button;
      Button datePickerButton = ViewBindings.findChildViewById(rootView, id);
      if (datePickerButton == null) {
        break missingId;
      }

      id = R.id.editText2;
      EditText editText2 = ViewBindings.findChildViewById(rootView, id);
      if (editText2 == null) {
        break missingId;
      }

      id = R.id.editText4;
      EditText editText4 = ViewBindings.findChildViewById(rootView, id);
      if (editText4 == null) {
        break missingId;
      }

      id = R.id.footer;
      View footer = ViewBindings.findChildViewById(rootView, id);
      if (footer == null) {
        break missingId;
      }
      BaseBinding binding_footer = BaseBinding.bind(footer);

      id = R.id.header_layout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      RelativeLayout rootLayout = (RelativeLayout) rootView;

      id = R.id.save_button;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      id = R.id.title_date;
      TextView titleDate = ViewBindings.findChildViewById(rootView, id);
      if (titleDate == null) {
        break missingId;
      }

      id = R.id.trans_balance;
      TextView transBalance = ViewBindings.findChildViewById(rootView, id);
      if (transBalance == null) {
        break missingId;
      }

      id = R.id.trans_budget;
      TextView transBudget = ViewBindings.findChildViewById(rootView, id);
      if (transBudget == null) {
        break missingId;
      }

      id = R.id.trans_target;
      TextView transTarget = ViewBindings.findChildViewById(rootView, id);
      if (transTarget == null) {
        break missingId;
      }

      return new SettingsBinding((RelativeLayout) rootView, backButton, buttonClearUserData,
          buttonLanguageSettings, datePickerButton, editText2, editText4, binding_footer,
          headerLayout, rootLayout, saveButton, spinnerContainer, title, titleDate, transBalance,
          transBudget, transTarget);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
