package com.example.budgettracker;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.widget.ImageButton;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;
import java.util.Locale;
import androidx.appcompat.app.AppCompatActivity;
import android.os.LocaleList;
public class LanguageActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.language);

        RadioGroup radioGroup = findViewById(R.id.radio_group);

        radioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.radio_english) {
                setLocale("en");
            } else if (checkedId == R.id.radio_afrikaans) {
                setLocale("af");
            } else if (checkedId == R.id.radio_zulu) {
                setLocale("zu");
            }
        });

        //back button
        ImageButton backButton = findViewById(R.id.back_button);
        backButton.setOnClickListener(v -> {
            Intent intent = new Intent(LanguageActivity.this, SettingsActivity.class);
            startActivity(intent);
        });
    }
    private void setLocale(String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.setLocale(locale);
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
        SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("language", languageCode);
        editor.apply();
        Toast.makeText(this, "Language changed to " + (languageCode.equals("en") ? "English" : languageCode.equals("af") ? "Afrikaans" : "Zulu"), Toast.LENGTH_SHORT).show();
        // Restart the activity
        Intent intent = getIntent();
        finish();
        startActivity(intent);
    }
}
