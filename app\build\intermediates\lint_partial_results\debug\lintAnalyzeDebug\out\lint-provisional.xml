<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="conditional_incidents">

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1070"
                endOffset="1090"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="22"
            column="9"
            startOffset="1070"
            endLine="22"
            endColumn="29"
            endOffset="1090"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/attach_money.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/attach_money.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/back.xml"
            line="7"
            column="5"
            startOffset="237"
            endLine="7"
            endColumn="25"
            endOffset="257"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="This attribute is not supported in images generated from this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/back.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/back.xml"
            line="9"
            column="26"
            startOffset="301"
            endLine="9"
            endColumn="46"
            endOffset="321"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/credit_card.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/credit_card.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/expand_more.xml"
            line="7"
            column="19"
            startOffset="244"
            endLine="7"
            endColumn="46"
            endOffset="271"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/expand_more.xml"
            line="9"
            column="28"
            startOffset="313"
            endLine="9"
            endColumn="48"
            endOffset="333"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_close.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_close.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_icon.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_icon.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprint.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprint.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml"
            line="7"
            column="19"
            startOffset="244"
            endLine="7"
            endColumn="39"
            endOffset="264"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml"
            line="9"
            column="28"
            startOffset="306"
            endLine="9"
            endColumn="48"
            endOffset="326"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logout.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logout.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mail_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mail_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="7"
            column="5"
            startOffset="237"
            endLine="7"
            endColumn="25"
            endOffset="257"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="This attribute is not supported in images generated from this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="9"
            column="26"
            startOffset="301"
            endLine="9"
            endColumn="46"
            endOffset="321"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/notifications.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/notifications.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/person.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/person.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/shopping.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/shopping.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/star_24px.xml"
            line="7"
            column="19"
            startOffset="244"
            endLine="7"
            endColumn="43"
            endOffset="268"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/star_24px.xml"
            line="9"
            column="28"
            startOffset="310"
            endLine="9"
            endColumn="48"
            endOffset="330"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/warning_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/warning_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/work_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/work_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="fffffffffc000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="47"
            column="15"
            startOffset="2830"
            endLine="47"
            endColumn="54"
            endOffset="2869"/>
        <map>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <api-levels id="requiresApi"
                value="fffffffffc000000"/>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="fffffffffc000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="46"
            column="15"
            startOffset="2043"
            endLine="46"
            endColumn="54"
            endOffset="2082"/>
        <map>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <api-levels id="requiresApi"
                value="fffffffffc000000"/>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/MyFirebaseMessagingService.kt"
            line="84"
            column="9"
            startOffset="2896"
            endLine="84"
            endColumn="67"
            endOffset="2954"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationHelper.kt"
            line="81"
            column="13"
            startOffset="3648"
            endLine="81"
            endColumn="92"
            endOffset="3727"/>
    </incident>

    <incident
        id="GestureBackNavigation"
        severity="warning"
        message="If intercepting back events, this should be handled through the registration of callbacks on the window level; Please see https://developer.android.com/about/versions/13/features/predictive-back-gesture">
        <show-url
            description="Show https://developer.android.com/about/versions/13/features/predictive-back-gesture"
            url="https://developer.android.com/about/versions/13/features/predictive-back-gesture"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/SettingsActivity.kt"
            line="227"
            column="24"
            startOffset="9917"
            endLine="227"
            endColumn="45"
            endOffset="9938"/>
    </incident>

</incidents>
