package com.example.budgettracker;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class RetrofitClient {
    private static Retrofit retrofit = null;
    private static final String BASE_URL = "https://backend-budget-tracket-v2.vercel.app";

    public static Retrofit getClient() {
        if (retrofit == null) {
            // Initialize Gson instance
            Gson gson = new GsonBuilder()
                    .registerTypeAdapter(Record.class, (JsonDeserializer<Record>) (json, typeOfT, context) -> {
                        JsonObject jsonObject = json.getAsJsonObject();
                        if (json.getAsJsonObject().has("trans_month")) {
                            return new Gson().fromJson(json, Transaction.class);
                        } else if (json.getAsJsonObject().has("note")) {
                            return new Gson().fromJson(json, Expenses.class);
                        } else if (jsonObject.has("user_email") ) {
                            return new Gson().fromJson(json, User.class);
                        } else {
                            return new Gson().fromJson(json, Budgets.class);
                        }
                    })
                    .create();

            // Initialize Retrofit instance
            retrofit = new Retrofit.Builder()
                    .baseUrl(BASE_URL)
                    .addConverterFactory(GsonConverterFactory.create(gson))
                    .build();
        }
        return retrofit;
    }
}
