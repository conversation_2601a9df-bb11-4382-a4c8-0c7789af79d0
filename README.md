# Budget Hero 
<div align="center">
  <img src="assets/images/logos.png" alt="Budget Hero Logos" width="200">
</div>

A comprehensive personal budget tracking Android application that helps users manage their finances with smart features, multi-language support, and seamless offline functionality.

## 📱 Features

### Core Functionality
- **Expense Tracking**: Add and categorize expenses with detailed notes and dates
- **Budget Management**: Set monthly budgets and track spending against targets
- **Transaction History**: View comprehensive transaction history with filtering options
- **Goal Setting**: Set savings targets and track progress towards financial goals
- **Real-time Balance**: Monitor account balance and spending in real-time

### Smart Notifications
- **Budget Alerts**: Get notified when approaching budget limits (70% threshold)
- **Goal Achievement**: Celebrate when reaching savings milestones
- **Low Balance Warnings**: Alerts when balance drops below $100
- **Expense Confirmations**: Instant feedback when adding expenses

### Security & Authentication
- **Firebase Authentication**: Secure email/password login system
- **Biometric Login**: Fingerprint authentication for quick access
- **Session Management**: Secure session handling with automatic logout
- **Data Encryption**: Protected user data with Firebase security

### Multi-Language Support 🌍
The app supports three languages:
- **English** (<PERSON>fault)
- **Afrikaans** (af)
- **Zulu** (zu)

Language can be changed dynamically through the settings menu, with immediate UI updates.

### Offline Capabilities 📱
- **Local Database**: SQLite database for offline data storage
- **Smart Sync**: Automatic synchronization when internet connection is available
- **Offline Notifications**: Local notifications when working offline
- **Fallback Mode**: Seamless transition between online and offline modes
- **Data Persistence**: All data saved locally and synced when online

### Data Visualization
- **Time Period Filters**: View data by 1D, 1W, 1M, 3M, 1Y periods
- **Spending Analytics**: Visual representation of spending patterns
- **Category Breakdown**: Expense categorization (Food, Transport, Entertainment, Utilities, Other)
- **Progress Tracking**: Visual progress indicators for budgets and goals

## 🏗️ Architecture

### Technology Stack
- **Platform**: Android (API 24+)
- **Language**: Java
- **Database**: SQLite (local) + Firebase Firestore (cloud)
- **Authentication**: Firebase Auth with biometric support
- **UI Framework**: Android Views with Material Design
- **Networking**: Retrofit for API calls
- **Charts**: GraphView for data visualization

### Key Components
- **MainActivity**: Login and authentication
- **DashboardActivity**: Main overview with balance and recent transactions
- **ExpensesActivity**: Add and manage expenses
- **TransactionsActivity**: Budget management and transaction history
- **NotificationActivity**: Smart notifications and alerts
- **SettingsActivity**: App configuration and preferences
- **LanguageActivity**: Multi-language support

## 🚀 Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK API 24 or higher
- Firebase project setup
- Google Services configuration

### Installation
1. Clone the repository
2. Open in Android Studio
3. Add your `google-services.json` file to the `app/` directory
4. Build and run the application

### Firebase Setup
1. Create a Firebase project
2. Enable Authentication (Email/Password)
3. Set up Firestore database
4. Configure Firebase Cloud Messaging
5. Download and add `google-services.json`

## 📊 Database Schema

### Local SQLite Tables
- **expenses**: User expenses with categories and amounts
- **budget**: Monthly budget allocations
- **transactions**: Financial transaction summaries
- **user**: User profile information
- **categories**: Expense categories

### Firebase Collections
- **users**: User profiles and authentication data
- **expenses**: Synced expense records
- **transactions**: Monthly transaction summaries
- **notifications**: Push notification history

## 🔧 Configuration

### App Configuration
The app uses `AppConfig.java` to control Firebase integration:
```java
public static final boolean ENABLE_FIREBASE = true;
```

### Network Detection
Automatic detection of internet connectivity for smart offline/online mode switching.

## 🌟 Key Features Implementation

### 1. Offline-First Architecture
- All data operations work offline
- Automatic sync when connection is restored
- Conflict resolution for data synchronization
- Local notifications for offline actions

### 2. Multi-Language Support
- Dynamic language switching without app restart
- Localized strings for English, Afrikaans, and Zulu
- Persistent language preferences
- RTL support ready

### 3. Smart Notifications
- Real-time budget monitoring
- Goal achievement celebrations
- Low balance alerts
- Expense tracking confirmations

### 4. Biometric Security
- Fingerprint authentication
- Secure session management
- Automatic logout on security events
- Encrypted local data storage

## 📱 Screenshots & App Assets

### App Icon & Branding
<div align="center">
  <img src="assets/images/ic_launcher.webp" alt="App Icon" width="80" height="80">
  <img src="assets/images/ic_launcher_round.webp" alt="App Icon Round" width="80" height="80">
  <img src="assets/images/logo_hdpi.png" alt="Logo HD" width="80" height="80">
</div>



### App Screenshots
*Note: Add actual screenshots of your app here showing:*
- Login screen with biometric authentication
- Dashboard with balance and spending overview
- Expense tracking interface
- Multi-language settings
- Notification center
- Budget management screen

### Final Image Assets
The app includes a comprehensive set of visual assets:

#### **App Icons & Logos**
- Main app icon (adaptive icon with background and foreground)
- Round app icon variant
- Primary logo (logo.png)
- Logo variations (logos.png)

#### **UI Icons & Graphics**
- **Financial Icons**: Money, credit card, transaction icons
- **Navigation Icons**: Home, settings, notifications, search
- **Action Icons**: Add, back, expand, star, warning
- **Security Icons**: Fingerprint, eye (password visibility)
- **Category Icons**: Shopping, work, monitoring

#### **Design Elements**
- **Backgrounds**: Gradient backgrounds, card backgrounds, button styles
- **Interactive Elements**: Ripple effects, chip backgrounds, spinner designs
- **Notification Styles**: Success, warning, and info notification backgrounds


## 🎯 Design System

### Color Palette
The app uses a modern, finance-focused color scheme with:
- **Primary Colors**: Professional blues and greens for trust and growth
- **Accent Colors**: Vibrant highlights for important actions
- **Status Colors**: Red for warnings, green for success, orange for alerts
- **Neutral Colors**: Grays for backgrounds and secondary text

### Typography
- **Headers**: Bold, clear typography for important information
- **Body Text**: Readable fonts optimized for financial data
- **Numbers**: Monospace fonts for currency and amounts

### Icon Design
- **Consistent Style**: Material Design principles throughout
- **Scalable Vectors**: XML vector drawables for crisp display
- **Accessibility**: High contrast and clear visual hierarchy
- **Contextual**: Icons that clearly represent their function

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔮 Future Enhancements

- **Export Functionality**: PDF reports and CSV exports
- **Advanced Analytics**: Spending trends and predictions
- **Multiple Accounts**: Support for multiple bank accounts
- **Receipt Scanning**: OCR for automatic expense entry
- **Budgeting Templates**: Pre-defined budget categories
- **Social Features**: Shared budgets and family accounts

## 📞 Support

For support and questions, please contact [<EMAIL>]

---

**Budget Hero** - Your personal finance companion 💰✨
