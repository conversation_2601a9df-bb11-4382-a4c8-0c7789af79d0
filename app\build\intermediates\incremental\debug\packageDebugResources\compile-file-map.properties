#Tue Jun 10 08:31:35 EAT 2025
com.example.budgettracker.app-main-6\:/anim/item_animation_fall_down.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\item_animation_fall_down.xml
com.example.budgettracker.app-main-6\:/drawable/attach_money.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\attach_money.xml
com.example.budgettracker.app-main-6\:/drawable/back.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\back.xml
com.example.budgettracker.app-main-6\:/drawable/backg_button.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\backg_button.xml
com.example.budgettracker.app-main-6\:/drawable/button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_background.xml
com.example.budgettracker.app-main-6\:/drawable/button_danger_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_danger_background.xml
com.example.budgettracker.app-main-6\:/drawable/button_ripple.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_ripple.xml
com.example.budgettracker.app-main-6\:/drawable/card_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background.xml
com.example.budgettracker.app-main-6\:/drawable/chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chip_background.xml
com.example.budgettracker.app-main-6\:/drawable/circle_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background.xml
com.example.budgettracker.app-main-6\:/drawable/credit_card.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\credit_card.xml
com.example.budgettracker.app-main-6\:/drawable/edittext_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\edittext_background.xml
com.example.budgettracker.app-main-6\:/drawable/expand_more.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\expand_more.xml
com.example.budgettracker.app-main-6\:/drawable/eye_close.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\eye_close.xml
com.example.budgettracker.app-main-6\:/drawable/eye_icon.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\eye_icon.xml
com.example.budgettracker.app-main-6\:/drawable/fab_label_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\fab_label_background.xml
com.example.budgettracker.app-main-6\:/drawable/fingerprint.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\fingerprint.xml
com.example.budgettracker.app-main-6\:/drawable/gradient_primary_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_primary_background.xml
com.example.budgettracker.app-main-6\:/drawable/home.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\home.xml
com.example.budgettracker.app-main-6\:/drawable/ic_add.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.example.budgettracker.app-main-6\:/drawable/ic_launcher_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.budgettracker.app-main-6\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.example.budgettracker.app-main-6\:/drawable/icon_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\icon_button_background.xml
com.example.budgettracker.app-main-6\:/drawable/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo.png
com.example.budgettracker.app-main-6\:/drawable/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logos.png
com.example.budgettracker.app-main-6\:/drawable/logout.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logout.xml
com.example.budgettracker.app-main-6\:/drawable/mail_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\mail_24px.xml
com.example.budgettracker.app-main-6\:/drawable/medium_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\medium_button_background.xml
com.example.budgettracker.app-main-6\:/drawable/modern_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\modern_button_background.xml
com.example.budgettracker.app-main-6\:/drawable/monitoring.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\monitoring.xml
com.example.budgettracker.app-main-6\:/drawable/nav_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\nav_button_background.xml
com.example.budgettracker.app-main-6\:/drawable/notification_info_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\notification_info_background.xml
com.example.budgettracker.app-main-6\:/drawable/notification_success_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\notification_success_background.xml
com.example.budgettracker.app-main-6\:/drawable/notification_warning_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\notification_warning_background.xml
com.example.budgettracker.app-main-6\:/drawable/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\notifications.xml
com.example.budgettracker.app-main-6\:/drawable/person.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\person.xml
com.example.budgettracker.app-main-6\:/drawable/search.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search.xml
com.example.budgettracker.app-main-6\:/drawable/settings_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\settings_24px.xml
com.example.budgettracker.app-main-6\:/drawable/shopping.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\shopping.xml
com.example.budgettracker.app-main-6\:/drawable/small_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\small_button_background.xml
com.example.budgettracker.app-main-6\:/drawable/small_chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\small_chip_background.xml
com.example.budgettracker.app-main-6\:/drawable/small_chip_selected_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\small_chip_selected_background.xml
com.example.budgettracker.app-main-6\:/drawable/spinner_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_background.xml
com.example.budgettracker.app-main-6\:/drawable/spinner_normal.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_normal.xml
com.example.budgettracker.app-main-6\:/drawable/spinner_press.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_press.xml
com.example.budgettracker.app-main-6\:/drawable/spinner_select.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_select.xml
com.example.budgettracker.app-main-6\:/drawable/star_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\star_24px.xml
com.example.budgettracker.app-main-6\:/drawable/transaction_icon_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\transaction_icon_background.xml
com.example.budgettracker.app-main-6\:/drawable/transaction_type_chip.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\transaction_type_chip.xml
com.example.budgettracker.app-main-6\:/drawable/warning_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\warning_24px.xml
com.example.budgettracker.app-main-6\:/drawable/work_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\work_24px.xml
com.example.budgettracker.app-main-6\:/menu/bottom_navigation_menu.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_navigation_menu.xml
com.example.budgettracker.app-main-6\:/menu/menu_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_main.xml
com.example.budgettracker.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.budgettracker.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.budgettracker.app-main-6\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-6\:/mipmap-hdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\logo.png
com.example.budgettracker.app-main-6\:/mipmap-hdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\logos.png
com.example.budgettracker.app-main-6\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-6\:/mipmap-mdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\logo.png
com.example.budgettracker.app-main-6\:/mipmap-mdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\logos.png
com.example.budgettracker.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-6\:/mipmap-xhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\logo.png
com.example.budgettracker.app-main-6\:/mipmap-xhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\logos.png
com.example.budgettracker.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-6\:/mipmap-xxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\logo.png
com.example.budgettracker.app-main-6\:/mipmap-xxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\logos.png
com.example.budgettracker.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-6\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\logo.png
com.example.budgettracker.app-main-6\:/mipmap-xxxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\logos.png
com.example.budgettracker.app-main-6\:/navigation/nav_graph.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\navigation\\nav_graph.xml
com.example.budgettracker.app-main-6\:/xml/backup_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.budgettracker.app-main-6\:/xml/data_extraction_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/activity_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/addexpense.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\addexpense.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/addtransaction.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\addtransaction.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/base.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\base.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/content_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\content_main.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/dashboard.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dashboard.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/item_notification.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_notification.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/language.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\language.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/list_bug.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\list_bug.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/list_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\list_item.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/login.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\login.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\notifications.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/register.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\register.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/settings.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\settings.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/spinner.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner.xml
com.example.budgettracker.app-packageDebugResources-3\:/layout/spinner_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_item.xml
