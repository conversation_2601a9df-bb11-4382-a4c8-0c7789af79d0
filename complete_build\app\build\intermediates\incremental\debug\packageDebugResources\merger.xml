<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res"><file name="item_animation_fall_down" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\anim\item_animation_fall_down.xml" qualifiers="" type="anim"/><file name="attach_money" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\attach_money.xml" qualifiers="" type="drawable"/><file name="back" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\back.xml" qualifiers="" type="drawable"/><file name="backg_button" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\backg_button.xml" qualifiers="" type="drawable"/><file name="button_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_danger_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\button_danger_background.xml" qualifiers="" type="drawable"/><file name="button_ripple" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\button_ripple.xml" qualifiers="" type="drawable"/><file name="card_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="chip_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\chip_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="credit_card" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\credit_card.xml" qualifiers="" type="drawable"/><file name="edittext_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\edittext_background.xml" qualifiers="" type="drawable"/><file name="expand_more" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\expand_more.xml" qualifiers="" type="drawable"/><file name="eye_close" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\eye_close.xml" qualifiers="" type="drawable"/><file name="eye_icon" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\eye_icon.xml" qualifiers="" type="drawable"/><file name="fab_label_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\fab_label_background.xml" qualifiers="" type="drawable"/><file name="fingerprint" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\fingerprint.xml" qualifiers="" type="drawable"/><file name="gradient_primary_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\gradient_primary_background.xml" qualifiers="" type="drawable"/><file name="home" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\home.xml" qualifiers="" type="drawable"/><file name="icon_button_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\icon_button_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="logo" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\logo.png" qualifiers="" type="drawable"/><file name="logos" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\logos.png" qualifiers="" type="drawable"/><file name="logout" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\logout.xml" qualifiers="" type="drawable"/><file name="mail_24px" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\mail_24px.xml" qualifiers="" type="drawable"/><file name="medium_button_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\medium_button_background.xml" qualifiers="" type="drawable"/><file name="modern_button_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\modern_button_background.xml" qualifiers="" type="drawable"/><file name="monitoring" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\monitoring.xml" qualifiers="" type="drawable"/><file name="nav_button_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\nav_button_background.xml" qualifiers="" type="drawable"/><file name="notifications" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\notifications.xml" qualifiers="" type="drawable"/><file name="notification_info_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\notification_info_background.xml" qualifiers="" type="drawable"/><file name="notification_success_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\notification_success_background.xml" qualifiers="" type="drawable"/><file name="notification_warning_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\notification_warning_background.xml" qualifiers="" type="drawable"/><file name="person" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\person.xml" qualifiers="" type="drawable"/><file name="search" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\search.xml" qualifiers="" type="drawable"/><file name="settings_24px" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\settings_24px.xml" qualifiers="" type="drawable"/><file name="shopping" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\shopping.xml" qualifiers="" type="drawable"/><file name="small_button_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\small_button_background.xml" qualifiers="" type="drawable"/><file name="small_chip_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\small_chip_background.xml" qualifiers="" type="drawable"/><file name="small_chip_selected_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\small_chip_selected_background.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="spinner_normal" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\spinner_normal.xml" qualifiers="" type="drawable"/><file name="spinner_press" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\spinner_press.xml" qualifiers="" type="drawable"/><file name="spinner_select" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\spinner_select.xml" qualifiers="" type="drawable"/><file name="star_24px" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\star_24px.xml" qualifiers="" type="drawable"/><file name="transaction_icon_background" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\transaction_icon_background.xml" qualifiers="" type="drawable"/><file name="transaction_type_chip" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\transaction_type_chip.xml" qualifiers="" type="drawable"/><file name="warning_24px" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\warning_24px.xml" qualifiers="" type="drawable"/><file name="work_24px" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\work_24px.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="addexpense" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml" qualifiers="" type="layout"/><file name="addtransaction" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml" qualifiers="" type="layout"/><file name="base" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml" qualifiers="" type="layout"/><file name="content_main" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\content_main.xml" qualifiers="" type="layout"/><file name="dashboard" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml" qualifiers="" type="layout"/><file name="item_notification" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\item_notification.xml" qualifiers="" type="layout"/><file name="language" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\language.xml" qualifiers="" type="layout"/><file name="list_bug" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_bug.xml" qualifiers="" type="layout"/><file name="list_item" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_item.xml" qualifiers="" type="layout"/><file name="login" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\login.xml" qualifiers="" type="layout"/><file name="notifications" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\notifications.xml" qualifiers="" type="layout"/><file name="register" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml" qualifiers="" type="layout"/><file name="settings" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml" qualifiers="" type="layout"/><file name="spinner" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\spinner.xml" qualifiers="" type="layout"/><file name="spinner_item" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\spinner_item.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="menu_main" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\logo.png" qualifiers="hdpi-v4" type="mipmap"/><file name="logos" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\logos.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-mdpi\logo.png" qualifiers="mdpi-v4" type="mipmap"/><file name="logos" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-mdpi\logos.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xhdpi\logo.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="logos" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xhdpi\logos.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxhdpi\logo.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="logos" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxhdpi\logos.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="logo" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\logo.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="logos" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\logos.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#ffffff</color><color name="primary">#0061A4</color><color name="steelblue">#4682B4</color><color name="onPrimary">#ffffff</color><color name="primary_blue">#0061A4</color><color name="text_color">#1A1C1E</color><color name="border_color">#C3C7CF</color><color name="radio_dot_color">#1A1C1E</color><color name="back_ground">#b0463a</color><color name="md_theme_light_primary">#0061A4</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#D1E4FF</color><color name="md_theme_light_onPrimaryContainer">#001D36</color><color name="md_theme_light_secondary">#535F70</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#D7E3F7</color><color name="md_theme_light_onSecondaryContainer">#101C2B</color><color name="md_theme_light_tertiary">#6B5778</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#F2DAFF</color><color name="md_theme_light_onTertiaryContainer">#251431</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_background">#FCFCFF</color><color name="md_theme_light_onBackground">#1A1C1E</color><color name="md_theme_light_surface">#FCFCFF</color><color name="md_theme_light_onSurface">#1A1C1E</color><color name="md_theme_light_surfaceVariant">#DFE2EB</color><color name="md_theme_light_onSurfaceVariant">#43474E</color><color name="md_theme_light_outline">#73777F</color><color name="md_theme_light_outlineVariant">#C3C7CF</color><color name="md_theme_light_scrim">#000000</color><color name="md_theme_light_inverseSurface">#2F3033</color><color name="md_theme_light_inverseOnSurface">#F1F0F4</color><color name="md_theme_light_inversePrimary">#9ECAFF</color><color name="scrim_overlay">#80000000</color><color name="primary_light">#E3F2FD</color><color name="primary_variant">#1976D2</color></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="fab_margin">16dp</dimen></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\ids.xml" qualifiers=""><item name="closeIcon" type="id"/><item name="profile_section" type="id"/><item name="header_section" type="id"/></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">BudgetTracker</string><string name="action_settings">Settings</string><string name="first_fragment_label">First Fragment</string><string name="second_fragment_label">Second Fragment</string><string name="next">Next</string><string name="previous">Previous</string><string name="search_description">Search</string><string name="chart_description">Chart</string><string name="home_description">Home</string><string name="expense_description">Expense</string><string name="transaction_description">Transactions</string><string name="profile_description">Profile</string><string name="add_a_new_expense">Add a new expense</string><string name="budgethero">BudgetHero</string><string name="todo">TODO</string><string name="your_personal_budget_tracker">Your personal budget tracker</string><string name="enter_email">Enter email</string><string name="enter_password">Enter password</string><string name="log_in">Log in</string><string name="or_log_in_with">Or log in with</string><string name="touch_id">Touch ID</string><string name="today">Today</string><string name="add_a_note">Add a note</string><string name="select_category">Select category</string><string name="_0_00">$0</string><string name="save">Save</string><string name="user_name">Username</string><string name="account_balance">Account Balance</string><string name="_5_943">$5,943</string><string name="last_30_days">This month</string><string name="_12">+12%</string><string name="_1d">1D</string><string name="_1w">1W</string><string name="_1m">1M</string><string name="_3m">3M</string><string name="_1y">1Y</string><string name="meta">META</string><string name="meta_platforms">Meta Platforms</string><string name="warning_icon">Warning Icon</string><string name="you_ve_spent_70_of_your_budget_for_the_month">You’ve spent 70% of your budget for the month</string><string name="budget_alert">Budget alert</string><string name="currency_icon">Currency Icon</string><string name="transaction">Transaction</string><string name="you_ve_received_25_from_sarah">You’ve received $25 from Sarah</string><string name="clear_all">Clear all</string><string name="fingerprint_icon_description">Fingerprint icon description</string><string name="logo_name">BudgetHero</string><string name="back_description">Back</string><string name="heading_list_dash">Expenditures</string><string name="type">Type</string><string name="budget_info">Your budget for this month is $%d</string><string name="add">Set</string><string name="add_money">Add money</string><string name="settings">settings</string><string name="language_settings">Language Settings</string><string name="clear_user_data">Clear User Data</string><string name="user_profile">user profile</string><string name="afrikaans">Afrikaans</string><string name="english">English</string><string name="language">Language</string><string name="notifications">Notifications</string><string name="zulu">Zulu</string><string name="name_bug">Name</string><string name="bug_amount">Amount</string><string name="exp_amount">Amount</string><string name="ext_name">Name</string><string name="exp_date">exp_date</string><string name="date_x">Date</string><string name="main_cash">Main_cash</string><string name="_0">$0</string><string name="month">This month</string><string name="pick_date">Pick Date</string><string name="budget">Budget</string><string name="target">Target</string><string name="balance">Balance</string><string name="set_new_target">Month Target</string><string name="set">Set</string><string name="sign_up">Sign Up</string><string name="add_expense">Add Expense</string><string name="view_all">View All</string><string-array name="categories_array">
        <item>Food</item>
        <item>Transport</item>
        <item>Entertainment</item>
        <item>Utilities</item>
        <item>Other</item>
    </string-array></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        
        <item name="colorPrimary">@color/material_dynamic_primary10</item>
        <item name="colorPrimaryVariant">@color/material_dynamic_primary20</item>
        <item name="colorOnPrimary">@color/material_dynamic_secondary10</item>
        <item name="colorSecondary">@color/material_dynamic_secondary20</item>
        <item name="colorSecondaryVariant">@color/material_dynamic_secondary30</item>
        <item name="colorOnSecondary">@color/material_dynamic_secondary50</item>
    </style><style name="App.Widget.BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">?attr/colorPrimaryContainer</item>
    </style><style name="SmallButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:minWidth">80dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/small_button_background</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:elevation">2dp</item>
    </style><style name="MediumButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/medium_button_background</item>
        <item name="android:elevation">3dp</item>
    </style><style name="IconButton">
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:background">@drawable/icon_button_background</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:elevation">2dp</item>
        <item name="android:tint">?attr/colorOnSurface</item>
    </style></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.BudgetTracker" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        
        
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        
        
        <item name="android:statusBarColor">@color/md_theme_light_surface</item>
        <item name="android:windowLightStatusBar">true</item>
        
        
        <item name="android:navigationBarColor">@color/md_theme_light_surface</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values-af\strings.xml" qualifiers="af"><string name="app_name">Begrotingsopvolger</string><string name="action_settings">Instellings</string><string name="first_fragment_label">Eerste Fragment</string><string name="second_fragment_label">Tweede Fragment</string><string name="next">Volgende</string><string name="previous">Vorige</string><string name="search_description">Soek</string><string name="chart_description">Grafiek</string><string name="home_description">Tuisskerm</string><string name="expense_description">Uitgawe</string><string name="transaction_description">Transaksies</string><string name="profile_description">Profiel</string><string name="add_a_new_expense">Voeg n nuwe uitgawe by</string><string name="budgethero">BudgetHero</string><string name="todo">TODO</string><string name="your_personal_budget_tracker">Jou persoonlike begrotingsopvolger</string><string name="enter_email">Voer e-pos in</string><string name="enter_password">Voer wagwoord in</string><string name="log_in">Teken in</string><string name="or_log_in_with">Of teken in met</string><string name="touch_id">Vingerafdruk ID</string><string name="today">Vandag</string><string name="add_a_note">Voeg n nota by</string><string name="select_category">Kies kategorie</string><string name="_0_00">$0.00</string><string name="save">Stoor</string><string name="user_name">Gebruikersnaam</string><string name="account_balance">Rekeningbalans</string><string name="_5_943">$5,943</string><string name="last_30_days">Laaste 30 Dae</string><string name="_12">+12%</string><string name="_1d">1D</string><string name="_1w">1W</string><string name="_1m">1M</string><string name="_3m">3M</string><string name="_1y">1J</string><string name="meta">META</string><string name="meta_platforms">Meta Platforms</string><string name="warning_icon">Waarskuwingsikoon</string><string name="you_ve_spent_70_of_your_budget_for_the_month">Jy het 70% van jou begroting vir die maand bestee</string><string name="budget_alert">Begrotingswaarskuwing</string><string name="currency_icon">Valutakoen</string><string name="transaction">Transaksie</string><string name="you_ve_received_25_from_sarah">Jy het $25 van Sarah ontvang</string><string name="clear_all">Verwyder alles</string><string name="fingerprint_icon_description">Vingerafdruk ikoon beskrywing</string><string name="logo_name">BudgetHero</string><string name="back_description">Terug</string><string name="heading_list_dash">Bestedings</string><string name="type">Tipe</string><string name="add">Voeg by</string><string name="add_money">Voeg geld by</string><string name="settings">Instellings</string><string name="language_settings">Taalinstellings</string><string name="clear_user_data">Verwyder gebruikersdata</string><string name="user_profile">gebruikers profiel</string><string name="afrikaans">Afrikaans</string><string name="english">Engels</string><string name="language">Taal</string><string name="notifications">Kennisgewings</string><string-array name="categories_array">
        <item>Kos</item>
        <item>Vervoer</item>
        <item>Vermaak</item>
        <item>Nutsgoedere</item>
        <item>Ander</item>
    </string-array></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">BudgetTracker</string><string name="action_settings">Settings</string><string name="first_fragment_label">First Fragment</string><string name="second_fragment_label">Second Fragment</string><string name="next">Next</string><string name="previous">Previous</string><string name="search_description">Search</string><string name="chart_description">Chart</string><string name="home_description">Home</string><string name="expense_description">Expense</string><string name="transaction_description">Transactions</string><string name="profile_description">Profile</string><string name="add_a_new_expense">Add a new expense</string><string name="budgethero">BudgetHero</string><string name="todo">TODO</string><string name="your_personal_budget_tracker">Your personal budget tracker</string><string name="enter_email">Enter email</string><string name="enter_password">Enter password</string><string name="log_in">Log in</string><string name="or_log_in_with">Or log in with</string><string name="touch_id">Touch ID</string><string name="today">Today</string><string name="add_a_note">Add a note</string><string name="select_category">Select category</string><string name="_0_00">$0.00</string><string name="save">Save</string><string name="user_name">Username</string><string name="account_balance">Account Balance</string><string name="_5_943">$5,943</string><string name="last_30_days">Last 30 Days</string><string name="_12">+12%</string><string name="_1d">1D</string><string name="_1w">1W</string><string name="_1m">1M</string><string name="_3m">3M</string><string name="_1y">1Y</string><string name="meta">META</string><string name="meta_platforms">Meta Platforms</string><string name="warning_icon">Warning Icon</string><string name="you_ve_spent_70_of_your_budget_for_the_month">You’ve spent 70% of your budget for the month</string><string name="budget_alert">Budget alert</string><string name="currency_icon">Currency Icon</string><string name="transaction">Transaction</string><string name="you_ve_received_25_from_sarah">You’ve received $25 from Sarah</string><string name="clear_all">Clear all</string><string name="fingerprint_icon_description">Fingerprint icon description</string><string name="logo_name">BudgetHero</string><string name="back_description">Back</string><string name="heading_list_dash">Expenditures</string><string name="type">Type</string><string name="add">Add</string><string name="add_money">Add money</string><string name="settings">settings</string><string name="language_settings">Language Settings</string><string name="clear_user_data">Clear User Data</string><string name="user_profile">user profile</string><string name="afrikaans">Afrikaans</string><string name="english">English</string><string name="language">Language</string><string name="notifications">Notifications</string><string name="zulu">Zulu</string><string name="ext_name">Name</string><string-array name="categories_array">
        <item>Food</item>
        <item>Transport</item>
        <item>Entertainment</item>
        <item>Utilities</item>
        <item>Other</item>
    </string-array></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="fab_margin">48dp</dimen></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.BudgetTracker" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">#9ECAFF</item>
        <item name="colorOnPrimary">#003258</item>
        <item name="colorPrimaryContainer">#00497D</item>
        <item name="colorOnPrimaryContainer">#D1E4FF</item>

        
        <item name="colorSecondary">#BCC7DB</item>
        <item name="colorOnSecondary">#263141</item>
        <item name="colorSecondaryContainer">#3C4858</item>
        <item name="colorOnSecondaryContainer">#D7E3F7</item>

        
        <item name="colorTertiary">#D6BEE4</item>
        <item name="colorOnTertiary">#3B2948</item>
        <item name="colorTertiaryContainer">#523F5F</item>
        <item name="colorOnTertiaryContainer">#F2DAFF</item>

        
        <item name="colorError">#FFB4AB</item>
        <item name="colorOnError">#690005</item>
        <item name="colorErrorContainer">#93000A</item>
        <item name="colorOnErrorContainer">#FFDAD6</item>

        
        <item name="colorSurface">#101418</item>
        <item name="colorOnSurface">#E2E2E9</item>
        <item name="colorSurfaceVariant">#43474E</item>
        <item name="colorOnSurfaceVariant">#C3C7CF</item>
        <item name="colorSurfaceInverse">#E2E2E9</item>
        <item name="colorOnSurfaceInverse">#2F3033</item>

        
        <item name="android:colorBackground">#101418</item>
        <item name="colorOnBackground">#E2E2E9</item>

        
        <item name="android:statusBarColor">#101418</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">#101418</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values-w1240dp\dimens.xml" qualifiers="w1240dp-v13"><dimen name="fab_margin">200dp</dimen></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values-w600dp\dimens.xml" qualifiers="w600dp-v13"><dimen name="fab_margin">48dp</dimen></file><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values-zu\strings.xml" qualifiers="zu"><string name="app_name">I-BudgetTracker</string><string name="action_settings">Izilungiselelo</string><string name="first_fragment_label">Isigaba Sokuqala</string><string name="second_fragment_label">Isigaba Sesibili</string><string name="next">Olandelayo</string><string name="previous">Okwedlule</string><string name="search_description">Sesha</string><string name="chart_description">Ishadi</string><string name="home_description">Ikhaya</string><string name="expense_description">Izindleko</string><string name="transaction_description">Izinkokhelo</string><string name="profile_description">Iphrofayela</string><string name="add_a_new_expense">Engeza izindleko ezintsha</string><string name="budgethero">BudgetHero</string><string name="todo">TODO</string><string name="your_personal_budget_tracker">Ibhajethi yakho yomuntu siqu</string><string name="enter_email">Faka i-imeyili</string><string name="enter_password">Faka iphasiwedi</string><string name="log_in">Ngena</string><string name="or_log_in_with">Noma ngena usebenzisa</string><string name="touch_id">ID Yengxenye Yesithupha</string><string name="today">Namuhla</string><string name="add_a_note">Engeza amanothi</string><string name="select_category">Khetha isigaba</string><string name="_0_00">$0.00</string><string name="save">Gcina</string><string name="user_name">Igama Lomsebenzisi</string><string name="account_balance">Isisindo Sekhadi</string><string name="_5_943">$5,943</string><string name="last_30_days">Izinsuku Eziyi-30 Ezidlule</string><string name="_12">+12%</string><string name="_1d">1D</string><string name="_1w">1V</string><string name="_1m">1M</string><string name="_3m">3M</string><string name="_1y">1Y</string><string name="meta">META</string><string name="meta_platforms">Meta Platforms</string><string name="warning_icon">Isithonjana Sesixwayiso</string><string name="you_ve_spent_70_of_your_budget_for_the_month">Usebenzise u-70% wesabelomali sakho senyanga</string><string name="budget_alert">Isixwayiso Sebhajethi</string><string name="currency_icon">Isithonjana Sezimali</string><string name="transaction">Ukukhokha</string><string name="you_ve_received_25_from_sarah">Uthole $25 kuSarah</string><string name="clear_all">Sula konke</string><string name="fingerprint_icon_description">Incazelo yesithonjana sezinhlamvu zomzimba</string><string name="logo_name">BudgetHero</string><string name="back_description">Emuva</string><string name="heading_list_dash">Izindleko</string><string name="type">Uhlobo</string><string name="add">Engeza</string><string name="add_money">Engeza imali</string><string name="settings">Izilungiselelo</string><string name="language_settings">Izilungiselelo Zolimi</string><string name="clear_user_data">Sula Idatha Yomsebenzisi</string><string name="user_profile">Iphrofayela yomsebenzisi</string><string name="afrikaans">Afrikaans</string><string name="english">IsiNgisi</string><string name="language">Ulimi</string><string name="notifications">Izaziso</string><string-array name="categories_array">
        <item>Ukudla</item>
        <item>Ukuthutha</item>
        <item>Ukuzijabulisa</item>
        <item>Izinsiza</item>
        <item>Okunye</item>
    </string-array></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">317792694031</string><string name="google_api_key" translatable="false">AIzaSyDt8sOPacepJywe_oL0ZhAS09AHOOkuMgs</string><string name="google_app_id" translatable="false">1:317792694031:android:9ae9916016b339df95987a</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyDt8sOPacepJywe_oL0ZhAS09AHOOkuMgs</string><string name="google_storage_bucket" translatable="false">budgettracker-6abe4.appspot.com</string><string name="project_id" translatable="false">budgettracker-6abe4</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>