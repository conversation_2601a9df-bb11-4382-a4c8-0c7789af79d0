<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="addtransaction" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\addtransaction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/addtransaction_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="322" endOffset="16"/></Target><Target id="@+id/footer" tag="layout/addtransaction_0" include="base"><Expressions/><location startLine="302" startOffset="4" endLine="307" endOffset="49"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="310" startOffset="4" endLine="320" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="318" startOffset="8" endLine="318" endOffset="43"/></Target><Target id="@+id/header_layout" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="41" endOffset="18"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="49"/></Target><Target id="@+id/budget_info" view="TextView"><Expressions/><location startLine="82" startOffset="20" endLine="90" endOffset="57"/></Target><Target id="@+id/editText5" view="EditText"><Expressions/><location startLine="142" startOffset="28" endLine="153" endOffset="57"/></Target><Target id="@+id/editText4" view="EditText"><Expressions/><location startLine="191" startOffset="28" endLine="201" endOffset="58"/></Target><Target id="@+id/save_button" view="Button"><Expressions/><location startLine="208" startOffset="20" endLine="211" endOffset="52"/></Target><Target id="@+id/empty_transactions_message" view="TextView"><Expressions/><location startLine="271" startOffset="20" endLine="284" endOffset="57"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="287" startOffset="20" endLine="293" endOffset="53"/></Target></Targets></Layout>