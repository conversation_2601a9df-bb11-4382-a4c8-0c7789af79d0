<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="settings" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/settings_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="375" endOffset="16"/></Target><Target id="@+id/footer" tag="layout/settings_0" include="base"><Expressions/><location startLine="355" startOffset="4" endLine="360" endOffset="49"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="363" startOffset="4" endLine="373" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="371" startOffset="8" endLine="371" endOffset="43"/></Target><Target id="@+id/header_layout" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="42" endOffset="18"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="49"/></Target><Target id="@+id/title" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="40" endOffset="52"/></Target><Target id="@+id/editText2" view="EditText"><Expressions/><location startLine="104" startOffset="28" endLine="114" endOffset="57"/></Target><Target id="@+id/editText4" view="EditText"><Expressions/><location startLine="142" startOffset="28" endLine="152" endOffset="57"/></Target><Target id="@+id/title_date" view="TextView"><Expressions/><location startLine="180" startOffset="28" endLine="188" endOffset="57"/></Target><Target id="@+id/date_picker_button" view="Button"><Expressions/><location startLine="192" startOffset="24" endLine="196" endOffset="54"/></Target><Target id="@+id/save_button" view="Button"><Expressions/><location startLine="201" startOffset="20" endLine="204" endOffset="54"/></Target><Target id="@+id/buttonLanguageSettings" view="Button"><Expressions/><location startLine="234" startOffset="20" endLine="238" endOffset="66"/></Target><Target id="@+id/buttonClearUserData" view="Button"><Expressions/><location startLine="240" startOffset="20" endLine="244" endOffset="64"/></Target><Target id="@+id/trans_balance" view="TextView"><Expressions/><location startLine="288" startOffset="24" endLine="295" endOffset="54"/></Target><Target id="@+id/trans_target" view="TextView"><Expressions/><location startLine="313" startOffset="24" endLine="320" endOffset="54"/></Target><Target id="@+id/trans_budget" view="TextView"><Expressions/><location startLine="337" startOffset="24" endLine="344" endOffset="54"/></Target></Targets></Layout>