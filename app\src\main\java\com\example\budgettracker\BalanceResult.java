package com.example.budgettracker;

public class BalanceResult {
    private float totalBudget;
    private float balance;
    private float transTarget; // Note the consistent naming convention for the field

    public BalanceResult(float totalBudget, float balance, float transTarget) {
        this.totalBudget = totalBudget;
        this.balance = balance;
        this.transTarget = transTarget;
    }

    // Getter for totalBudget
    public float getTotalBudget() {
        return totalBudget;
    }

    // Setter for totalBudget
    public void setTotalBudget(float totalBudget) {
        this.totalBudget = totalBudget;
    }

    // Getter for balance
    public float getBalance() {
        return balance;
    }

    // Setter for balance
    public void setBalance(float balance) {
        this.balance = balance;
    }

    // Getter for transTarget
    public float getTransTarget() {
        return transTarget;
    }

    // Setter for transTarget
    public void setTransTarget(float transTarget) {
        this.transTarget = transTarget;
    }
}
