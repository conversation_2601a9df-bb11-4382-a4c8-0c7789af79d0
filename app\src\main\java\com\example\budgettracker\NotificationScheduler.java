package com.example.budgettracker;

import android.content.Context;
import android.util.Log;

import androidx.work.Constraints;
import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.NetworkType;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;

import java.util.concurrent.TimeUnit;

public class NotificationScheduler {
    private static final String TAG = "NotificationScheduler";
    private static final String NOTIFICATION_WORK_NAME = "budget_notification_check";
    
    public static void schedulePeriodicNotificationChecks(Context context) {
        // Create constraints for the work
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED) // Require internet connection
                .setRequiresBatteryNotLow(true) // Don't run when battery is low
                .build();

        // Create periodic work request (runs every 6 hours)
        PeriodicWorkRequest notificationWorkRequest = new PeriodicWorkRequest.Builder(
                NotificationWorker.class,
                6, TimeUnit.HOURS) // Check every 6 hours
                .setConstraints(constraints)
                .addTag("notification_check")
                .build();

        // Schedule the work
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                NOTIFICATION_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP, // Keep existing work if already scheduled
                notificationWorkRequest
        );

        Log.d(TAG, "Periodic notification checks scheduled");
    }

    public static void cancelPeriodicNotificationChecks(Context context) {
        WorkManager.getInstance(context).cancelUniqueWork(NOTIFICATION_WORK_NAME);
        Log.d(TAG, "Periodic notification checks cancelled");
    }

    public static void scheduleImmediateNotificationCheck(Context context) {
        // Create constraints for immediate work
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();

        // Create one-time work request for immediate execution
        androidx.work.OneTimeWorkRequest immediateWorkRequest = 
                new androidx.work.OneTimeWorkRequest.Builder(NotificationWorker.class)
                .setConstraints(constraints)
                .addTag("immediate_notification_check")
                .build();

        WorkManager.getInstance(context).enqueue(immediateWorkRequest);
        Log.d(TAG, "Immediate notification check scheduled");
    }
}
