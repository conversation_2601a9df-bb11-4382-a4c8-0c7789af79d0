<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="list_bug" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\list_bug.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/list_bug_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="106" endOffset="51"/></Target><Target id="@+id/list_icon" view="ImageView"><Expressions/><location startLine="28" startOffset="12" endLine="33" endOffset="64"/></Target><Target id="@+id/itemName" view="TextView"><Expressions/><location startLine="45" startOffset="12" endLine="55" endOffset="41"/></Target><Target id="@+id/itemDate" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="66" endOffset="49"/></Target><Target id="@+id/itemAmount" view="TextView"><Expressions/><location startLine="79" startOffset="12" endLine="87" endOffset="56"/></Target></Targets></Layout>