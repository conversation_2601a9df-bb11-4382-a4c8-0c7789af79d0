<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:id="@+id/root_layout"
    android:padding="24dp">

    <!-- Welcome Icon -->
    <ImageView
        android:id="@+id/welcome_icon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="64dp"
        android:contentDescription="@string/todo"
        android:src="@drawable/logo"
        android:scaleType="fitCenter" />

    <!-- Description -->
    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/welcome_icon"
        android:layout_centerHorizontal="true"
        android:text="@string/your_personal_budget_tracker"
        android:textColor="?attr/colorOnSurface"
        android:textSize="16sp"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:fontFamily="sans-serif" />

    <!-- Email Container -->
    <LinearLayout
        android:id="@+id/email_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/description"
        android:layout_marginTop="48dp"
        android:orientation="vertical"
        android:background="@drawable/edittext_background"
        android:padding="16dp">

        <EditText
            android:id="@+id/email_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/enter_email"
            android:background="@android:color/transparent"
            android:inputType="textEmailAddress"
            android:textColor="?attr/colorOnSurface"
            android:textColorHint="?attr/colorOnSurface"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Password Container -->
    <LinearLayout
        android:id="@+id/password_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/email_container"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:background="@drawable/edittext_background"
        android:padding="16dp">

        <EditText
            android:id="@+id/password_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="@string/enter_password"
            android:background="@android:color/transparent"
            android:inputType="textPassword"
            android:textColor="?attr/colorOnSurface"
            android:textColorHint="?attr/colorOnSurface"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/password_eye"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:contentDescription="@string/todo"
            android:src="@drawable/eye_icon"
            android:tint="?attr/colorOnSurface" />

    </LinearLayout>

    <!-- Login Button -->
    <Button
        android:id="@+id/login_button"
        style="@style/MediumButton"
        android:layout_below="@id/password_container"
        android:layout_marginTop="32dp"
        android:text="@string/log_in" />

    <!-- Or Log In With -->
    <TextView
        android:id="@+id/or_login_with"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/login_button"
        android:layout_centerHorizontal="true"
        android:text="@string/or_log_in_with"
        android:textColor="?attr/colorOnSurface"
        android:textSize="14sp"
        android:layout_marginTop="24dp" />

    <!-- Biometric Authentication Card -->
    <LinearLayout
        android:id="@+id/biometric_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/or_login_with"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:orientation="vertical"
        android:background="@drawable/card_background"
        android:padding="24dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/fingerprint_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="@string/fingerprint_icon_description"
            android:src="@drawable/fingerprint"
            android:tint="?attr/colorPrimary" />

        <TextView
            android:id="@+id/fingerprint_instead"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/touch_id"
            android:textColor="?attr/colorPrimary"
            android:textSize="12sp"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- Sign Up Link -->
    <Button
        android:id="@+id/signup_instead"
        style="@android:style/Widget.Button.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/biometric_card"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="24dp"
        android:text="@string/sign_up"
        android:textSize="14sp"
        android:background="@android:color/transparent"
        android:textColor="?attr/colorPrimary" />

    <!-- Loading Spinner Overlay -->
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="@color/scrim_overlay">

        <include layout="@layout/spinner" />

    </RelativeLayout>

</RelativeLayout>
