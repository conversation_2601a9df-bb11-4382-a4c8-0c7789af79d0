package com.example.budgettracker

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.ContentValues
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class SettingsActivity : AppCompatActivity() {
    
    private lateinit var dateTextView: TextView
    private lateinit var balanceTextView: TextView
    private lateinit var targetTextView: TextView
    private lateinit var budgetTextView: TextView
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var editText4: EditText
    private lateinit var calendar: Calendar
    private lateinit var dateFormat: SimpleDateFormat
    private lateinit var user_id: String
    private lateinit var loadingSpinner: LoadingSpinner

    private fun setupNavigationButtons() {
        val homeButton = findViewById<ImageButton>(R.id.button_home)
        val expensesButton = findViewById<ImageButton>(R.id.button_expenses)
        val transactionsButton = findViewById<ImageButton>(R.id.button_transactions)
        val profileButton = findViewById<ImageButton>(R.id.button_profile)
        val notificationButton = findViewById<ImageButton>(R.id.button_notification)

        homeButton.setOnClickListener { navigateTo(DashboardActivity::class.java) }
        expensesButton.setOnClickListener { navigateTo(ExpensesActivity::class.java) }
        transactionsButton.setOnClickListener { navigateTo(TransactionsActivity::class.java) }
        profileButton.setOnClickListener { navigateTo(SettingsActivity::class.java) }
        notificationButton.setOnClickListener { navigateTo(NotificationActivity::class.java) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.settings)
        
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)

        dbHelper = DatabaseHelper(this)
        val sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid() ?: ""
        }
        
        editText4 = findViewById(R.id.editText4)
        val saveButton = findViewById<Button>(R.id.save_button)
        val datePickerButton = findViewById<Button>(R.id.date_picker_button)
        val clearDataButton = findViewById<Button>(R.id.buttonClearUserData)
        dateTextView = findViewById(R.id.title_date)
        balanceTextView = findViewById(R.id.trans_balance)
        targetTextView = findViewById(R.id.trans_target)
        budgetTextView = findViewById(R.id.trans_budget)
        @SuppressLint("CutPasteId") val languageSettingsButton = findViewById<Button>(R.id.buttonLanguageSettings)
        @SuppressLint("CutPasteId") val clearUserDataButton = findViewById<Button>(R.id.buttonClearUserData)

        calendar = Calendar.getInstance()
        dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        updateDateLabel()
        
        if (NetworkUtils.isInternetAvailable(this)) {
            fetchDataForDatOnline(dateFormat.format(calendar.time))
        } else {
            fetchDataForDate(dateFormat.format(calendar.time))
        }

        datePickerButton.setOnClickListener { showDatePickerDialog() }

        languageSettingsButton.setOnClickListener {
            val intent = Intent(this@SettingsActivity, LanguageActivity::class.java)
            startActivity(intent)
        }

        // Setup clear user data button
        clearUserDataButton.setOnClickListener { showClearDataConfirmation() }

        // Setup navigation buttons
        setupNavigationButtons()

        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@SettingsActivity, DashboardActivity::class.java)
            startActivity(intent)
        }

        saveButton.setOnClickListener {
            handleSaveAction()
        }
        
        editText4.setOnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) {
                v.onKeyDown(keyCode, event)
                handleSaveAction()
                true
            } else {
                false
            }
        }
    }

    private fun navigateTo(destination: Class<*>) {
        val intent = Intent(this@SettingsActivity, destination)
        startActivity(intent)
        finish()
    }

    private fun showDatePickerDialog() {
        DatePickerDialog(this@SettingsActivity, { _, year, month, dayOfMonth ->
            calendar.set(year, month, dayOfMonth)
            updateDateLabel()

            if (NetworkUtils.isInternetAvailable(this)) {
                fetchDataForDatOnline(dateFormat.format(calendar.time))
            } else {
                fetchDataForDate(dateFormat.format(calendar.time))
            }
        }, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH)).show()
    }

    private fun fetchDataForDate(date: String) {
        val db: SQLiteDatabase = dbHelper.readableDatabase // Use getReadableDatabase() for read operations
        var cursor: Cursor? = null
        try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("yyyy-MM", Locale.getDefault())
            val parsedDate = inputFormat.parse(date)
            val monthYear = outputFormat.format(parsedDate!!)

            cursor = db.rawQuery(
                "SELECT trans_balance, trans_target, trans_budget FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?",
                arrayOf(monthYear, user_id)
            )
            if (cursor.moveToFirst()) {
                balanceTextView.text = String.format(Locale.getDefault(), "%.2f", cursor.getDouble(0))
                targetTextView.text = String.format(Locale.getDefault(), "%.2f", cursor.getDouble(1))
                budgetTextView.text = String.format(Locale.getDefault(), "%.2f", cursor.getDouble(2))
            } else {
                balanceTextView.text = "0.00"
                targetTextView.text = "0.00"
                budgetTextView.text = "0.00"
            }
        } catch (e: ParseException) {
            Log.w("DatabaseHelper", "Error parsing date: ${e.message}")
        } finally {
            cursor?.close()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun fetchDataForDatOnline(date: String) {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val outputFormat = SimpleDateFormat("yyyy-MM", Locale.getDefault())
        val parsedDate: Date?
        try {
            parsedDate = inputFormat.parse(date)
            if (parsedDate != null) {
                val monthYear = outputFormat.format(parsedDate)
                val call = apiService.getRecords("transactions", monthYear, user_id)
                call.enqueue(object : Callback<List<Record>> {
                    override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                        if (response.isSuccessful && response.body() != null) {
                            val records = response.body()!!
                            var balance = 0.00
                            var target = 0.00
                            var budget = 0.00

                            for (record in records) {
                                if (record is Transaction) {
                                    balance = record.getBalance()
                                    target = record.getTransTarget()
                                    budget = record.getTotal()
                                    break
                                }
                            }
                            balanceTextView.text = String.format(Locale.getDefault(), "%.2f", balance)
                            targetTextView.text = String.format(Locale.getDefault(), "%.2f", target)
                            budgetTextView.text = String.format(Locale.getDefault(), "%.2f", budget)
                            loadingSpinner.hide()
                        } else {
                            balanceTextView.text = "0.00"
                            targetTextView.text = "0.00"
                            budgetTextView.text = "0.00"
                            loadingSpinner.hide()
                            Toast.makeText(this@SettingsActivity, "No data found", Toast.LENGTH_SHORT).show()
                        }
                    }

                    override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                        loadingSpinner.hide()
                        Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
                    }
                })
            }
        } catch (e: ParseException) {
            loadingSpinner.hide()
            balanceTextView.text = "0.00"
            targetTextView.text = "0.00"
            budgetTextView.text = "0.00"
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            startActivity(Intent(this@SettingsActivity, MainActivity::class.java))
            finish()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun updateDateLabel() {
        dateTextView.text = dateFormat.format(calendar.time)
    }

    private fun handleSaveAction() {
        val targetStr = editText4.text.toString()
        val targetAmount: Int
        if (targetStr.isEmpty()) {
            Toast.makeText(this@SettingsActivity, "Please enter a target amount", Toast.LENGTH_SHORT).show()
            return
        }

        try {
            targetAmount = targetStr.toInt()
        } catch (e: NumberFormatException) {
            Toast.makeText(this@SettingsActivity, "Invalid target amount. Please enter a valid number.", Toast.LENGTH_SHORT).show()
            return
        }

        val monthYear = SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(calendar.time)
        if (NetworkUtils.isInternetAvailable(this)) {
            checkExistingOnline(monthYear, targetAmount)
        } else {
            checkExistingTarget(monthYear, targetAmount)
        }
    }

    private fun checkExistingTarget(monthYear: String, newTargetAmount: Int) {
        val db = dbHelper.readableDatabase
        val query = "SELECT COUNT(*) FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id))
        if (cursor.moveToFirst()) {
            val count = cursor.getInt(0)
            cursor.close()
            if (count > 0) {
                AlertDialog.Builder(this)
                    .setTitle("Confirmation")
                    .setMessage("A target for this month already exists. Do you want to remove the existing target and add the new one?")
                    .setPositiveButton("Yes") { _, _ -> updateTarget(monthYear, newTargetAmount) }
                    .setNegativeButton("No", null)
                    .show()
            } else {
                updateTarget(monthYear, newTargetAmount)
            }
        }
    }

    private fun checkExistingOnline(monthYear: String, newTargetAmount: Int) {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("transactions", monthYear, user_id)
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var targetExists = false

                    for (record in records) {
                        if (record is Transaction) {
                            targetExists = true
                            break
                        }
                    }

                    if (targetExists) {
                        AlertDialog.Builder(this@SettingsActivity)
                            .setTitle("Confirmation")
                            .setMessage("A target for this month already exists. Do you want to remove the existing target and add the new one?")
                            .setPositiveButton("Yes") { _, _ -> updateTargetOnilne(monthYear, newTargetAmount) }
                            .setNegativeButton("No") { _, _ -> loadingSpinner.hide() }
                            .show()
                    } else {
                        updateTargetOnilne(monthYear, newTargetAmount)
                    }
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "Failed to check existing target", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                Log.e("Trialx", t.toString())
                Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun updateTarget(monthYear: String, newTargetAmount: Int) {
        val db = dbHelper.writableDatabase
        val date = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(calendar.time)
        val values = ContentValues().apply {
            put("trans_target", newTargetAmount)
            put("trans_month", date)
        }

        val rowsUpdated = db.update("transactions", values, " strftime('%Y-%m', trans_month)  LIKE ? AND user_id = ?", arrayOf(monthYear, user_id))
        if (rowsUpdated == 0) {
            values.put("user_id", user_id)
            val newRowId = db.insert("transactions", null, values)
            if (newRowId == -1L) {
                Toast.makeText(this@SettingsActivity, "Error adding new target", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this@SettingsActivity, "New target added successfully", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this@SettingsActivity, "Target updated successfully", Toast.LENGTH_SHORT).show()
        }
        Toast.makeText(this@SettingsActivity, "Target updated successfully$rowsUpdated", Toast.LENGTH_SHORT).show()
        db.close()
        loadingSpinner.hide()
        val intent = intent
        finish()
        startActivity(intent)
    }

    private fun updateTargetOnilne(monthYear: String, newTargetAmount: Int) {
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val data = hashMapOf<String, Any>(
            "trans_target" to newTargetAmount,
            "user_id" to user_id
        )
        val updateCall = apiService.updateRecord("transactions", user_id, monthYear, data)
        updateCall.enqueue(object : Callback<Void> {
            override fun onResponse(call: Call<Void>, response: Response<Void>) {
                if (response.isSuccessful) {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "Target updated successfully", Toast.LENGTH_SHORT).show()
                } else if (response.code() == 404) {
                    insertNewTargetOnline(monthYear, newTargetAmount)
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "Error updating target: ${response.message()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<Void>, t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
        loadingSpinner.hide()
        val intent = intent
        finish()
        startActivity(intent)
    }

    private fun insertNewTargetOnline(monthYear: String, newTargetAmount: Int) {
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val data = hashMapOf<String, Any>(
            "trans_target" to newTargetAmount,
            "trans_month" to monthYear,
            "user_id" to user_id
        )
        val insertCall = apiService.createRecord("transactions", data)
        insertCall.enqueue(object : Callback<Void> {
            override fun onResponse(call: Call<Void>, response: Response<Void>) {
                if (response.isSuccessful) {
                    Toast.makeText(this@SettingsActivity, "New target added successfully", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this@SettingsActivity, "Error adding new target: ${response.message()}", Toast.LENGTH_SHORT).show()
                }
                loadingSpinner.hide()
                val intent = intent
                finish()
                startActivity(intent)
            }

            override fun onFailure(call: Call<Void>, t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun showClearDataConfirmation() {
        AlertDialog.Builder(this)
            .setTitle("Clear All User Data")
            .setMessage("This will permanently delete all your expenses, transactions, and settings. This action cannot be undone. Are you sure?")
            .setPositiveButton("Yes, Clear All") { _, _ -> clearAllUserData() }
            .setNegativeButton("Cancel", null)
            .setIcon(android.R.drawable.ic_dialog_alert)
            .show()
    }

    private fun clearAllUserData() {
        loadingSpinner.show()

        // Clear Firebase data if online and authenticated
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            val firebaseManager = FirebaseManager()
            if (firebaseManager.isUserAuthenticated()) {
                clearFirebaseData(firebaseManager)
            } else {
                clearLocalDataOnly()
            }
        } else {
            clearLocalDataOnly()
        }
    }

    private fun clearFirebaseData(firebaseManager: FirebaseManager) {
        // Clear expenses from Firebase
        firebaseManager.clearAllExpenses(object : FirebaseManager.FirebaseCallback<Void> {
            override fun onSuccess(result: Void?) {
                Log.d("SettingsActivity", "Firebase expenses cleared")
                // Clear transactions from Firebase
                firebaseManager.clearAllTransactions(object : FirebaseManager.FirebaseCallback<Void> {
                    override fun onSuccess(result: Void?) {
                        Log.d("SettingsActivity", "Firebase transactions cleared")
                        // Clear notifications from Firebase
                        firebaseManager.clearAllNotifications(object : FirebaseManager.FirebaseCallback<Void> {
                            override fun onSuccess(result: Void?) {
                                Log.d("SettingsActivity", "Firebase notifications cleared")
                                // Finally clear local data
                                clearLocalDataOnly()
                            }

                            override fun onFailure(e: Exception) {
                                Log.e("SettingsActivity", "Failed to clear Firebase notifications", e)
                                // Still clear local data even if Firebase fails
                                clearLocalDataOnly()
                            }
                        })
                    }

                    override fun onFailure(e: Exception) {
                        Log.e("SettingsActivity", "Failed to clear Firebase transactions", e)
                        // Still clear local data even if Firebase fails
                        clearLocalDataOnly()
                    }
                })
            }

            override fun onFailure(e: Exception) {
                Log.e("SettingsActivity", "Failed to clear Firebase expenses", e)
                // Still clear local data even if Firebase fails
                clearLocalDataOnly()
            }
        })
    }

    private fun clearLocalDataOnly() {
        try {
            // Clear SQLite database
            val db = dbHelper.writableDatabase

            // Delete all user data from all tables
            db.delete("expenses", "user_id = ?", arrayOf(user_id))
            db.delete("budget", "user_id = ?", arrayOf(user_id))
            db.delete("transactions", "user_id = ?", arrayOf(user_id))
            db.delete("user", "user_id = ?", arrayOf(user_id))

            db.close()

            // Clear session data
            val sessionManager = SessionManager(this)
            sessionManager.logoutUser()

            loadingSpinner.hide()

            // Show success message and redirect to login
            Toast.makeText(this, "All user data has been cleared successfully", Toast.LENGTH_LONG).show()

            // Redirect to login screen
            val intent = Intent(this@SettingsActivity, MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            finish()

        } catch (e: Exception) {
            loadingSpinner.hide()
            Log.e("SettingsActivity", "Error clearing local data", e)
            Toast.makeText(this, "Error clearing data: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
}
