#Tue Jun 10 00:30:22 EAT 2025
com.example.budgettracker.app-main-61\:/anim/item_animation_fall_down.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_item_animation_fall_down.xml.flat
com.example.budgettracker.app-main-61\:/drawable/attach_money.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_attach_money.xml.flat
com.example.budgettracker.app-main-61\:/drawable/back.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_back.xml.flat
com.example.budgettracker.app-main-61\:/drawable/backg_button.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_backg_button.xml.flat
com.example.budgettracker.app-main-61\:/drawable/button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/button_danger_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_danger_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/button_ripple.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_ripple.xml.flat
com.example.budgettracker.app-main-61\:/drawable/card_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_card_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_chip_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/circle_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_circle_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/credit_card.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_credit_card.xml.flat
com.example.budgettracker.app-main-61\:/drawable/edittext_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_edittext_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/expand_more.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_expand_more.xml.flat
com.example.budgettracker.app-main-61\:/drawable/eye_close.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_eye_close.xml.flat
com.example.budgettracker.app-main-61\:/drawable/eye_icon.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_eye_icon.xml.flat
com.example.budgettracker.app-main-61\:/drawable/fab_label_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_fab_label_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/fingerprint.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_fingerprint.xml.flat
com.example.budgettracker.app-main-61\:/drawable/gradient_primary_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_gradient_primary_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/home.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_home.xml.flat
com.example.budgettracker.app-main-61\:/drawable/ic_add.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_add.xml.flat
com.example.budgettracker.app-main-61\:/drawable/ic_launcher_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_foreground.xml.flat
com.example.budgettracker.app-main-61\:/drawable/icon_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_button_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logo.png.flat
com.example.budgettracker.app-main-61\:/drawable/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logos.png.flat
com.example.budgettracker.app-main-61\:/drawable/logout.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logout.xml.flat
com.example.budgettracker.app-main-61\:/drawable/mail_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mail_24px.xml.flat
com.example.budgettracker.app-main-61\:/drawable/medium_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_medium_button_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/modern_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_modern_button_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/monitoring.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_monitoring.xml.flat
com.example.budgettracker.app-main-61\:/drawable/nav_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_nav_button_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/notification_info_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_info_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/notification_success_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_success_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/notification_warning_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_warning_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notifications.xml.flat
com.example.budgettracker.app-main-61\:/drawable/person.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_person.xml.flat
com.example.budgettracker.app-main-61\:/drawable/search.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_search.xml.flat
com.example.budgettracker.app-main-61\:/drawable/settings_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_settings_24px.xml.flat
com.example.budgettracker.app-main-61\:/drawable/shopping.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_shopping.xml.flat
com.example.budgettracker.app-main-61\:/drawable/small_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_small_button_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/small_chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_small_chip_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/small_chip_selected_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_small_chip_selected_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_normal.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_normal.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_press.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_press.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_select.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_select.xml.flat
com.example.budgettracker.app-main-61\:/drawable/star_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_star_24px.xml.flat
com.example.budgettracker.app-main-61\:/drawable/transaction_icon_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_transaction_icon_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/transaction_type_chip.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_transaction_type_chip.xml.flat
com.example.budgettracker.app-main-61\:/drawable/warning_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_warning_24px.xml.flat
com.example.budgettracker.app-main-61\:/drawable/work_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_work_24px.xml.flat
com.example.budgettracker.app-main-61\:/menu/bottom_navigation_menu.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_bottom_navigation_menu.xml.flat
com.example.budgettracker.app-main-61\:/menu/menu_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_menu_main.xml.flat
com.example.budgettracker.app-main-61\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.budgettracker.app-main-61\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.budgettracker.app-main-61\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-hdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_logo.png.flat
com.example.budgettracker.app-main-61\:/mipmap-hdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-mdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_logo.png.flat
com.example.budgettracker.app-main-61\:/mipmap-mdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_logo.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_logo.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_logo.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xxxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/navigation/nav_graph.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\navigation_nav_graph.xml.flat
com.example.budgettracker.app-main-61\:/xml/backup_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.budgettracker.app-main-61\:/xml/data_extraction_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/activity_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_activity_main.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/addexpense.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_addexpense.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/addtransaction.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_addtransaction.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/base.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_base.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/content_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_content_main.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/dashboard.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dashboard.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/item_notification.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_notification.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/language.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_language.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/list_bug.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_list_bug.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/list_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_list_item.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/login.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_login.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notifications.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/register.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_register.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/settings.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_settings.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/spinner.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_spinner.xml.flat
com.example.budgettracker.app-mergeReleaseResources-59\:/layout/spinner_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_spinner_item.xml.flat
