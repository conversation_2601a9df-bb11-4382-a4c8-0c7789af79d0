package com.example.budgettracker;
import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

public class DatabaseHelper extends SQLiteOpenHelper {

    private static final String DATABASE_NAME = "casemate.db";
    private static final int DATABASE_VERSION = 4;
    private static final String COLUMN_USER_ID = "user_id";
    //EXPENSES
    private static final String TABLE_EXPENSES = "expenses";
    private static final String COLUMN_EXPENSE_ID = "exp_id";
     private static final String COLUMN_EXPENSE_NAME = "name";
    private static final String COLUMN_EXPENSE_NOTE = "note";
    private static final String COLUMN_EXPENSE_DATE = "date";
    private static final String COLUMN_EXPENSE_AMOUNT = "amount";
    private static final String COLUMN_EXPENSE_BUDGET_AMOUNT = "budget_amount";

    private static final String TABLE_EXPENSES_CREATE =
            "CREATE TABLE " + TABLE_EXPENSES + " (" +
                    COLUMN_EXPENSE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COLUMN_USER_ID + " INTEGER, " +
                    COLUMN_EXPENSE_NAME + " TEXT, " +
                    COLUMN_EXPENSE_DATE + " DATETIME, " +
                    COLUMN_EXPENSE_NOTE +  " TEXT, " +
                    COLUMN_EXPENSE_AMOUNT + " INTEGER, " +
                    COLUMN_EXPENSE_BUDGET_AMOUNT + " INTEGER, " +
                    "synced INTEGER DEFAULT 0);";
    //USER
    private static final String TABLE_USER = "user";
    private static final String COLUMN_USER_DATE = "created_date";
    private static final String COLUMN_USER_NAME = "user_name";
    private static final String COLUMN_USER_STATUS = "user_status";
    private static final String COLUMN_USER_EMAIL = "user_email";
    private static final String TABLE_USER_CREATE =
            "CREATE TABLE " + TABLE_USER + " (" +
                    COLUMN_USER_ID + " TEXT, " +
                    COLUMN_USER_DATE + " DATETIME, " +
                    COLUMN_USER_EMAIL + " TEXT, " +
                    COLUMN_USER_STATUS + " TEXT, " +
                    COLUMN_USER_NAME + " TEXT);";

    //BUDGET
    private static final String TABLE_BUDGET = "budget";
    private static final String COLUMN_BUDGET_ID = "bud_id";
    private static final String COLUMN_BUDGET_NAME = "bud_name";
    private static final String COLUMN_BUDGET_DATE = "bud_date";
    private static final String COLUMN_BUDGET_AMOUNT = "bud_amount";

    private static final String TABLE_BUDGET_CREATE =
            "CREATE TABLE " + TABLE_BUDGET + " (" +
                    COLUMN_BUDGET_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COLUMN_USER_ID + " INTEGER, " +
                    COLUMN_BUDGET_DATE + " DATETIME, " +
                    COLUMN_BUDGET_AMOUNT + " FLOAT, "+
                    COLUMN_BUDGET_NAME + " TEXT, " +
                    "synced INTEGER DEFAULT 0);";
    //CATEGORIES
    private static final String TABLE_CATEGORIES = "categories";
    private static final String COLUMN_CATEGORY_ID = "cat_id";
    private static final String COLUMN_CATEGORY_NAME = "cat_name";
    private static final String TABLE_CATEGORIES_CREATE =
            "CREATE TABLE " + TABLE_CATEGORIES + " (" +
                    COLUMN_CATEGORY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COLUMN_CATEGORY_NAME + " TEXT);";

    //CATEGORIES
    private static final String TABLE_TRANS = "transactions";
    private static final String COLUMN_TRANS_ID = "trans_id";
    private static final String COLUMN_TRANS_BUDGET = "trans_budget";
    private static final String COLUMN_TRANS_BALANCE = "trans_balance";
    private static final String COLUMN_TRANS_MONTH = "trans_month";
    private static final String COLUMN_TRANS_TARGET = "trans_target";
    private static final String TABLE_TRANS_CREATE =
            "CREATE TABLE " + TABLE_TRANS + " (" +
                    COLUMN_TRANS_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COLUMN_USER_ID + " INTEGER, " +
                    COLUMN_TRANS_MONTH + " DATETIME, " +
                    COLUMN_TRANS_BALANCE + " FLOAT, "+
                    COLUMN_TRANS_TARGET + " FLOAT, "+
                    COLUMN_TRANS_BUDGET + " FLOAT);";

    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(TABLE_EXPENSES_CREATE);
        db.execSQL(TABLE_CATEGORIES_CREATE);
        db.execSQL(TABLE_BUDGET_CREATE);
        db.execSQL(TABLE_USER_CREATE);
        db.execSQL(TABLE_TRANS_CREATE);

        // Add other table creations here
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        if (oldVersion < 4) {
            // Add sync column to existing tables
            try {
                db.execSQL("ALTER TABLE " + TABLE_EXPENSES + " ADD COLUMN synced INTEGER DEFAULT 0");
                db.execSQL("ALTER TABLE " + TABLE_BUDGET + " ADD COLUMN synced INTEGER DEFAULT 0");
            } catch (Exception e) {
                // If columns already exist or other error, recreate tables
                db.execSQL("DROP TABLE IF EXISTS " + TABLE_EXPENSES);
                db.execSQL("DROP TABLE IF EXISTS " + TABLE_CATEGORIES);
                db.execSQL("DROP TABLE IF EXISTS " + TABLE_BUDGET);
                db.execSQL("DROP TABLE IF EXISTS " + TABLE_USER);
                db.execSQL("DROP TABLE IF EXISTS " + TABLE_TRANS);
                onCreate(db);
            }
        }
    }
}
