package com.example.budgettracker;

import android.util.Log;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.DocumentReference;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.Query;
import com.google.firebase.firestore.QueryDocumentSnapshot;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class FirebaseManager {
    private static final String TAG = "FirebaseManager";
    private FirebaseFirestore db;
    private FirebaseAuth auth;
    private String currentUserId;

    public FirebaseManager() {
        db = FirebaseFirestore.getInstance();
        auth = FirebaseAuth.getInstance();
        FirebaseUser currentUser = auth.getCurrentUser();
        if (currentUser != null) {
            currentUserId = currentUser.getUid();
        }
    }

    // Interface for callbacks
    public interface FirebaseCallback<T> {
        void onSuccess(T result);
        void onFailure(Exception e);
    }

    // User operations
    public void saveUser(String email, String username, String uid, FirebaseCallback<Void> callback) {
        Map<String, Object> user = new HashMap<>();
        user.put("user_id", uid);
        user.put("user_email", email);
        user.put("user_name", username);
        user.put("user_status", 0);
        user.put("created_date", getCurrentDate());

        db.collection("users").document(uid)
                .set(user)
                .addOnSuccessListener(aVoid -> {
                    Log.d(TAG, "User saved successfully");
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error saving user", e);
                    callback.onFailure(e);
                });
    }

    // Get user display name
    public void getUserDisplayName(String userId, FirebaseCallback<String> callback) {
        db.collection("users").document(userId)
                .get()
                .addOnSuccessListener(documentSnapshot -> {
                    if (documentSnapshot.exists()) {
                        String displayName = documentSnapshot.getString("user_name");
                        Log.d(TAG, "Retrieved username from Firebase: " + displayName);
                        callback.onSuccess(displayName);
                    } else {
                        Log.w(TAG, "User document does not exist for ID: " + userId);
                        callback.onSuccess(null);
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error getting user display name", e);
                    callback.onFailure(e);
                });
    }

    // Expense operations
    public void saveExpense(String name, String note, String date, int amount, FirebaseCallback<Void> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        Map<String, Object> expense = new HashMap<>();
        expense.put("user_id", currentUserId);
        expense.put("name", name);
        expense.put("note", note);
        expense.put("date", date);
        expense.put("amount", amount);
        expense.put("budget_amount", amount);
        expense.put("created_at", getCurrentDate());

        db.collection("expenses")
                .add(expense)
                .addOnSuccessListener(documentReference -> {
                    Log.d(TAG, "Expense saved with ID: " + documentReference.getId());
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error saving expense", e);
                    callback.onFailure(e);
                });
    }

    // Transaction (Budget) operations
    public void saveTransaction(String name, String date, int amount, FirebaseCallback<Void> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        Map<String, Object> transaction = new HashMap<>();
        transaction.put("user_id", currentUserId);
        transaction.put("bud_name", name);
        transaction.put("bud_date", date);
        transaction.put("bud_amount", amount);
        transaction.put("created_at", getCurrentDate());

        db.collection("budget")
                .add(transaction)
                .addOnSuccessListener(documentReference -> {
                    Log.d(TAG, "Transaction saved with ID: " + documentReference.getId());
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error saving transaction", e);
                    callback.onFailure(e);
                });
    }

    // Get expenses for current user (simplified to avoid index requirement)
    public void getExpenses(FirebaseCallback<List<Map<String, Object>>> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        db.collection("expenses")
                .whereEqualTo("user_id", currentUserId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    List<Map<String, Object>> expenses = new ArrayList<>();
                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        Map<String, Object> expense = document.getData();
                        expense.put("id", document.getId());
                        expenses.add(expense);
                    }

                    // Sort by date in descending order (most recent first) in the app
                    expenses.sort((a, b) -> {
                        String dateA = (String) a.get("date");
                        String dateB = (String) b.get("date");
                        if (dateA == null) dateA = "";
                        if (dateB == null) dateB = "";
                        return dateB.compareTo(dateA); // Descending order
                    });

                    callback.onSuccess(expenses);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error getting expenses", e);
                    callback.onFailure(e);
                });
    }

    // Get transactions for current user (simplified to avoid index requirement)
    public void getTransactions(FirebaseCallback<List<Map<String, Object>>> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        db.collection("budget")
                .whereEqualTo("user_id", currentUserId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    List<Map<String, Object>> transactions = new ArrayList<>();
                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        Map<String, Object> transaction = document.getData();
                        transaction.put("id", document.getId());
                        transactions.add(transaction);
                    }

                    // Sort by date in descending order (most recent first) in the app
                    transactions.sort((a, b) -> {
                        String dateA = (String) a.get("bud_date");
                        String dateB = (String) b.get("bud_date");
                        if (dateA == null) dateA = "";
                        if (dateB == null) dateB = "";
                        return dateB.compareTo(dateA); // Descending order
                    });

                    callback.onSuccess(transactions);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error getting transactions", e);
                    callback.onFailure(e);
                });
    }

    // Get or create transaction summary for a month
    public void getOrCreateTransactionSummary(String monthYear, FirebaseCallback<Map<String, Object>> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        DocumentReference docRef = db.collection("transactions")
                .document(currentUserId + "_" + monthYear);

        docRef.get().addOnCompleteListener(task -> {
            if (task.isSuccessful()) {
                DocumentSnapshot document = task.getResult();
                if (document.exists()) {
                    Map<String, Object> data = document.getData();
                    callback.onSuccess(data);
                } else {
                    // Create new transaction summary
                    Map<String, Object> newSummary = new HashMap<>();
                    newSummary.put("user_id", currentUserId);
                    newSummary.put("trans_month", monthYear + "-01");
                    newSummary.put("trans_balance", 0.0);
                    newSummary.put("trans_target", 0.0);
                    newSummary.put("trans_budget", 0.0);

                    docRef.set(newSummary)
                            .addOnSuccessListener(aVoid -> callback.onSuccess(newSummary))
                            .addOnFailureListener(callback::onFailure);
                }
            } else {
                callback.onFailure(task.getException());
            }
        });
    }

    // Update transaction summary
    public void updateTransactionSummary(String monthYear, double balance, double target, double budget, FirebaseCallback<Void> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        DocumentReference docRef = db.collection("transactions")
                .document(currentUserId + "_" + monthYear);

        Map<String, Object> updates = new HashMap<>();
        updates.put("trans_balance", balance);
        updates.put("trans_target", target);
        updates.put("trans_budget", budget);

        docRef.update(updates)
                .addOnSuccessListener(aVoid -> {
                    Log.d(TAG, "Transaction summary updated successfully");
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error updating transaction summary", e);
                    callback.onFailure(e);
                });
    }

    // Get expenses for a specific month (simplified to avoid index requirement)
    public void getExpensesForMonth(String monthYear, FirebaseCallback<List<Map<String, Object>>> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        // Use simpler query that only filters by user_id to avoid composite index requirement
        db.collection("expenses")
                .whereEqualTo("user_id", currentUserId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    List<Map<String, Object>> expenses = new ArrayList<>();
                    String startDate = monthYear + "-01";
                    String endDate = getNextMonth(monthYear) + "-01";

                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        Map<String, Object> expense = document.getData();
                        String expenseDate = (String) expense.get("date");

                        // Filter by date in the app instead of in the query
                        if (expenseDate != null && expenseDate.compareTo(startDate) >= 0 && expenseDate.compareTo(endDate) < 0) {
                            expense.put("id", document.getId());
                            expenses.add(expense);
                        }
                    }

                    // Sort by date in descending order (most recent first)
                    expenses.sort((a, b) -> {
                        String dateA = (String) a.get("date");
                        String dateB = (String) b.get("date");
                        if (dateA == null) dateA = "";
                        if (dateB == null) dateB = "";
                        return dateB.compareTo(dateA); // Descending order
                    });

                    callback.onSuccess(expenses);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error getting expenses for month", e);
                    callback.onFailure(e);
                });
    }

    // Helper methods
    private String getCurrentDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(new Date());
    }

    private String getNextMonth(String monthYear) {
        try {
            String[] parts = monthYear.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            month++;
            if (month > 12) {
                month = 1;
                year++;
            }

            return String.format(Locale.getDefault(), "%d-%02d", year, month);
        } catch (Exception e) {
            return monthYear;
        }
    }

    // Check if user is authenticated
    public boolean isUserAuthenticated() {
        return currentUserId != null;
    }

    // Get current user ID
    public String getCurrentUserId() {
        return currentUserId;
    }

    // Notification operations
    public void saveNotification(String title, String message, String type, FirebaseCallback<Void> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        Map<String, Object> notification = new HashMap<>();
        notification.put("user_id", currentUserId);
        notification.put("title", title);
        notification.put("message", message);
        notification.put("type", type);
        notification.put("timestamp", getCurrentDate());
        notification.put("is_read", false);
        notification.put("created_at", System.currentTimeMillis());

        db.collection("notifications")
                .add(notification)
                .addOnSuccessListener(documentReference -> {
                    Log.d(TAG, "Notification saved with ID: " + documentReference.getId());
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error saving notification", e);
                    callback.onFailure(e);
                });
    }

    // Get notifications for current user (simplified to avoid index requirement)
    public void getNotifications(FirebaseCallback<List<Map<String, Object>>> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        db.collection("notifications")
                .whereEqualTo("user_id", currentUserId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    List<Map<String, Object>> notifications = new ArrayList<>();
                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        Map<String, Object> notification = document.getData();
                        notification.put("id", document.getId());
                        notifications.add(notification);
                    }

                    // Sort by created_at in descending order (most recent first) in the app
                    notifications.sort((a, b) -> {
                        Object createdAtA = a.get("created_at");
                        Object createdAtB = b.get("created_at");

                        // Handle both Long and String timestamps
                        long timestampA = 0;
                        long timestampB = 0;

                        if (createdAtA instanceof Long) {
                            timestampA = (Long) createdAtA;
                        } else if (createdAtA instanceof String) {
                            try {
                                timestampA = Long.parseLong((String) createdAtA);
                            } catch (NumberFormatException e) {
                                timestampA = 0;
                            }
                        }

                        if (createdAtB instanceof Long) {
                            timestampB = (Long) createdAtB;
                        } else if (createdAtB instanceof String) {
                            try {
                                timestampB = Long.parseLong((String) createdAtB);
                            } catch (NumberFormatException e) {
                                timestampB = 0;
                            }
                        }

                        return Long.compare(timestampB, timestampA); // Descending order
                    });

                    // Limit to last 50 notifications
                    if (notifications.size() > 50) {
                        notifications = notifications.subList(0, 50);
                    }

                    callback.onSuccess(notifications);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error getting notifications", e);
                    callback.onFailure(e);
                });
    }

    // Mark notification as read
    public void markNotificationAsRead(String notificationId, FirebaseCallback<Void> callback) {
        db.collection("notifications").document(notificationId)
                .update("is_read", true)
                .addOnSuccessListener(aVoid -> {
                    Log.d(TAG, "Notification marked as read");
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error marking notification as read", e);
                    callback.onFailure(e);
                });
    }

    // Clear all notifications for current user
    public void clearAllNotifications(FirebaseCallback<Void> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        db.collection("notifications")
                .whereEqualTo("user_id", currentUserId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        document.getReference().delete();
                    }
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error clearing notifications", e);
                    callback.onFailure(e);
                });
    }

    // Create budget alert notification
    public void createBudgetAlert(String monthYear, double spentPercentage, FirebaseCallback<Void> callback) {
        String title = "Budget Alert";
        String message;
        String type;

        if (spentPercentage >= 100) {
            message = String.format("You've exceeded your budget by %.1f%% this month", spentPercentage - 100);
            type = "BUDGET_EXCEEDED";
        } else if (spentPercentage >= 80) {
            message = String.format("You've spent %.0f%% of your budget for this month", spentPercentage);
            type = "BUDGET_WARNING";
        } else {
            // No alert needed
            callback.onSuccess(null);
            return;
        }

        saveNotification(title, message, type, callback);
    }

    // Create goal achievement notification
    public void createGoalAchievementNotification(String goalType, FirebaseCallback<Void> callback) {
        String title = "Goal Achieved!";
        String message = String.format("Congratulations! You've reached your %s goal.", goalType);
        String type = "GOAL_ACHIEVED";

        saveNotification(title, message, type, callback);
    }

    // Create transaction notification
    public void createTransactionNotification(String transactionName, double amount, FirebaseCallback<Void> callback) {
        String title = "Transaction Added";
        String message = String.format("Added transaction: %s ($%.2f)", transactionName, amount);
        String type = "TRANSACTION_ADDED";

        saveNotification(title, message, type, callback);
    }

    // Create expense notification
    public void createExpenseNotification(String expenseName, double amount, FirebaseCallback<Void> callback) {
        String title = "Expense Added";
        String message = String.format("Added expense: %s ($%.2f)", expenseName, amount);
        String type = "EXPENSE_ADDED";

        saveNotification(title, message, type, callback);
    }

    // Clear all expenses for current user
    public void clearAllExpenses(FirebaseCallback<Void> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        db.collection("expenses")
                .whereEqualTo("user_id", currentUserId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        document.getReference().delete();
                    }
                    Log.d(TAG, "All expenses cleared for user: " + currentUserId);
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error clearing expenses", e);
                    callback.onFailure(e);
                });
    }

    // Clear all transactions for current user
    public void clearAllTransactions(FirebaseCallback<Void> callback) {
        if (currentUserId == null) {
            callback.onFailure(new Exception("User not authenticated"));
            return;
        }

        db.collection("transactions")
                .whereEqualTo("user_id", currentUserId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        document.getReference().delete();
                    }
                    Log.d(TAG, "All transactions cleared for user: " + currentUserId);
                    callback.onSuccess(null);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error clearing transactions", e);
                    callback.onFailure(e);
                });
    }


}
