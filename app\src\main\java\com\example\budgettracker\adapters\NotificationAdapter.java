package com.example.budgettracker.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.budgettracker.R;
import com.example.budgettracker.models.NotificationItem;
import java.util.List;

public class NotificationAdapter extends RecyclerView.Adapter<NotificationAdapter.NotificationViewHolder> {
    private List<NotificationItem> notifications;
    private OnNotificationClickListener clickListener;

    public interface OnNotificationClickListener {
        void onNotificationClick(NotificationItem notification, int position);
    }

    public NotificationAdapter(List<NotificationItem> notifications) {
        this.notifications = notifications;
    }

    public NotificationAdapter(List<NotificationItem> notifications, OnNotificationClickListener clickListener) {
        this.notifications = notifications;
        this.clickListener = clickListener;
    }

    @NonNull
    @Override
    public NotificationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_notification, parent, false);
        return new NotificationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull NotificationViewHolder holder, int position) {
        NotificationItem notification = notifications.get(position);

        holder.titleTextView.setText(notification.getTitle());
        holder.messageTextView.setText(notification.getMessage());
        holder.timestampTextView.setText(notification.getTimestamp());

        // Set icon and background based on notification type
        switch (notification.getType()) {
            case BUDGET_WARNING:
            case BUDGET_EXCEEDED:
                holder.iconImageView.setImageResource(R.drawable.warning_24px);
                holder.iconBackground.setBackgroundResource(R.drawable.notification_warning_background);
                break;
            case TRANSACTION_ADDED:
            case EXPENSE_ADDED:
                holder.iconImageView.setImageResource(R.drawable.attach_money);
                holder.iconBackground.setBackgroundResource(R.drawable.notification_success_background);
                break;
            case GOAL_ACHIEVED:
                holder.iconImageView.setImageResource(R.drawable.star_24px);
                holder.iconBackground.setBackgroundResource(R.drawable.notification_info_background);
                break;
            case LOW_BALANCE:
                holder.iconImageView.setImageResource(R.drawable.warning_24px);
                holder.iconBackground.setBackgroundResource(R.drawable.notification_warning_background);
                break;
        }

        // Set read/unread state
        if (notification.isRead()) {
            holder.itemView.setAlpha(0.7f);
        } else {
            holder.itemView.setAlpha(1.0f);
        }

        // Set click listener
        holder.itemView.setOnClickListener(v -> {
            if (clickListener != null) {
                clickListener.onNotificationClick(notification, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return notifications.size();
    }

    public void updateNotifications(List<NotificationItem> newNotifications) {
        this.notifications = newNotifications;
        notifyDataSetChanged();
    }

    public static class NotificationViewHolder extends RecyclerView.ViewHolder {
        TextView titleTextView;
        TextView messageTextView;
        TextView timestampTextView;
        ImageView iconImageView;
        LinearLayout iconBackground;

        public NotificationViewHolder(@NonNull View itemView) {
            super(itemView);
            titleTextView = itemView.findViewById(R.id.notification_title);
            messageTextView = itemView.findViewById(R.id.notification_message);
            timestampTextView = itemView.findViewById(R.id.notification_timestamp);
            iconImageView = itemView.findViewById(R.id.notification_icon);
            iconBackground = itemView.findViewById(R.id.notification_icon_background);
        }
    }
}
