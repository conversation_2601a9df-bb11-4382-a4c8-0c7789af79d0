{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-58:/values-en/values-en.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "64,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,63,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3339,1376,1663,1701,1773,1737,1845,1809,1565,1504,109,2745,720,1264,2782,3059,57,2595,2154,785,436,2409,2943,2209,3108,962,1015,540,3296,2458,163,2646,489,3153,2878,1608,1074,2545,1881,1920,295,3200,1117,334,663,1417,381,228,1315,2831,1223,836,1176,2266,597,2706,1456,3004,1979,2319,2034,875,3257", "endLines": "70,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,63,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17,62", "endColumns": "19,39,36,34,34,34,34,34,41,59,52,35,63,49,47,47,50,49,53,49,51,47,59,55,43,51,57,55,41,85,63,58,49,45,63,53,41,48,37,57,37,55,57,45,55,37,53,65,59,45,39,37,45,51,64,37,46,53,53,88,118,85,37", "endOffsets": "3553,1411,1695,1731,1803,1767,1875,1839,1602,1559,157,2776,779,1309,2825,3102,103,2640,2203,830,483,2452,2998,2260,3147,1009,1068,591,3333,2539,222,2700,534,3194,2937,1657,1111,2589,1914,1973,328,3251,1170,375,714,1450,430,289,1370,2872,1258,869,1217,2313,657,2739,1498,3053,2028,2403,2148,956,3290"}, "to": {"startLines": "2,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,268,308,345,380,415,450,485,520,562,622,675,711,775,825,873,921,972,1022,1076,1126,1178,1226,1286,1342,1386,1438,1496,1552,1594,1680,1744,1803,1853,1899,1963,2017,2059,2108,2146,2204,2242,2298,2356,2402,2458,2496,2550,2616,2676,2722,2762,2800,2846,2898,2963,3001,3048,3102,3156,3245,3364,3450", "endLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70", "endColumns": "19,39,36,34,34,34,34,34,41,59,52,35,63,49,47,47,50,49,53,49,51,47,59,55,43,51,57,55,41,85,63,58,49,45,63,53,41,48,37,57,37,55,57,45,55,37,53,65,59,45,39,37,45,51,64,37,46,53,53,88,118,85,37", "endOffsets": "263,303,340,375,410,445,480,515,557,617,670,706,770,820,868,916,967,1017,1071,1121,1173,1221,1281,1337,1381,1433,1491,1547,1589,1675,1739,1798,1848,1894,1958,2012,2054,2103,2141,2199,2237,2293,2351,2397,2453,2491,2545,2611,2671,2717,2757,2795,2841,2893,2958,2996,3043,3097,3151,3240,3359,3445,3483"}}]}]}