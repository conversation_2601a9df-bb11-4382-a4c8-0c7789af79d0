<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="categories_array">
        <item>Food</item>
        <item>Transport</item>
        <item>Entertainment</item>
        <item>Utilities</item>
        <item>Other</item>
    </string-array>
    <color name="back_ground">#b0463a</color>
    <color name="black">#FF000000</color>
    <color name="border_color">#C3C7CF</color>
    <color name="md_theme_light_background">#FCFCFF</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_inverseOnSurface">#F1F0F4</color>
    <color name="md_theme_light_inversePrimary">#9ECAFF</color>
    <color name="md_theme_light_inverseSurface">#2F3033</color>
    <color name="md_theme_light_onBackground">#1A1C1E</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_onPrimaryContainer">#001D36</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_onSecondaryContainer">#101C2B</color>
    <color name="md_theme_light_onSurface">#1A1C1E</color>
    <color name="md_theme_light_onSurfaceVariant">#43474E</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_onTertiaryContainer">#251431</color>
    <color name="md_theme_light_outline">#73777F</color>
    <color name="md_theme_light_outlineVariant">#C3C7CF</color>
    <color name="md_theme_light_primary">#0061A4</color>
    <color name="md_theme_light_primaryContainer">#D1E4FF</color>
    <color name="md_theme_light_scrim">#000000</color>
    <color name="md_theme_light_secondary">#535F70</color>
    <color name="md_theme_light_secondaryContainer">#D7E3F7</color>
    <color name="md_theme_light_surface">#FCFCFF</color>
    <color name="md_theme_light_surfaceVariant">#DFE2EB</color>
    <color name="md_theme_light_tertiary">#6B5778</color>
    <color name="md_theme_light_tertiaryContainer">#F2DAFF</color>
    <color name="onPrimary">#ffffff</color>
    <color name="primary">#0061A4</color>
    <color name="primary_blue">#0061A4</color>
    <color name="primary_light">#E3F2FD</color>
    <color name="primary_variant">#1976D2</color>
    <color name="radio_dot_color">#1A1C1E</color>
    <color name="scrim_overlay">#80000000</color>
    <color name="steelblue">#4682B4</color>
    <color name="text_color">#1A1C1E</color>
    <color name="white">#ffffff</color>
    <dimen name="fab_margin">16dp</dimen>
    <item name="closeIcon" type="id"/>
    <item name="header_section" type="id"/>
    <item name="profile_section" type="id"/>
    <string name="_0">$0</string>
    <string name="_0_00">$0</string>
    <string name="_12">+12%</string>
    <string name="_1d">1D</string>
    <string name="_1m">1M</string>
    <string name="_1w">1W</string>
    <string name="_1y">1Y</string>
    <string name="_3m">3M</string>
    <string name="_5_943">$5,943</string>
    <string name="account_balance">Account Balance</string>
    <string name="action_settings">Settings</string>
    <string name="add">Set</string>
    <string name="add_a_new_expense">Add a new expense</string>
    <string name="add_a_note">Add a note</string>
    <string name="add_expense">Add Expense</string>
    <string name="add_money">Add money</string>
    <string name="afrikaans">Afrikaans</string>
    <string name="app_name">BudgetTracker</string>
    <string name="back_description">Back</string>
    <string name="balance">Balance</string>
    <string name="budget">Budget</string>
    <string name="budget_alert">Budget alert</string>
    <string name="budget_info">Your budget for this month is $%d</string>
    <string name="budgethero">BudgetHero</string>
    <string name="bug_amount">Amount</string>
    <string name="chart_description">Chart</string>
    <string name="clear_all">Clear all</string>
    <string name="clear_user_data">Clear User Data</string>
    <string name="currency_icon">Currency Icon</string>
    <string name="date_x">Date</string>
    <string name="english">English</string>
    <string name="enter_email">Enter email</string>
    <string name="enter_password">Enter password</string>
    <string name="exp_amount">Amount</string>
    <string name="exp_date">exp_date</string>
    <string name="expense_description">Expense</string>
    <string name="ext_name">Name</string>
    <string name="fingerprint_icon_description">Fingerprint icon description</string>
    <string name="first_fragment_label">First Fragment</string>
    <string name="gcm_defaultSenderId" translatable="false">317792694031</string>
    <string name="google_api_key" translatable="false">AIzaSyDt8sOPacepJywe_oL0ZhAS09AHOOkuMgs</string>
    <string name="google_app_id" translatable="false">1:317792694031:android:9ae9916016b339df95987a</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyDt8sOPacepJywe_oL0ZhAS09AHOOkuMgs</string>
    <string name="google_storage_bucket" translatable="false">budgettracker-6abe4.appspot.com</string>
    <string name="heading_list_dash">Expenditures</string>
    <string name="home_description">Home</string>
    <string name="language">Language</string>
    <string name="language_settings">Language Settings</string>
    <string name="last_30_days">This month</string>
    <string name="log_in">Log in</string>
    <string name="logo_name">BudgetHero</string>
    <string name="main_cash">Main_cash</string>
    <string name="meta">META</string>
    <string name="meta_platforms">Meta Platforms</string>
    <string name="month">This month</string>
    <string name="name_bug">Name</string>
    <string name="next">Next</string>
    <string name="notifications">Notifications</string>
    <string name="or_log_in_with">Or log in with</string>
    <string name="pick_date">Pick Date</string>
    <string name="previous">Previous</string>
    <string name="profile_description">Profile</string>
    <string name="project_id" translatable="false">budgettracker-6abe4</string>
    <string name="save">Save</string>
    <string name="search_description">Search</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="select_category">Select category</string>
    <string name="set">Set</string>
    <string name="set_new_target">Month Target</string>
    <string name="settings">settings</string>
    <string name="sign_up">Sign Up</string>
    <string name="target">Target</string>
    <string name="today">Today</string>
    <string name="todo">TODO</string>
    <string name="touch_id">Touch ID</string>
    <string name="transaction">Transaction</string>
    <string name="transaction_description">Transactions</string>
    <string name="type">Type</string>
    <string name="user_name">Username</string>
    <string name="user_profile">user profile</string>
    <string name="view_all">View All</string>
    <string name="warning_icon">Warning Icon</string>
    <string name="you_ve_received_25_from_sarah">You’ve received $25 from Sarah</string>
    <string name="you_ve_spent_70_of_your_budget_for_the_month">You’ve spent 70% of your budget for the month</string>
    <string name="your_personal_budget_tracker">Your personal budget tracker</string>
    <string name="zulu">Zulu</string>
    <style name="App.Widget.BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">?attr/colorPrimaryContainer</item>
    </style>
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        
        <item name="colorPrimary">@color/material_dynamic_primary10</item>
        <item name="colorPrimaryVariant">@color/material_dynamic_primary20</item>
        <item name="colorOnPrimary">@color/material_dynamic_secondary10</item>
        <item name="colorSecondary">@color/material_dynamic_secondary20</item>
        <item name="colorSecondaryVariant">@color/material_dynamic_secondary30</item>
        <item name="colorOnSecondary">@color/material_dynamic_secondary50</item>
    </style>
    <style name="Base.Theme.BudgetTracker" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        
        
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        
        
        <item name="android:statusBarColor">@color/md_theme_light_surface</item>
        <item name="android:windowLightStatusBar">true</item>
        
        
        <item name="android:navigationBarColor">@color/md_theme_light_surface</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>
    <style name="IconButton">
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:background">@drawable/icon_button_background</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:elevation">2dp</item>
        <item name="android:tint">?attr/colorOnSurface</item>
    </style>
    <style name="MediumButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/medium_button_background</item>
        <item name="android:elevation">3dp</item>
    </style>
    <style name="SmallButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:minWidth">80dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/small_button_background</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:elevation">2dp</item>
    </style>
</resources>