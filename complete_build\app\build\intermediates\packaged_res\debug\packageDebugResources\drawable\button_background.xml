<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
     <!-- Pressed state -->
        <item android:state_pressed="true">
            <shape android:shape="rectangle">
                <solid android:color="#bcc0c4"/>
                <corners android:radius="4dp"/>
            </shape>
        </item>

        <!-- Default state -->
        <item>
            <shape android:shape="rectangle">
                <solid android:color="@android:color/transparent"/>
            </shape>
        </item>


</selector>