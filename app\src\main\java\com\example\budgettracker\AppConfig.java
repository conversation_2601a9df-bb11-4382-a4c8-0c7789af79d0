package com.example.budgettracker;

/**
 * Application configuration class to control Firebase usage
 * This allows easy enabling/disabling of Firebase features during development
 */
public class AppConfig {

    /**
     * Set this to false to disable all Firebase calls across the app
     * Set to true once Firestore database is properly set up
     */
    public static final boolean ENABLE_FIREBASE = true;

    /**
     * Check if Firebase should be used for the given context
     * @return true if Firebase is enabled and should be used
     */
    public static boolean shouldUseFirebase() {
        return ENABLE_FIREBASE;
    }

    /**
     * Check if Firebase should be used with network availability check
     * @param hasNetwork whether network is available
     * @return true if Firebase should be used
     */
    public static boolean shouldUseFirebase(boolean hasNetwork) {
        return ENABLE_FIREBASE && hasNetwork;
    }
}
