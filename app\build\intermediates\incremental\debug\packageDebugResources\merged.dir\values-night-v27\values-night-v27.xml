<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.BudgetTracker" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">#9ECAFF</item>
        <item name="colorOnPrimary">#003258</item>
        <item name="colorPrimaryContainer">#00497D</item>
        <item name="colorOnPrimaryContainer">#D1E4FF</item>

        
        <item name="colorSecondary">#BCC7DB</item>
        <item name="colorOnSecondary">#263141</item>
        <item name="colorSecondaryContainer">#3C4858</item>
        <item name="colorOnSecondaryContainer">#D7E3F7</item>

        
        <item name="colorTertiary">#D6BEE4</item>
        <item name="colorOnTertiary">#3B2948</item>
        <item name="colorTertiaryContainer">#523F5F</item>
        <item name="colorOnTertiaryContainer">#F2DAFF</item>

        
        <item name="colorError">#FFB4AB</item>
        <item name="colorOnError">#690005</item>
        <item name="colorErrorContainer">#93000A</item>
        <item name="colorOnErrorContainer">#FFDAD6</item>

        
        <item name="colorSurface">#101418</item>
        <item name="colorOnSurface">#E2E2E9</item>
        <item name="colorSurfaceVariant">#43474E</item>
        <item name="colorOnSurfaceVariant">#C3C7CF</item>
        <item name="colorSurfaceInverse">#E2E2E9</item>
        <item name="colorOnSurfaceInverse">#2F3033</item>

        
        <item name="android:colorBackground">#101418</item>
        <item name="colorOnBackground">#E2E2E9</item>

        
        <item name="android:statusBarColor">#101418</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">#101418</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>
</resources>