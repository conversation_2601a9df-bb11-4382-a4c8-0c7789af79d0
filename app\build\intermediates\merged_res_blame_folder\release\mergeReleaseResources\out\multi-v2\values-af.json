{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.example.budgettracker.app-mergeReleaseResources-58:\\values-af\\values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,334,444,535,676,789,901,1019,1157,1300,1421,1554,1698,1798,1953,2080,2218,2363,2485,2609,2705,2829,2916,3035,3136,3265", "endColumns": "156,121,109,90,140,112,111,117,137,142,120,132,143,99,154,126,137,144,121,123,95,123,86,118,100,128,97", "endOffsets": "207,329,439,530,671,784,896,1014,1152,1295,1416,1549,1693,1793,1948,2075,2213,2358,2480,2604,2700,2824,2911,3030,3131,3260,3358"}, "to": {"startLines": "59,60,92,95,102,103,107,108,109,110,111,112,113,115,116,117,119,120,121,197,210,211,212,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4214,4371,7694,7962,8418,8559,8987,9099,9217,9355,9498,9619,9752,9983,10083,10238,10430,10568,10713,16307,17124,17220,17344,17431,17550,17651,17780", "endColumns": "156,121,109,90,140,112,111,117,137,142,120,132,143,99,154,126,137,144,121,123,95,123,86,118,100,128,97", "endOffsets": "4366,4488,7799,8048,8554,8667,9094,9212,9350,9493,9614,9747,9891,10078,10233,10360,10563,10708,10830,16426,17215,17339,17426,17545,17646,17775,17873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,932,1028,1134,1219,1322,1440,1517,1593,1684,1777,1872,1966,2065,2158,2253,2352,2447,2541,2622,2729,2834,2931,3039,3142,3244,3398,16483", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "927,1023,1129,1214,1317,1435,1512,1588,1679,1772,1867,1961,2060,2153,2248,2347,2442,2536,2617,2724,2829,2926,3034,3137,3239,3393,3491,16559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,157", "endColumns": "101,116", "endOffsets": "152,269"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "15715,15817", "endColumns": "101,116", "endOffsets": "15812,15929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "6492", "endColumns": "142", "endOffsets": "6630"}}, {"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-af\\strings.xml", "from": {"startLines": "62,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3342,1420,1714,1752,1824,1788,1896,1860,1615,1555,114,2813,735,1305,2854,3149,57,2664,2214,805,445,2472,3019,2278,3198,988,1043,556,2526,171,2716,500,3242,2956,1658,1104,2614,1932,1971,304,3285,1149,347,678,1461,392,237,1360,2906,1263,856,1209,2332,613,2774,1501,3088,2030,2384,2090,895", "endLines": "68,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "endColumns": "19,39,36,34,34,34,34,34,41,58,55,39,68,53,50,47,55,50,62,49,53,52,67,52,42,53,59,55,86,64,56,54,41,61,54,43,48,37,57,41,55,58,43,55,38,51,65,58,48,40,37,52,50,63,37,52,59,58,86,122,91", "endOffsets": "3549,1455,1746,1782,1854,1818,1926,1890,1652,1609,165,2848,799,1354,2900,3192,108,2710,2272,850,494,2520,3082,2326,3236,1037,1098,607,2608,231,2768,550,3279,3013,1708,1143,2658,1965,2024,341,3336,1203,386,729,1495,439,298,1414,2950,1299,889,1257,2378,672,2807,1549,3143,2084,2466,2208,982"}, "to": {"startLines": "2,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,530,570,607,642,677,712,747,782,3496,3555,3611,3651,3720,3774,3825,4107,4163,4915,4978,5242,5296,5430,7909,8053,8096,8150,8274,9896,10365,10835,10892,11076,11118,11180,11235,11279,12259,12297,15934,15976,16032,16168,16212,16268,16431,16564,16630,16689,16839,16880,16918,16971,17022,17086,17878,17931,17991,18050,18137,18260", "endLines": "8,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "endColumns": "19,39,36,34,34,34,34,34,41,58,55,39,68,53,50,47,55,50,62,49,53,52,67,52,42,53,59,55,86,64,56,54,41,61,54,43,48,37,57,41,55,58,43,55,38,51,65,58,48,40,37,52,50,63,37,52,59,58,86,122,91", "endOffsets": "351,565,602,637,672,707,742,777,819,3550,3606,3646,3715,3769,3820,3868,4158,4209,4973,5023,5291,5344,5493,7957,8091,8145,8205,8325,9978,10425,10887,10942,11113,11175,11230,11274,11323,12292,12350,15971,16027,16086,16207,16263,16302,16478,16625,16684,16733,16875,16913,16966,17017,17081,17119,17926,17986,18045,18132,18255,18347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1055,1143,1209,1272,1358,1420,1481,1539,1605,1668,1723,1841,1898,1960,2015,2084,2203,2291,2374,2513,2596,2677,2805,2892,2969,3027,3078,3144,3213,3289,3375,3451,3525,3604,3677,3748,3851,3938,4009,4098,4188,4260,4335,4422,4473,4552,4619,4700,4784,4846,4910,4973,5043,5147,5250,5346,5446,5508,5563", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "269,350,430,508,603,691,791,905,986,1050,1138,1204,1267,1353,1415,1476,1534,1600,1663,1718,1836,1893,1955,2010,2079,2198,2286,2369,2508,2591,2672,2800,2887,2964,3022,3073,3139,3208,3284,3370,3446,3520,3599,3672,3743,3846,3933,4004,4093,4183,4255,4330,4417,4468,4547,4614,4695,4779,4841,4905,4968,5038,5142,5245,5341,5441,5503,5558,5635"}, "to": {"startLines": "9,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "356,4493,4574,4654,4732,4827,5028,5128,5349,8210,8330,10947,11013,11328,11414,11476,11537,11595,11661,11724,11779,11897,11954,12016,12071,12140,12355,12443,12526,12665,12748,12829,12957,13044,13121,13179,13230,13296,13365,13441,13527,13603,13677,13756,13829,13900,14003,14090,14161,14250,14340,14412,14487,14574,14625,14704,14771,14852,14936,14998,15062,15125,15195,15299,15402,15498,15598,15660,16091", "endLines": "12,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "525,4569,4649,4727,4822,4910,5123,5237,5425,8269,8413,11008,11071,11409,11471,11532,11590,11656,11719,11774,11892,11949,12011,12066,12135,12254,12438,12521,12660,12743,12824,12952,13039,13116,13174,13225,13291,13360,13436,13522,13598,13672,13751,13824,13895,13998,14085,14156,14245,14335,14407,14482,14569,14620,14699,14766,14847,14931,14993,15057,15120,15190,15294,15397,15493,15593,15655,15710,16163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "93,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "7804,8672,8774,8887", "endColumns": "104,101,112,99", "endOffsets": "7904,8769,8882,8982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "16738", "endColumns": "100", "endOffsets": "16834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}, "to": {"startLines": "55,56", "startColumns": "4,4", "startOffsets": "3873,3985", "endColumns": "111,121", "endOffsets": "3980,4102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5498,5605,5761,5887,5997,6147,6269,6390,6635,6801,6909,7066,7193,7332,7486,7552,7615", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "5600,5756,5882,5992,6142,6264,6385,6487,6796,6904,7061,7188,7327,7481,7547,7610,7689"}}]}, {"outputFile": "com.example.budgettracker.app-mergeReleaseResources-58:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-af\\values-af.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "59,60,92,95,102,103,107,108,109,110,111,112,113,115,116,117,119,120,121,197,210,211,212,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4206,4363,7686,7954,8410,8551,8979,9091,9209,9347,9490,9611,9744,9975,10075,10230,10422,10560,10705,16299,17116,17212,17336,17423,17542,17643,17772", "endColumns": "156,121,109,90,140,112,111,117,137,142,120,132,143,99,154,126,137,144,121,123,95,123,86,118,100,128,97", "endOffsets": "4358,4480,7791,8040,8546,8659,9086,9204,9342,9485,9606,9739,9883,10070,10225,10352,10555,10700,10822,16418,17207,17331,17418,17537,17638,17767,17865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,932,1028,1134,1219,1322,1440,1517,1593,1684,1777,1872,1966,2065,2158,2253,2352,2447,2541,2622,2729,2834,2931,3039,3142,3244,3398,16475", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "927,1023,1129,1214,1317,1435,1512,1588,1679,1772,1867,1961,2060,2153,2248,2347,2442,2536,2617,2724,2829,2926,3034,3137,3239,3393,3491,16551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-af\\values-af.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "15707,15809", "endColumns": "101,116", "endOffsets": "15804,15921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-af\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "6484", "endColumns": "142", "endOffsets": "6622"}}, {"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-af\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,100,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,530,570,607,642,677,712,747,782,3496,3555,3611,3651,3720,3774,3825,4107,4155,4907,4970,5234,5288,5422,7901,8045,8088,8142,8266,9888,10357,10827,10884,11068,11110,11172,11227,11271,12251,12289,15926,15968,16024,16160,16204,16260,16423,16556,16622,16681,16831,16872,16910,16963,17014,17078,17870,17923,17983,18042,18129,18252", "endLines": "8,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "endColumns": "19,39,36,34,34,34,34,34,41,58,55,39,68,53,50,47,47,50,62,49,53,52,67,52,42,53,59,55,86,64,56,54,41,61,54,43,48,37,57,41,55,58,43,55,38,51,65,58,48,40,37,52,50,63,37,52,59,58,86,122,91", "endOffsets": "351,565,602,637,672,707,742,777,819,3550,3606,3646,3715,3769,3820,3868,4150,4201,4965,5015,5283,5336,5485,7949,8083,8137,8197,8317,9970,10417,10879,10934,11105,11167,11222,11266,11315,12284,12342,15963,16019,16078,16199,16255,16294,16470,16617,16676,16725,16867,16905,16958,17009,17073,17111,17918,17978,18037,18124,18247,18339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-af\\values-af.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "9,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "356,4485,4566,4646,4724,4819,5020,5120,5341,8202,8322,10939,11005,11320,11406,11468,11529,11587,11653,11716,11771,11889,11946,12008,12063,12132,12347,12435,12518,12657,12740,12821,12949,13036,13113,13171,13222,13288,13357,13433,13519,13595,13669,13748,13821,13892,13995,14082,14153,14242,14332,14404,14479,14566,14617,14696,14763,14844,14928,14990,15054,15117,15187,15291,15394,15490,15590,15652,16083", "endLines": "12,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "525,4561,4641,4719,4814,4902,5115,5229,5417,8261,8405,11000,11063,11401,11463,11524,11582,11648,11711,11766,11884,11941,12003,12058,12127,12246,12430,12513,12652,12735,12816,12944,13031,13108,13166,13217,13283,13352,13428,13514,13590,13664,13743,13816,13887,13990,14077,14148,14237,14327,14399,14474,14561,14612,14691,14758,14839,14923,14985,15049,15112,15182,15286,15389,15485,15585,15647,15702,16155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-af\\values-af.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "93,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "7796,8664,8766,8879", "endColumns": "104,101,112,99", "endOffsets": "7896,8761,8874,8974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "16730", "endColumns": "100", "endOffsets": "16826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-af\\values-af.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "55,56", "startColumns": "4,4", "startOffsets": "3873,3985", "endColumns": "111,121", "endOffsets": "3980,4102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-af\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5490,5597,5753,5879,5989,6139,6261,6382,6627,6793,6901,7058,7185,7324,7478,7544,7607", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "5592,5748,5874,5984,6134,6256,6377,6479,6788,6896,7053,7180,7319,7473,7539,7602,7681"}}]}]}