#Tue Jun 10 08:31:34 EAT 2025
com.example.budgettracker.app-main-62\:/anim/item_animation_fall_down.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_item_animation_fall_down.xml.flat
com.example.budgettracker.app-main-62\:/drawable/attach_money.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_attach_money.xml.flat
com.example.budgettracker.app-main-62\:/drawable/back.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_back.xml.flat
com.example.budgettracker.app-main-62\:/drawable/backg_button.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_backg_button.xml.flat
com.example.budgettracker.app-main-62\:/drawable/button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/button_danger_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_danger_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/button_ripple.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_ripple.xml.flat
com.example.budgettracker.app-main-62\:/drawable/card_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chip_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/circle_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/credit_card.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_credit_card.xml.flat
com.example.budgettracker.app-main-62\:/drawable/edittext_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_edittext_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/expand_more.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_expand_more.xml.flat
com.example.budgettracker.app-main-62\:/drawable/eye_close.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_eye_close.xml.flat
com.example.budgettracker.app-main-62\:/drawable/eye_icon.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_eye_icon.xml.flat
com.example.budgettracker.app-main-62\:/drawable/fab_label_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_fab_label_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/fingerprint.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_fingerprint.xml.flat
com.example.budgettracker.app-main-62\:/drawable/gradient_primary_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_primary_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/home.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_home.xml.flat
com.example.budgettracker.app-main-62\:/drawable/ic_add.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.budgettracker.app-main-62\:/drawable/ic_launcher_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.budgettracker.app-main-62\:/drawable/icon_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_icon_button_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo.png.flat
com.example.budgettracker.app-main-62\:/drawable/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logos.png.flat
com.example.budgettracker.app-main-62\:/drawable/logout.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logout.xml.flat
com.example.budgettracker.app-main-62\:/drawable/mail_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_mail_24px.xml.flat
com.example.budgettracker.app-main-62\:/drawable/medium_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_medium_button_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/modern_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_modern_button_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/monitoring.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_monitoring.xml.flat
com.example.budgettracker.app-main-62\:/drawable/nav_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_nav_button_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/notification_info_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_notification_info_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/notification_success_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_notification_success_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/notification_warning_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_notification_warning_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_notifications.xml.flat
com.example.budgettracker.app-main-62\:/drawable/person.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_person.xml.flat
com.example.budgettracker.app-main-62\:/drawable/search.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search.xml.flat
com.example.budgettracker.app-main-62\:/drawable/settings_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_settings_24px.xml.flat
com.example.budgettracker.app-main-62\:/drawable/shopping.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_shopping.xml.flat
com.example.budgettracker.app-main-62\:/drawable/small_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_small_button_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/small_chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_small_chip_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/small_chip_selected_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_small_chip_selected_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/spinner_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/spinner_normal.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_normal.xml.flat
com.example.budgettracker.app-main-62\:/drawable/spinner_press.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_press.xml.flat
com.example.budgettracker.app-main-62\:/drawable/spinner_select.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_select.xml.flat
com.example.budgettracker.app-main-62\:/drawable/star_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_star_24px.xml.flat
com.example.budgettracker.app-main-62\:/drawable/transaction_icon_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_transaction_icon_background.xml.flat
com.example.budgettracker.app-main-62\:/drawable/transaction_type_chip.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_transaction_type_chip.xml.flat
com.example.budgettracker.app-main-62\:/drawable/warning_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_warning_24px.xml.flat
com.example.budgettracker.app-main-62\:/drawable/work_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_work_24px.xml.flat
com.example.budgettracker.app-main-62\:/menu/bottom_navigation_menu.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation_menu.xml.flat
com.example.budgettracker.app-main-62\:/menu/menu_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_main.xml.flat
com.example.budgettracker.app-main-62\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.budgettracker.app-main-62\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.budgettracker.app-main-62\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-hdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_logo.png.flat
com.example.budgettracker.app-main-62\:/mipmap-hdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_logos.png.flat
com.example.budgettracker.app-main-62\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-mdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_logo.png.flat
com.example.budgettracker.app-main-62\:/mipmap-mdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_logos.png.flat
com.example.budgettracker.app-main-62\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-xhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_logo.png.flat
com.example.budgettracker.app-main-62\:/mipmap-xhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_logos.png.flat
com.example.budgettracker.app-main-62\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-xxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_logo.png.flat
com.example.budgettracker.app-main-62\:/mipmap-xxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_logos.png.flat
com.example.budgettracker.app-main-62\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-62\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_logo.png.flat
com.example.budgettracker.app-main-62\:/mipmap-xxxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_logos.png.flat
com.example.budgettracker.app-main-62\:/navigation/nav_graph.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.example.budgettracker.app-main-62\:/xml/backup_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.budgettracker.app-main-62\:/xml/data_extraction_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/activity_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/addexpense.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_addexpense.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/addtransaction.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_addtransaction.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/base.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_base.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/content_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_content_main.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/dashboard.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dashboard.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/item_notification.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_notification.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/language.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_language.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/list_bug.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_list_bug.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/list_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_list_item.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/login.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_login.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_notifications.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/register.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_register.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/settings.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_settings.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/spinner.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner.xml.flat
com.example.budgettracker.app-mergeDebugResources-59\:/layout/spinner_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_item.xml.flat
