package com.example.budgettracker

import android.animation.ObjectAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView

class ItemAdapter(private val expensesList: List<Expenses>) : RecyclerView.Adapter<ItemAdapter.ViewHolder>() {

    private val expandedItems = HashSet<Int>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.list_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val expense = expensesList[position]

        // Set basic data
        val name = expense.getName()
        holder.nameTextView.text = name
        holder.dateTextView.text = expense.getDate()
        holder.amountTextView.text = String.format("$%d", expense.getAmount())

        // Set full description for expandable section
        var fullDescription = expense.getNote()
        if (fullDescription.isNullOrBlank()) {
            fullDescription = name // Use name as fallback
        }
        holder.fullDescriptionTextView.text = fullDescription

        // Show expand button only if description is longer than name or has additional info
        val hasExpandableContent = fullDescription != name || name.length > 20
        holder.expandButton.visibility = if (hasExpandableContent) View.VISIBLE else View.GONE

        // Handle expand/collapse state
        val isExpanded = expandedItems.contains(position)
        holder.expandableDetails.visibility = if (isExpanded) View.VISIBLE else View.GONE

        // Rotate expand button based on state
        val rotation = if (isExpanded) 180f else 0f
        holder.expandButton.rotation = rotation

        // Set click listeners
        setupClickListeners(holder, position, expense)

        // Add tooltip for truncated text
        if (name.length > 20) {
            holder.nameTextView.setOnLongClickListener { v ->
                Toast.makeText(v.context, name, Toast.LENGTH_LONG).show()
                true
            }
        }
    }

    private fun setupClickListeners(holder: ViewHolder, position: Int, expense: Expenses) {
        // Expand button click
        holder.expandButton.setOnClickListener {
            toggleExpansion(position, holder)
        }

        // Card click for expansion
        holder.itemView.setOnClickListener {
            if (holder.expandButton.visibility == View.VISIBLE) {
                toggleExpansion(position, holder)
            }
        }

        // Long click for full details
        holder.itemView.setOnLongClickListener { v ->
            val details = String.format(
                "Item: %s\nAmount: $%d\nDate: %s\nNote: %s",
                expense.getName(),
                expense.getAmount(),
                expense.getDate(),
                expense.getNote() ?: "No additional notes"
            )
            Toast.makeText(v.context, details, Toast.LENGTH_LONG).show()
            true
        }
    }

    private fun toggleExpansion(position: Int, holder: ViewHolder) {
        val isExpanded = expandedItems.contains(position)

        if (isExpanded) {
            expandedItems.remove(position)
            holder.expandableDetails.visibility = View.GONE
            animateExpandButton(holder.expandButton, 0f)
        } else {
            expandedItems.add(position)
            holder.expandableDetails.visibility = View.VISIBLE
            animateExpandButton(holder.expandButton, 180f)
        }
    }

    private fun animateExpandButton(button: ImageButton, targetRotation: Float) {
        val animator = ObjectAnimator.ofFloat(button, "rotation", targetRotation)
        animator.duration = 200
        animator.start()
    }

    override fun getItemCount(): Int {
        return expensesList.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val nameTextView: TextView = itemView.findViewById(R.id.itemName)
        val dateTextView: TextView = itemView.findViewById(R.id.itemDate)
        val amountTextView: TextView = itemView.findViewById(R.id.itemAmount)
        val fullDescriptionTextView: TextView = itemView.findViewById(R.id.itemFullDescription)
        val categoryTextView: TextView = itemView.findViewById(R.id.itemCategory)
        val expandButton: ImageButton = itemView.findViewById(R.id.expand_button)
        val expandableDetails: View = itemView.findViewById(R.id.expandable_details)
    }
}
