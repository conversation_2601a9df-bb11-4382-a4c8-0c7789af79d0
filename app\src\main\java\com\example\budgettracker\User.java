package com.example.budgettracker;


import com.google.gson.annotations.SerializedName;

public class User extends Record {
//    @SerializedName("user_user_id")
//    private String user_id;
    private String created_date;
    private String user_email;
    private String user_status;
    private String user_name;

    public User(String user_id, String created_date, String user_name, int user_status, String user_email) {
        super(user_id);
//        this.user_id = user_id;
        this.created_date = created_date;
        this.user_name = user_name;
        this.user_status = String.valueOf(user_status);
        this.user_email = user_email;
    }

    // Getters and Setters
//    public String getuser_id() {
//        return user_id;
//    }
//
//    public void setuser_id(String user_id) {
//        this.user_id = user_id;
//    }

    // Getters and Setters
    public String getuser_id() {
        return super.getuser_id();
    }

    public void setUserId(String user_id) {
        super.setuser_id(user_id);
    }
    public String getCreatedDate() {
        return created_date;
    }

    public void setCreatedDate(String created_date) {
        this.created_date = created_date;
    }

    public String getUserName() {
        return user_name;
    }

    public void setUserName(String user_name) {
        this.user_name = user_name;
    }

    public String getUserStatus() {
        return user_status;
    }

    public void setUserStatus(String user_status) {
        this.user_status = user_status;
    }

    public String getUserEmail() {
        return user_email;
    }

    public void setUserEmail(String user_email) {
        this.user_email = user_email;
    }
}
