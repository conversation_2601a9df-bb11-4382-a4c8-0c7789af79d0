package com.example.budgettracker;
import android.content.Context;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;

import java.util.concurrent.TimeUnit;
public class SyncScheduler {
    public static void scheduleSync(Context context) {
//        PeriodicWorkRequest syncRequest = new PeriodicWorkRequest.Builder(
//                SyncWorker.class,
//                15,
//                TimeUnit.MINUTES
//        ).build();

//        WorkManager.getInstance(context).enqueue(syncRequest);
    }
}
