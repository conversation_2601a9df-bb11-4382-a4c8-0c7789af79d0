package com.example.budgettracker

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.QueryDocumentSnapshot
import java.text.SimpleDateFormat
import java.util.*

class FirebaseManager {
    companion object {
        private const val TAG = "FirebaseManager"
    }
    
    private val db: FirebaseFirestore = FirebaseFirestore.getInstance()
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private var currentUserId: String? = null

    init {
        val currentUser: FirebaseUser? = auth.currentUser
        if (currentUser != null) {
            currentUserId = currentUser.uid
        }
    }

    // Interface for callbacks
    interface FirebaseCallback<T> {
        fun onSuccess(result: T?)
        fun onFailure(e: Exception)
    }

    // User operations
    fun saveUser(email: String, username: String, uid: String, callback: FirebaseCallback<Void>) {
        val user = hashMapOf<String, Any>(
            "user_id" to uid,
            "user_email" to email,
            "user_name" to username,
            "user_status" to 0,
            "created_date" to getCurrentDate()
        )

        db.collection("users").document(uid)
            .set(user)
            .addOnSuccessListener {
                Log.d(TAG, "User saved successfully")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error saving user", e)
                callback.onFailure(e)
            }
    }

    // Get user display name
    fun getUserDisplayName(userId: String, callback: FirebaseCallback<String?>) {
        db.collection("users").document(userId)
            .get()
            .addOnSuccessListener { documentSnapshot ->
                if (documentSnapshot.exists()) {
                    val displayName = documentSnapshot.getString("user_name")
                    Log.d(TAG, "Retrieved username from Firebase: $displayName")
                    callback.onSuccess(displayName)
                } else {
                    Log.w(TAG, "User document does not exist for ID: $userId")
                    callback.onSuccess(null)
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error getting user display name", e)
                callback.onFailure(e)
            }
    }

    // Expense operations
    fun saveExpense(name: String, note: String, date: String, amount: Int, callback: FirebaseCallback<Void>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        val expense = hashMapOf<String, Any>(
            "user_id" to currentUserId!!,
            "name" to name,
            "note" to note,
            "date" to date,
            "amount" to amount,
            "budget_amount" to amount,
            "created_at" to getCurrentDate()
        )

        db.collection("expenses")
            .add(expense)
            .addOnSuccessListener { documentReference ->
                Log.d(TAG, "Expense saved with ID: ${documentReference.id}")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error saving expense", e)
                callback.onFailure(e)
            }
    }

    // Transaction (Budget) operations
    fun saveTransaction(name: String, date: String, amount: Int, callback: FirebaseCallback<Void>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        val transaction = hashMapOf<String, Any>(
            "user_id" to currentUserId!!,
            "bud_name" to name,
            "bud_date" to date,
            "bud_amount" to amount,
            "created_at" to getCurrentDate()
        )

        db.collection("budget")
            .add(transaction)
            .addOnSuccessListener { documentReference ->
                Log.d(TAG, "Transaction saved with ID: ${documentReference.id}")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error saving transaction", e)
                callback.onFailure(e)
            }
    }

    // Get expenses for current user (simplified to avoid index requirement)
    fun getExpenses(callback: FirebaseCallback<List<Map<String, Any>>>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        db.collection("expenses")
            .whereEqualTo("user_id", currentUserId)
            .get()
            .addOnSuccessListener { queryDocumentSnapshots ->
                val expenses = mutableListOf<Map<String, Any>>()
                for (document in queryDocumentSnapshots) {
                    val expense = document.data.toMutableMap()
                    expense["id"] = document.id
                    expenses.add(expense)
                }

                // Sort by date in descending order (most recent first) in the app
                expenses.sortWith { a, b ->
                    val dateA = a["date"] as? String ?: ""
                    val dateB = b["date"] as? String ?: ""
                    dateB.compareTo(dateA) // Descending order
                }

                callback.onSuccess(expenses)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error getting expenses", e)
                callback.onFailure(e)
            }
    }

    // Get transactions for current user (simplified to avoid index requirement)
    fun getTransactions(callback: FirebaseCallback<List<Map<String, Any>>>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        db.collection("budget")
            .whereEqualTo("user_id", currentUserId)
            .get()
            .addOnSuccessListener { queryDocumentSnapshots ->
                val transactions = mutableListOf<Map<String, Any>>()
                for (document in queryDocumentSnapshots) {
                    val transaction = document.data.toMutableMap()
                    transaction["id"] = document.id
                    transactions.add(transaction)
                }

                // Sort by date in descending order (most recent first) in the app
                transactions.sortWith { a, b ->
                    val dateA = a["bud_date"] as? String ?: ""
                    val dateB = b["bud_date"] as? String ?: ""
                    dateB.compareTo(dateA) // Descending order
                }

                callback.onSuccess(transactions)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error getting transactions", e)
                callback.onFailure(e)
            }
    }

    // Get or create transaction summary for a month
    fun getOrCreateTransactionSummary(monthYear: String, callback: FirebaseCallback<Map<String, Any>>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        val docRef: DocumentReference = db.collection("transactions")
            .document("${currentUserId}_$monthYear")

        docRef.get().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val document: DocumentSnapshot = task.result
                if (document.exists()) {
                    val data = document.data
                    if (data != null) {
                        callback.onSuccess(data)
                    } else {
                        callback.onFailure(Exception("Document data is null"))
                    }
                } else {
                    // Create new transaction summary
                    val newSummary = hashMapOf<String, Any>(
                        "user_id" to currentUserId!!,
                        "trans_month" to "$monthYear-01",
                        "trans_balance" to 0.0,
                        "trans_target" to 0.0,
                        "trans_budget" to 0.0
                    )

                    docRef.set(newSummary)
                        .addOnSuccessListener { callback.onSuccess(newSummary) }
                        .addOnFailureListener { e -> callback.onFailure(e) }
                }
            } else {
                task.exception?.let { callback.onFailure(it) }
            }
        }
    }

    // Update transaction summary
    fun updateTransactionSummary(monthYear: String, balance: Double, target: Double, budget: Double, callback: FirebaseCallback<Void>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        val docRef: DocumentReference = db.collection("transactions")
            .document("${currentUserId}_$monthYear")

        val updates = hashMapOf<String, Any>(
            "trans_balance" to balance,
            "trans_target" to target,
            "trans_budget" to budget
        )

        docRef.update(updates)
            .addOnSuccessListener {
                Log.d(TAG, "Transaction summary updated successfully")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error updating transaction summary", e)
                callback.onFailure(e)
            }
    }

    // Helper methods
    private fun getCurrentDate(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return sdf.format(Date())
    }

    private fun getNextMonth(monthYear: String): String {
        return try {
            val parts = monthYear.split("-")
            var year = parts[0].toInt()
            var month = parts[1].toInt()

            month++
            if (month > 12) {
                month = 1
                year++
            }

            String.format(Locale.getDefault(), "%d-%02d", year, month)
        } catch (e: Exception) {
            monthYear
        }
    }

    // Check if user is authenticated
    fun isUserAuthenticated(): Boolean {
        return currentUserId != null
    }

    // Get current user ID
    fun getCurrentUserId(): String? {
        return currentUserId
    }

    // Get expenses for a specific month (simplified to avoid index requirement)
    fun getExpensesForMonth(monthYear: String, callback: FirebaseCallback<List<Map<String, Any>>>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        // Use simpler query that only filters by user_id to avoid composite index requirement
        db.collection("expenses")
            .whereEqualTo("user_id", currentUserId)
            .get()
            .addOnSuccessListener { queryDocumentSnapshots ->
                val expenses = mutableListOf<Map<String, Any>>()
                val startDate = "$monthYear-01"
                val endDate = "${getNextMonth(monthYear)}-01"

                for (document in queryDocumentSnapshots) {
                    val expense = document.data.toMutableMap()
                    val expenseDate = expense["date"] as? String

                    // Filter by date in the app instead of in the query
                    if (expenseDate != null && expenseDate >= startDate && expenseDate < endDate) {
                        expense["id"] = document.id
                        expenses.add(expense)
                    }
                }

                // Sort by date in descending order (most recent first)
                expenses.sortWith { a, b ->
                    val dateA = a["date"] as? String ?: ""
                    val dateB = b["date"] as? String ?: ""
                    dateB.compareTo(dateA) // Descending order
                }

                callback.onSuccess(expenses)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error getting expenses for month", e)
                callback.onFailure(e)
            }
    }

    // Notification operations
    fun saveNotification(title: String, message: String, type: String, callback: FirebaseCallback<Void>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        val notification = hashMapOf<String, Any>(
            "user_id" to currentUserId!!,
            "title" to title,
            "message" to message,
            "type" to type,
            "timestamp" to getCurrentDate(),
            "is_read" to false,
            "created_at" to System.currentTimeMillis()
        )

        db.collection("notifications")
            .add(notification)
            .addOnSuccessListener { documentReference ->
                Log.d(TAG, "Notification saved with ID: ${documentReference.id}")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error saving notification", e)
                callback.onFailure(e)
            }
    }

    // Get notifications for current user (simplified to avoid index requirement)
    fun getNotifications(callback: FirebaseCallback<List<Map<String, Any>>>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        db.collection("notifications")
            .whereEqualTo("user_id", currentUserId)
            .get()
            .addOnSuccessListener { queryDocumentSnapshots ->
                val notifications = mutableListOf<Map<String, Any>>()
                for (document in queryDocumentSnapshots) {
                    val notification = document.data.toMutableMap()
                    notification["id"] = document.id
                    notifications.add(notification)
                }

                // Sort by created_at in descending order (most recent first) in the app
                notifications.sortWith { a, b ->
                    val createdAtA = a["created_at"]
                    val createdAtB = b["created_at"]

                    // Handle both Long and String timestamps
                    val timestampA = when (createdAtA) {
                        is Long -> createdAtA
                        is String -> try { createdAtA.toLong() } catch (e: NumberFormatException) { 0L }
                        else -> 0L
                    }

                    val timestampB = when (createdAtB) {
                        is Long -> createdAtB
                        is String -> try { createdAtB.toLong() } catch (e: NumberFormatException) { 0L }
                        else -> 0L
                    }

                    timestampB.compareTo(timestampA) // Descending order
                }

                // Limit to last 50 notifications
                val limitedNotifications = if (notifications.size > 50) {
                    notifications.subList(0, 50)
                } else {
                    notifications
                }

                callback.onSuccess(limitedNotifications)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error getting notifications", e)
                callback.onFailure(e)
            }
    }

    // Mark notification as read
    fun markNotificationAsRead(notificationId: String, callback: FirebaseCallback<Void>) {
        db.collection("notifications").document(notificationId)
            .update("is_read", true)
            .addOnSuccessListener {
                Log.d(TAG, "Notification marked as read")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error marking notification as read", e)
                callback.onFailure(e)
            }
    }

    // Clear all notifications for current user
    fun clearAllNotifications(callback: FirebaseCallback<Void>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        db.collection("notifications")
            .whereEqualTo("user_id", currentUserId)
            .get()
            .addOnSuccessListener { queryDocumentSnapshots ->
                for (document in queryDocumentSnapshots) {
                    document.reference.delete()
                }
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error clearing notifications", e)
                callback.onFailure(e)
            }
    }

    // Create budget alert notification
    fun createBudgetAlert(monthYear: String, spentPercentage: Double, callback: FirebaseCallback<Void>) {
        val title = "Budget Alert"
        val message: String
        val type: String

        when {
            spentPercentage >= 100 -> {
                message = String.format("You've exceeded your budget by %.1f%% this month", spentPercentage - 100)
                type = "BUDGET_EXCEEDED"
            }
            spentPercentage >= 80 -> {
                message = String.format("You've spent %.0f%% of your budget for this month", spentPercentage)
                type = "BUDGET_WARNING"
            }
            else -> {
                // No alert needed
                callback.onSuccess(null)
                return
            }
        }

        saveNotification(title, message, type, callback)
    }

    // Create goal achievement notification
    fun createGoalAchievementNotification(goalType: String, callback: FirebaseCallback<Void>) {
        val title = "Goal Achieved!"
        val message = String.format("Congratulations! You've reached your %s goal.", goalType)
        val type = "GOAL_ACHIEVED"

        saveNotification(title, message, type, callback)
    }

    // Create transaction notification
    fun createTransactionNotification(transactionName: String, amount: Double, callback: FirebaseCallback<Void>) {
        val title = "Transaction Added"
        val message = String.format("Added transaction: %s ($%.2f)", transactionName, amount)
        val type = "TRANSACTION_ADDED"

        saveNotification(title, message, type, callback)
    }

    // Create expense notification
    fun createExpenseNotification(expenseName: String, amount: Double, callback: FirebaseCallback<Void>) {
        val title = "Expense Added"
        val message = String.format("Added expense: %s ($%.2f)", expenseName, amount)
        val type = "EXPENSE_ADDED"

        saveNotification(title, message, type, callback)
    }

    // Clear all expenses for current user
    fun clearAllExpenses(callback: FirebaseCallback<Void>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        db.collection("expenses")
            .whereEqualTo("user_id", currentUserId)
            .get()
            .addOnSuccessListener { queryDocumentSnapshots ->
                for (document in queryDocumentSnapshots) {
                    document.reference.delete()
                }
                Log.d(TAG, "All expenses cleared for user: $currentUserId")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error clearing expenses", e)
                callback.onFailure(e)
            }
    }

    // Clear all transactions for current user
    fun clearAllTransactions(callback: FirebaseCallback<Void>) {
        if (currentUserId == null) {
            callback.onFailure(Exception("User not authenticated"))
            return
        }

        db.collection("transactions")
            .whereEqualTo("user_id", currentUserId)
            .get()
            .addOnSuccessListener { queryDocumentSnapshots ->
                for (document in queryDocumentSnapshots) {
                    document.reference.delete()
                }
                Log.d(TAG, "All transactions cleared for user: $currentUserId")
                callback.onSuccess(null)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error clearing transactions", e)
                callback.onFailure(e)
            }
    }
}
