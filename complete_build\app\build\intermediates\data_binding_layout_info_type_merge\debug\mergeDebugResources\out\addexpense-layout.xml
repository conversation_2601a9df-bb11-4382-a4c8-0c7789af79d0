<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="addexpense" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\addexpense.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/addexpense_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="273" endOffset="16"/></Target><Target id="@+id/footer" tag="layout/addexpense_0" include="base"><Expressions/><location startLine="253" startOffset="4" endLine="258" endOffset="49"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="261" startOffset="4" endLine="271" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="269" startOffset="8" endLine="269" endOffset="43"/></Target><Target id="@+id/header_layout" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="42" endOffset="18"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="49"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="40" endOffset="52"/></Target><Target id="@+id/category_spinner" view="Spinner"><Expressions/><location startLine="98" startOffset="24" endLine="103" endOffset="52"/></Target><Target id="@+id/editText2" view="EditText"><Expressions/><location startLine="129" startOffset="28" endLine="140" endOffset="57"/></Target><Target id="@+id/editText4" view="EditText"><Expressions/><location startLine="178" startOffset="28" endLine="188" endOffset="58"/></Target><Target id="@+id/editText3" view="TextView"><Expressions/><location startLine="216" startOffset="28" endLine="226" endOffset="67"/></Target><Target id="@+id/date_picker_button" view="Button"><Expressions/><location startLine="228" startOffset="28" endLine="232" endOffset="66"/></Target><Target id="@+id/save_button" view="Button"><Expressions/><location startLine="239" startOffset="20" endLine="242" endOffset="60"/></Target></Targets></Layout>