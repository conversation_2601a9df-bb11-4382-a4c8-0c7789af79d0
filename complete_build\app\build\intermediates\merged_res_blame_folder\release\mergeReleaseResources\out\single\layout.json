[{"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/list_item.xml", "source": "com.example.budgettracker.app-main-61:/layout/list_item.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/content_main.xml", "source": "com.example.budgettracker.app-main-61:/layout/content_main.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/item_notification.xml", "source": "com.example.budgettracker.app-main-61:/layout/item_notification.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/login.xml", "source": "com.example.budgettracker.app-main-61:/layout/login.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/activity_main.xml", "source": "com.example.budgettracker.app-main-61:/layout/activity_main.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/addexpense.xml", "source": "com.example.budgettracker.app-main-61:/layout/addexpense.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/base.xml", "source": "com.example.budgettracker.app-main-61:/layout/base.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/addtransaction.xml", "source": "com.example.budgettracker.app-main-61:/layout/addtransaction.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/language.xml", "source": "com.example.budgettracker.app-main-61:/layout/language.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/register.xml", "source": "com.example.budgettracker.app-main-61:/layout/register.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/notifications.xml", "source": "com.example.budgettracker.app-main-61:/layout/notifications.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/spinner_item.xml", "source": "com.example.budgettracker.app-main-61:/layout/spinner_item.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/list_bug.xml", "source": "com.example.budgettracker.app-main-61:/layout/list_bug.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/settings.xml", "source": "com.example.budgettracker.app-main-61:/layout/settings.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/dashboard.xml", "source": "com.example.budgettracker.app-main-61:/layout/dashboard.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-59:/layout/spinner.xml", "source": "com.example.budgettracker.app-main-61:/layout/spinner.xml"}]