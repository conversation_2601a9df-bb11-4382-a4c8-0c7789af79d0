<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_notification" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\item_notification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_notification_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="75" endOffset="35"/></Target><Target id="@+id/notification_icon_background" view="LinearLayout"><Expressions/><location startLine="16" startOffset="8" endLine="32" endOffset="22"/></Target><Target id="@+id/notification_icon" view="ImageView"><Expressions/><location startLine="24" startOffset="12" endLine="30" endOffset="45"/></Target><Target id="@+id/notification_title" view="TextView"><Expressions/><location startLine="40" startOffset="12" endLine="49" endOffset="51"/></Target><Target id="@+id/notification_message" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="60" endOffset="41"/></Target><Target id="@+id/notification_timestamp" view="TextView"><Expressions/><location startLine="62" startOffset="12" endLine="69" endOffset="48"/></Target></Targets></Layout>