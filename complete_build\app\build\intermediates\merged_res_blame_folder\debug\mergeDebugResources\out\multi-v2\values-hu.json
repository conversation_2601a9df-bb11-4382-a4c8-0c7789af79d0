{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-58:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,14604", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,14683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,120", "endOffsets": "160,281"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3067,3177", "endColumns": "109,120", "endOffsets": "3172,3293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4309,4420,4604,4742,4851,5019,5157,5279,5566,5736,5844,6029,6166,6338,6510,6581,6649", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "4415,4599,4737,4846,5014,5152,5274,5384,5731,5839,6024,6161,6333,6505,6576,6644,6732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1033,1132,1207,1266,1376,1438,1507,1565,1637,1698,1753,1856,1913,1973,2028,2109,2229,2312,2400,2535,2618,2698,2838,2932,3014,3067,3118,3184,3260,3342,3428,3512,3589,3664,3743,3820,3925,4021,4098,4190,4287,4361,4446,4543,4595,4678,4745,4833,4920,4982,5046,5109,5175,5273,5379,5473,5580,5637,5692", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "258,339,415,492,582,662,761,881,964,1028,1127,1202,1261,1371,1433,1502,1560,1632,1693,1748,1851,1908,1968,2023,2104,2224,2307,2395,2530,2613,2693,2833,2927,3009,3062,3113,3179,3255,3337,3423,3507,3584,3659,3738,3815,3920,4016,4093,4185,4282,4356,4441,4538,4590,4673,4740,4828,4915,4977,5041,5104,5170,5268,5374,5468,5575,5632,5687,5772"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3603,3684,3760,3837,3927,4007,4106,4226,7036,7100,9583,9658,9717,9827,9889,9958,10016,10088,10149,10204,10307,10364,10424,10479,10560,10680,10763,10851,10986,11069,11149,11289,11383,11465,11518,11569,11635,11711,11793,11879,11963,12040,12115,12194,12271,12376,12472,12549,12641,12738,12812,12897,12994,13046,13129,13196,13284,13371,13433,13497,13560,13626,13724,13830,13924,14031,14088,14375", "endLines": "5,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "308,3679,3755,3832,3922,4002,4101,4221,4304,7095,7194,9653,9712,9822,9884,9953,10011,10083,10144,10199,10302,10359,10419,10474,10555,10675,10758,10846,10981,11064,11144,11284,11378,11460,11513,11564,11630,11706,11788,11874,11958,12035,12110,12189,12266,12371,12467,12544,12636,12733,12807,12892,12989,13041,13124,13191,13279,13366,13428,13492,13555,13621,13719,13825,13919,14026,14083,14138,14455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6848,7493,7594,7709", "endColumns": "95,100,114,103", "endOffsets": "6939,7589,7704,7808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "14688", "endColumns": "100", "endOffsets": "14784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,360,471,563,732,857,968,1095,1249,1393,1520,1649,1808,1911,2081,2205,2346,2497,2627,2771,2880,3021,3123,3257,3360,3495", "endColumns": "175,128,110,91,168,124,110,126,153,143,126,128,158,102,169,123,140,150,129,143,108,140,101,133,102,134,102", "endOffsets": "226,355,466,558,727,852,963,1090,1244,1388,1515,1644,1803,1906,2076,2200,2341,2492,2622,2766,2875,3016,3118,3252,3355,3490,3593"}, "to": {"startLines": "35,36,63,65,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,146,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3298,3474,6737,6944,7199,7368,7813,7924,8051,8205,8349,8476,8605,8764,8867,9037,9161,9302,9453,14460,14789,14898,15039,15141,15275,15378,15513", "endColumns": "175,128,110,91,168,124,110,126,153,143,126,128,158,102,169,123,140,150,129,143,108,140,101,133,102,134,102", "endOffsets": "3469,3598,6843,7031,7363,7488,7919,8046,8200,8344,8471,8600,8759,8862,9032,9156,9297,9448,9578,14599,14893,15034,15136,15270,15373,15508,15611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5389", "endColumns": "176", "endOffsets": "5561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "14143,14255", "endColumns": "111,119", "endOffsets": "14250,14370"}}]}]}