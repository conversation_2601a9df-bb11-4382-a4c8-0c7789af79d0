R_DEF: Internal format may change without notice
local
anim item_animation_fall_down
array categories_array
color back_ground
color black
color border_color
color md_theme_light_background
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_inverseOnSurface
color md_theme_light_inversePrimary
color md_theme_light_inverseSurface
color md_theme_light_onBackground
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_outlineVariant
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_scrim
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_surface
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color onPrimary
color primary
color primary_blue
color primary_light
color primary_variant
color radio_dot_color
color scrim_overlay
color steelblue
color text_color
color white
dimen fab_margin
drawable attach_money
drawable back
drawable backg_button
drawable button_background
drawable button_danger_background
drawable button_ripple
drawable card_background
drawable chip_background
drawable circle_background
drawable credit_card
drawable edittext_background
drawable expand_more
drawable eye_close
drawable eye_icon
drawable fab_label_background
drawable fingerprint
drawable gradient_primary_background
drawable home
drawable ic_add
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable icon_button_background
drawable logo
drawable logos
drawable logout
drawable mail_24px
drawable medium_button_background
drawable modern_button_background
drawable monitoring
drawable nav_button_background
drawable notification_info_background
drawable notification_success_background
drawable notification_warning_background
drawable notifications
drawable person
drawable search
drawable settings_24px
drawable shopping
drawable small_button_background
drawable small_chip_background
drawable small_chip_selected_background
drawable spinner_background
drawable spinner_normal
drawable spinner_press
drawable spinner_select
drawable star_24px
drawable transaction_icon_background
drawable transaction_type_chip
drawable warning_24px
drawable work_24px
id FirstFragment
id action_FirstFragment_to_SecondFragment
id action_settings
id app_logo
id back_button
id balance_vs_budget
id biometric_card
id btn_1d
id btn_1m
id btn_1w
id btn_1y
id btn_3m
id budget_id
id budget_info
id buttonClearUserData
id buttonLanguageSettings
id button_expenses
id button_home
id button_logout
id button_notification
id button_profile
id button_transactions
id category_spinner
id clear_all_button
id closeIcon
id date_picker_button
id description
id editText2
id editText3
id editText4
id editText5
id email_container
id email_input
id empty_state
id empty_transactions_message
id expand_button
id expandable_details
id fab
id fab_add_budget
id fab_add_expense
id fab_add_transaction
id fab_label_budget
id fab_label_expense
id fingerprint_icon
id fingerprint_instead
id firstTextView
id footer
id graph
id header_layout
id header_section
id itemAmount
id itemCategory
id itemDate
id itemFullDescription
id itemName
id list_icon
id loading_spinner
id loading_text
id login_button
id nav_graph
id nav_host_fragment_content_main
id notification_icon
id notification_icon_background
id notification_message
id notification_timestamp
id notification_title
id notifications_recycler_view
id or_login_with
id password_container
id password_container_confrim
id password_eye
id password_eye_confrim
id password_input
id password_input_confrim
id profile_section
id radio_afrikaans
id radio_english
id radio_group
id radio_zulu
id recyclerView
id register_logo
id root_layout
id save_button
id secondTextView
id signup_button
id signup_instead
id spending_vs_target
id spinner_container
id textView
id title
id title_date
id toolbar
id total_expenditure
id trans_balance
id trans_budget
id trans_target
id username_container
id username_input
id welcome_icon
layout activity_main
layout addexpense
layout addtransaction
layout base
layout content_main
layout dashboard
layout item_notification
layout language
layout list_bug
layout list_item
layout login
layout notifications
layout register
layout settings
layout spinner
layout spinner_item
menu bottom_navigation_menu
menu menu_main
mipmap ic_launcher
mipmap ic_launcher_round
mipmap logo
mipmap logos
navigation nav_graph
string _0
string _0_00
string _12
string _1d
string _1m
string _1w
string _1y
string _3m
string _5_943
string account_balance
string action_settings
string add
string add_a_new_expense
string add_a_note
string add_expense
string add_money
string afrikaans
string app_name
string back_description
string balance
string budget
string budget_alert
string budget_info
string budgethero
string bug_amount
string chart_description
string clear_all
string clear_user_data
string currency_icon
string date_x
string english
string enter_email
string enter_password
string exp_amount
string exp_date
string expense_description
string ext_name
string fingerprint_icon_description
string first_fragment_label
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string heading_list_dash
string home_description
string language
string language_settings
string last_30_days
string log_in
string logo_name
string main_cash
string meta
string meta_platforms
string month
string name_bug
string next
string notifications
string or_log_in_with
string pick_date
string previous
string profile_description
string project_id
string save
string search_description
string second_fragment_label
string select_category
string set
string set_new_target
string settings
string sign_up
string target
string today
string todo
string touch_id
string transaction
string transaction_description
string type
string user_name
string user_profile
string view_all
string warning_icon
string you_ve_received_25_from_sarah
string you_ve_spent_70_of_your_budget_for_the_month
string your_personal_budget_tracker
string zulu
style AppTheme
style App.Widget.BottomNavigationView.ActiveIndicator
style Base.Theme.BudgetTracker
style IconButton
style MediumButton
style SmallButton
xml backup_rules
xml data_extraction_rules
