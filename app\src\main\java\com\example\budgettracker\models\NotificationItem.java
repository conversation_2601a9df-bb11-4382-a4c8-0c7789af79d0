package com.example.budgettracker.models;

public class NotificationItem {
    public enum NotificationType {
        BUDGET_WARNING,
        BUDGET_EXCEEDED,
        GOAL_ACHIEVED,
        TRANSACTION_ADDED,
        EXPENSE_ADDED,
        LOW_BALANCE
    }

    private String id; // Firebase document ID
    private String title;
    private String message;
    private String timestamp;
    private NotificationType type;
    private boolean isRead;
    private String amount;

    public NotificationItem(String title, String message, String timestamp, NotificationType type) {
        this.title = title;
        this.message = message;
        this.timestamp = timestamp;
        this.type = type;
        this.isRead = false;
    }

    public NotificationItem(String title, String message, String timestamp, NotificationType type, String amount) {
        this.title = title;
        this.message = message;
        this.timestamp = timestamp;
        this.type = type;
        this.amount = amount;
        this.isRead = false;
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public NotificationType getType() {
        return type;
    }

    public void setType(NotificationType type) {
        this.type = type;
    }

    public boolean isRead() {
        return isRead;
    }

    public void setRead(boolean read) {
        isRead = read;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }
}
