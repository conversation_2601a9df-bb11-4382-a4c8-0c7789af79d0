package com.example.budgettracker

import android.content.Context
import androidx.work.PeriodicWorkRequest
import androidx.work.WorkManager
import java.util.concurrent.TimeUnit

object SyncScheduler {
    fun scheduleSync(context: Context) {
//        val syncRequest = PeriodicWorkRequest.Builder(
//            SyncWorker::class.java,
//            15,
//            TimeUnit.MINUTES
//        ).build()

//        WorkManager.getInstance(context).enqueue(syncRequest)
    }
}
