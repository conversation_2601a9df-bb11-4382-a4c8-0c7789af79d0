package com.example.budgettracker

import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.util.Log

class SyncService(private val context: Context) {
    companion object {
        private const val TAG = "SyncService"
    }
    
    private val dbHelper = DatabaseHelper(context)
    private val firebaseManager = FirebaseManager()

    interface SyncCallback {
        fun onSyncComplete(success: <PERSON><PERSON><PERSON>, message: String)
    }

    fun syncLocalDataToFirebase(callback: SyncCallback) {
        if (!firebaseManager.isUserAuthenticated()) {
            callback.onSyncComplete(false, "User not authenticated")
            return
        }

        Log.d(TAG, "Starting sync of local data to Firebase")
        
        // Sync expenses first, then transactions
        syncExpensesToFirebase(object : SyncCallback {
            override fun onSyncComplete(success: <PERSON><PERSON><PERSON>, message: String) {
                if (success) {
                    syncTransactionsToFirebase(callback)
                } else {
                    callback.onSyncComplete(false, "Failed to sync expenses: $message")
                }
            }
        })
    }

    private fun syncExpensesToFirebase(callback: SyncCallback) {
        val db = dbHelper.readableDatabase
        
        // Get all unsynced expenses (we'll add a sync flag later)
        val query = "SELECT name, note, date, amount, user_id FROM expenses WHERE synced = 0 OR synced IS NULL"
        val cursor = db.rawQuery(query, null)
        
        val unsyncedExpenses = mutableListOf<ExpenseData>()
        
        while (cursor.moveToNext()) {
            val expense = ExpenseData().apply {
                name = cursor.getString(0)
                note = cursor.getString(1)
                date = cursor.getString(2)
                amount = cursor.getInt(3)
                userId = cursor.getString(4)
            }
            unsyncedExpenses.add(expense)
        }
        cursor.close()
        
        if (unsyncedExpenses.isEmpty()) {
            Log.d(TAG, "No expenses to sync")
            callback.onSyncComplete(true, "No expenses to sync")
            return
        }

        Log.d(TAG, "Syncing ${unsyncedExpenses.size} expenses")
        syncExpensesBatch(unsyncedExpenses, 0, callback)
    }

    private fun syncExpensesBatch(expenses: List<ExpenseData>, index: Int, callback: SyncCallback) {
        if (index >= expenses.size) {
            // All expenses synced successfully
            markExpensesAsSynced()
            callback.onSyncComplete(true, "All expenses synced successfully")
            return
        }

        val expense = expenses[index]
        firebaseManager.saveExpense(expense.name, expense.note, expense.date, expense.amount, 
            object : FirebaseManager.FirebaseCallback<Void> {
                override fun onSuccess(result: Void?) {
                    Log.d(TAG, "Synced expense: ${expense.name}")
                    // Continue with next expense
                    syncExpensesBatch(expenses, index + 1, callback)
                }

                override fun onFailure(e: Exception) {
                    Log.e(TAG, "Failed to sync expense: ${expense.name}", e)
                    callback.onSyncComplete(false, "Failed to sync expense: ${expense.name}")
                }
            })
    }

    private fun syncTransactionsToFirebase(callback: SyncCallback) {
        val db = dbHelper.readableDatabase
        
        // Get all unsynced transactions
        val query = "SELECT bud_name, bud_date, bud_amount, user_id FROM budget WHERE synced = 0 OR synced IS NULL"
        val cursor = db.rawQuery(query, null)
        
        val unsyncedTransactions = mutableListOf<TransactionData>()
        
        while (cursor.moveToNext()) {
            val transaction = TransactionData().apply {
                name = cursor.getString(0)
                date = cursor.getString(1)
                amount = cursor.getInt(2)
                userId = cursor.getString(3)
            }
            unsyncedTransactions.add(transaction)
        }
        cursor.close()
        
        if (unsyncedTransactions.isEmpty()) {
            Log.d(TAG, "No transactions to sync")
            callback.onSyncComplete(true, "Sync completed successfully")
            return
        }

        Log.d(TAG, "Syncing ${unsyncedTransactions.size} transactions")
        syncTransactionsBatch(unsyncedTransactions, 0, callback)
    }

    private fun syncTransactionsBatch(transactions: List<TransactionData>, index: Int, callback: SyncCallback) {
        if (index >= transactions.size) {
            // All transactions synced successfully
            markTransactionsAsSynced()
            callback.onSyncComplete(true, "Sync completed successfully")
            
            // Show notification
            NotificationHelper.sendNotification(context, "Sync Complete", 
                "All local data has been synced to the cloud")
            return
        }

        val transaction = transactions[index]
        firebaseManager.saveTransaction(transaction.name, transaction.date, transaction.amount, 
            object : FirebaseManager.FirebaseCallback<Void> {
                override fun onSuccess(result: Void?) {
                    Log.d(TAG, "Synced transaction: ${transaction.name}")
                    // Continue with next transaction
                    syncTransactionsBatch(transactions, index + 1, callback)
                }

                override fun onFailure(e: Exception) {
                    Log.e(TAG, "Failed to sync transaction: ${transaction.name}", e)
                    callback.onSyncComplete(false, "Failed to sync transaction: ${transaction.name}")
                }
            })
    }

    private fun markExpensesAsSynced() {
        val db = dbHelper.writableDatabase
        db.execSQL("UPDATE expenses SET synced = 1 WHERE synced = 0 OR synced IS NULL")
        db.close()
    }

    private fun markTransactionsAsSynced() {
        val db = dbHelper.writableDatabase
        db.execSQL("UPDATE budget SET synced = 1 WHERE synced = 0 OR synced IS NULL")
        db.close()
    }

    // Helper classes for data transfer
    private class ExpenseData {
        lateinit var name: String
        lateinit var note: String
        lateinit var date: String
        lateinit var userId: String
        var amount: Int = 0
    }

    private class TransactionData {
        lateinit var name: String
        lateinit var date: String
        lateinit var userId: String
        var amount: Int = 0
    }
}
