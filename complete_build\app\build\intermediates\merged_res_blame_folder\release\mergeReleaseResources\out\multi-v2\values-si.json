{"logs": [{"outputFile": "com.example.budgettracker.app-mergeReleaseResources-58:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,14027", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,14104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,116", "endOffsets": "161,278"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "13594,13705", "endColumns": "110,116", "endOffsets": "13700,13817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,113", "endOffsets": "160,274"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3035,3145", "endColumns": "109,113", "endOffsets": "3140,3254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1040,1129,1196,1256,1350,1414,1477,1533,1603,1670,1725,1844,1901,1965,2019,2092,2214,2297,2382,2514,2592,2672,2794,2880,2964,3024,3076,3142,3212,3285,3367,3444,3516,3593,3665,3735,3848,3941,4014,4104,4197,4271,4343,4434,4488,4568,4634,4718,4803,4865,4929,4992,5058,5163,5268,5363,5464,5528,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,121,85,83,59,51,65,69,72,81,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79", "endOffsets": "264,340,417,495,586,671,773,888,971,1035,1124,1191,1251,1345,1409,1472,1528,1598,1665,1720,1839,1896,1960,2014,2087,2209,2292,2377,2509,2587,2667,2789,2875,2959,3019,3071,3137,3207,3280,3362,3439,3511,3588,3660,3730,3843,3936,4009,4099,4192,4266,4338,4429,4483,4563,4629,4713,4798,4860,4924,4987,5053,5158,5263,5358,5459,5523,5579,5659"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3530,3606,3683,3761,3852,3937,4039,4154,6738,6802,9139,9206,9266,9360,9424,9487,9543,9613,9680,9735,9854,9911,9975,10029,10102,10224,10307,10392,10524,10602,10682,10804,10890,10974,11034,11086,11152,11222,11295,11377,11454,11526,11603,11675,11745,11858,11951,12024,12114,12207,12281,12353,12444,12498,12578,12644,12728,12813,12875,12939,13002,13068,13173,13278,13373,13474,13538,13822", "endLines": "5,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,121,85,83,59,51,65,69,72,81,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79", "endOffsets": "314,3601,3678,3756,3847,3932,4034,4149,4232,6797,6886,9201,9261,9355,9419,9482,9538,9608,9675,9730,9849,9906,9970,10024,10097,10219,10302,10387,10519,10597,10677,10799,10885,10969,11029,11081,11147,11217,11290,11372,11449,11521,11598,11670,11740,11853,11946,12019,12109,12202,12276,12348,12439,12493,12573,12639,12723,12808,12870,12934,12997,13063,13168,13273,13368,13469,13533,13589,13897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6539,7147,7254,7370", "endColumns": "107,106,115,104", "endOffsets": "6642,7249,7365,7470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,326,438,529,671,785,900,1024,1150,1282,1409,1531,1672,1775,1929,2056,2185,2320,2449,2574,2673,2799,2890,3008,3112,3243", "endColumns": "148,121,111,90,141,113,114,123,125,131,126,121,140,102,153,126,128,134,128,124,98,125,90,117,103,130,100", "endOffsets": "199,321,433,524,666,780,895,1019,1145,1277,1404,1526,1667,1770,1924,2051,2180,2315,2444,2569,2668,2794,2885,3003,3107,3238,3339"}, "to": {"startLines": "35,36,63,65,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,146,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3259,3408,6427,6647,6891,7033,7475,7590,7714,7840,7972,8099,8221,8362,8465,8619,8746,8875,9010,13902,14210,14309,14435,14526,14644,14748,14879", "endColumns": "148,121,111,90,141,113,114,123,125,131,126,121,140,102,153,126,128,134,128,124,98,125,90,117,103,130,100", "endOffsets": "3403,3525,6534,6733,7028,7142,7585,7709,7835,7967,8094,8216,8357,8460,8614,8741,8870,9005,9134,14022,14304,14430,14521,14639,14743,14874,14975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4237,4345,4499,4623,4736,4878,5002,5118,5355,5506,5621,5777,5908,6052,6213,6286,6347", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "4340,4494,4618,4731,4873,4997,5113,5211,5501,5616,5772,5903,6047,6208,6281,6342,6422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5216", "endColumns": "138", "endOffsets": "5350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "14109", "endColumns": "100", "endOffsets": "14205"}}]}]}