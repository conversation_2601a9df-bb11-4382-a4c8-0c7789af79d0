<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface">

    <!-- Header with modern styling -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/app_logo"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/logo"
                android:scaleType="fitCenter"
                android:layout_marginEnd="12dp" />

            <TextView
                android:id="@+id/firstTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/logo_name"
                android:textColor="?attr/colorPrimary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

        <ImageButton
            android:id="@+id/button_notification"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginEnd="8dp"
            android:contentDescription="@string/notifications"
            android:src="@drawable/notifications"
            android:background="@drawable/button_background"
            android:scaleType="fitCenter"
            app:tint="?attr/colorOnSurface" />

        <TextView
            android:id="@+id/secondTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="@string/user_name"
            android:textColor="?attr/colorOnSurface"
            android:textSize="14sp"
            android:fontFamily="sans-serif-medium" />

        <ImageButton
            android:id="@+id/button_logout"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:contentDescription="@string/notifications"
            android:src="@drawable/logout"
            android:background="@drawable/button_background"
            android:scaleType="fitCenter"
            app:tint="?attr/colorOnSurface" />

    </LinearLayout>

    <!-- Main content with scroll view -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_layout"
        android:layout_above="@id/footer"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Balance Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/gradient_primary_background"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/account_balance"
                        android:textColor="?attr/colorOnPrimary"
                        android:textSize="14sp"
                        android:fontFamily="sans-serif-medium"
                        android:alpha="0.87" />

                    <TextView
                        android:id="@+id/budget_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/_0"
                        android:textColor="?attr/colorOnPrimary"
                        android:textSize="36sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif" />

                    <!-- Spending vs Target -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Spending vs Target:"
                            android:textColor="?attr/colorOnPrimary"
                            android:textSize="12sp"
                            android:layout_marginEnd="8dp"
                            android:alpha="0.87" />

                        <TextView
                            android:id="@+id/spending_vs_target"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0% of target spent"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:background="@drawable/chip_background"
                            android:padding="8dp" />

                    </LinearLayout>

                    <!-- Balance vs Budget -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Balance vs Budget:"
                            android:textColor="?attr/colorOnPrimary"
                            android:textSize="12sp"
                            android:layout_marginEnd="8dp"
                            android:alpha="0.87" />

                        <TextView
                            android:id="@+id/balance_vs_budget"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0% remaining"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:background="@drawable/chip_background"
                            android:padding="8dp" />

                    </LinearLayout>

                    <!-- Total Expenditure -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total Spent:"
                            android:textColor="?attr/colorOnPrimary"
                            android:textSize="12sp"
                            android:layout_marginEnd="8dp"
                            android:alpha="0.87" />

                        <TextView
                            android:id="@+id/total_expenditure"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$0"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:background="@drawable/chip_background"
                            android:padding="8dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Chart Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Spending Overview"
                        android:textColor="?attr/colorOnSurface"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginBottom="16dp" />

                    <com.jjoe64.graphview.GraphView
                        android:id="@+id/graph"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="16dp" />

                    <!-- Time period buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btn_1d"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:layout_margin="3dp"
                            android:background="@drawable/small_chip_background"
                            android:minWidth="48dp"
                            android:paddingStart="10dp"
                            android:paddingEnd="10dp"
                            android:text="@string/_1d"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="8sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/btn_1w"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:layout_margin="3dp"
                            android:minWidth="48dp"
                            android:text="@string/_1w"
                            android:textSize="8sp"
                            android:textStyle="bold"
                            android:background="@drawable/small_chip_background"
                            android:textColor="?attr/colorOnSurface"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />

                        <Button
                            android:id="@+id/btn_1m"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:layout_margin="3dp"
                            android:minWidth="48dp"
                            android:text="@string/_1m"
                            android:textSize="8sp"
                            android:textStyle="bold"
                            android:background="@drawable/small_chip_selected_background"
                            android:textColor="?attr/colorOnPrimary"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />

                        <Button
                            android:id="@+id/btn_3m"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:layout_margin="3dp"
                            android:minWidth="48dp"
                            android:text="@string/_3m"
                            android:textSize="8sp"
                            android:textStyle="bold"
                            android:background="@drawable/small_chip_background"
                            android:textColor="?attr/colorOnSurface"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />

                        <Button
                            android:id="@+id/btn_1y"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:layout_margin="3dp"
                            android:minWidth="48dp"
                            android:text="@string/_1y"
                            android:textSize="8sp"
                            android:textStyle="bold"
                            android:background="@drawable/small_chip_background"
                            android:textColor="?attr/colorOnSurface"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>


            <!-- Transactions Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="80dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:id="@+id/itemName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/heading_list_dash"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium" />

                        <Button
                            android:layout_width="wrap_content"
                            android:layout_height="35dp"
                            android:background="@drawable/small_chip_background"
                            android:fontFamily="sans-serif-medium"
                            android:gravity="center"
                            android:letterSpacing="0.02"
                            android:text="@string/view_all"
                            android:textColor="@color/white"
                            android:textSize="9sp"
                            android:textStyle="bold" />


                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Root layout for loading spinner -->
            <RelativeLayout
                android:id="@+id/root_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </ScrollView>

    <!-- Modern Bottom Navigation -->
    <include
        android:id="@+id/footer"
        layout="@layout/base"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

    <!-- Floating Action Button Group -->
    <!-- Label for Add Expense -->
    <TextView
        android:id="@+id/fab_label_expense"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@id/fab_add_expense"
        android:layout_alignTop="@id/fab_add_expense"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="6dp"
        android:text="Add Expense"
        android:textColor="?attr/colorOnSurface"
        android:textSize="12sp"
        android:textStyle="bold"
        android:background="@drawable/fab_label_background"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:elevation="4dp"
        android:visibility="gone" />

    <!-- Sub FAB for Add Expense -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_expense"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_above="@id/footer"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="160dp"
        android:contentDescription="Add Expense"
        android:src="@drawable/attach_money"
        android:backgroundTint="?attr/colorSecondary"
        android:tint="?attr/colorOnSecondary"
        android:elevation="6dp"
        android:visibility="gone"
        app:borderWidth="0dp"
        app:fabSize="mini" />

    <!-- Label for Add Transaction -->
    <TextView
        android:id="@+id/fab_label_budget"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@id/fab_add_budget"
        android:layout_alignTop="@id/fab_add_budget"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="6dp"
        android:text="Add Transaction"
        android:textColor="?attr/colorOnSurface"
        android:textSize="12sp"
        android:textStyle="bold"
        android:background="@drawable/fab_label_background"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:elevation="4dp"
        android:visibility="gone" />

    <!-- Sub FAB for Add Transaction -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_budget"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_above="@id/footer"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="110dp"
        android:contentDescription="Add Budget"
        android:src="@drawable/credit_card"
        android:backgroundTint="?attr/colorTertiary"
        android:tint="?attr/colorOnTertiary"
        android:elevation="6dp"
        android:visibility="gone"
        app:borderWidth="0dp"
        app:fabSize="mini" />

    <!-- Main Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_transaction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_above="@id/footer"
        android:layout_margin="16dp"
        android:contentDescription="Add New Entry"
        android:src="@drawable/ic_add"
        android:backgroundTint="?attr/colorPrimary"
        android:tint="?attr/colorOnPrimary"
        android:elevation="8dp"
        app:borderWidth="0dp"
        app:fabSize="normal" />

    <!-- Loading Spinner Overlay -->
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="@color/scrim_overlay">

        <include layout="@layout/spinner" />

    </RelativeLayout>

</RelativeLayout>
