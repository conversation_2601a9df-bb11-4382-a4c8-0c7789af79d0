// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNotificationBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView notificationIcon;

  @NonNull
  public final LinearLayout notificationIconBackground;

  @NonNull
  public final TextView notificationMessage;

  @NonNull
  public final TextView notificationTimestamp;

  @NonNull
  public final TextView notificationTitle;

  private ItemNotificationBinding(@NonNull CardView rootView, @NonNull ImageView notificationIcon,
      @NonNull LinearLayout notificationIconBackground, @NonNull TextView notificationMessage,
      @NonNull TextView notificationTimestamp, @NonNull TextView notificationTitle) {
    this.rootView = rootView;
    this.notificationIcon = notificationIcon;
    this.notificationIconBackground = notificationIconBackground;
    this.notificationMessage = notificationMessage;
    this.notificationTimestamp = notificationTimestamp;
    this.notificationTitle = notificationTitle;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_notification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNotificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.notification_icon;
      ImageView notificationIcon = ViewBindings.findChildViewById(rootView, id);
      if (notificationIcon == null) {
        break missingId;
      }

      id = R.id.notification_icon_background;
      LinearLayout notificationIconBackground = ViewBindings.findChildViewById(rootView, id);
      if (notificationIconBackground == null) {
        break missingId;
      }

      id = R.id.notification_message;
      TextView notificationMessage = ViewBindings.findChildViewById(rootView, id);
      if (notificationMessage == null) {
        break missingId;
      }

      id = R.id.notification_timestamp;
      TextView notificationTimestamp = ViewBindings.findChildViewById(rootView, id);
      if (notificationTimestamp == null) {
        break missingId;
      }

      id = R.id.notification_title;
      TextView notificationTitle = ViewBindings.findChildViewById(rootView, id);
      if (notificationTitle == null) {
        break missingId;
      }

      return new ItemNotificationBinding((CardView) rootView, notificationIcon,
          notificationIconBackground, notificationMessage, notificationTimestamp,
          notificationTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
