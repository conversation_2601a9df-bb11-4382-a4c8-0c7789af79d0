package com.example.budgettracker.models

class NotificationItem {
    enum class NotificationType {
        BUDGET_WARNING,
        BUDGET_EXCEEDED,
        GOAL_ACHIEVED,
        TRANSACTION_ADDED,
        EXPENSE_ADDED,
        LOW_BALANCE
    }

    private var id: String? = null // Firebase document ID
    private var title: String
    private var message: String
    private var timestamp: String
    private var type: NotificationType
    private var isRead: Boolean = false
    private var amount: String? = null

    constructor(title: String, message: String, timestamp: String, type: NotificationType) {
        this.title = title
        this.message = message
        this.timestamp = timestamp
        this.type = type
        this.isRead = false
    }

    constructor(title: String, message: String, timestamp: String, type: NotificationType, amount: String) {
        this.title = title
        this.message = message
        this.timestamp = timestamp
        this.type = type
        this.amount = amount
        this.isRead = false
    }

    // Getters and setters
    fun getId(): String? {
        return id
    }

    fun setId(id: String?) {
        this.id = id
    }

    fun getTitle(): String {
        return title
    }

    fun setTitle(title: String) {
        this.title = title
    }

    fun getMessage(): String {
        return message
    }

    fun setMessage(message: String) {
        this.message = message
    }

    fun getTimestamp(): String {
        return timestamp
    }

    fun setTimestamp(timestamp: String) {
        this.timestamp = timestamp
    }

    fun getType(): NotificationType {
        return type
    }

    fun setType(type: NotificationType) {
        this.type = type
    }

    fun isRead(): Boolean {
        return isRead
    }

    fun setRead(read: Boolean) {
        isRead = read
    }

    fun getAmount(): String? {
        return amount
    }

    fun setAmount(amount: String?) {
        this.amount = amount
    }
}
