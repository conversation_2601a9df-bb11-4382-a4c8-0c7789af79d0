{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-58:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6615,7224,7326,7435", "endColumns": "105,101,108,105", "endOffsets": "6716,7321,7430,7536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5312", "endColumns": "151", "endOffsets": "5459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,111", "endOffsets": "164,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3051,3165", "endColumns": "113,111", "endOffsets": "3160,3272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1106,1197,1263,1324,1414,1478,1545,1606,1675,1737,1791,1898,1957,2018,2072,2146,2266,2351,2435,2570,2641,2711,2843,2930,3013,3071,3127,3193,3266,3346,3441,3523,3592,3668,3748,3817,3926,4021,4104,4194,4289,4363,4437,4530,4584,4669,4736,4822,4907,4969,5033,5096,5162,5264,5363,5456,5555,5617,5677", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "266,364,457,540,641,733,837,954,1035,1101,1192,1258,1319,1409,1473,1540,1601,1670,1732,1786,1893,1952,2013,2067,2141,2261,2346,2430,2565,2636,2706,2838,2925,3008,3066,3122,3188,3261,3341,3436,3518,3587,3663,3743,3812,3921,4016,4099,4189,4284,4358,4432,4525,4579,4664,4731,4817,4902,4964,5028,5091,5157,5259,5358,5451,5550,5612,5672,5752"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3562,3660,3753,3836,3937,4029,4133,4250,6810,6876,9253,9319,9380,9470,9534,9601,9662,9731,9793,9847,9954,10013,10074,10128,10202,10322,10407,10491,10626,10697,10767,10899,10986,11069,11127,11183,11249,11322,11402,11497,11579,11648,11724,11804,11873,11982,12077,12160,12250,12345,12419,12493,12586,12640,12725,12792,12878,12963,13025,13089,13152,13218,13320,13419,13512,13611,13673,13962", "endLines": "5,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "316,3655,3748,3831,3932,4024,4128,4245,4326,6871,6962,9314,9375,9465,9529,9596,9657,9726,9788,9842,9949,10008,10069,10123,10197,10317,10402,10486,10621,10692,10762,10894,10981,11064,11122,11178,11244,11317,11397,11492,11574,11643,11719,11799,11868,11977,12072,12155,12245,12340,12414,12488,12581,12635,12720,12787,12873,12958,13020,13084,13147,13213,13315,13414,13507,13606,13668,13728,14037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4331,4440,4599,4727,4838,4974,5096,5208,5464,5607,5716,5872,6000,6133,6281,6341,6408", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "4435,4594,4722,4833,4969,5091,5203,5307,5602,5711,5867,5995,6128,6276,6336,6403,6493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "14248", "endColumns": "100", "endOffsets": "14344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,340,457,546,689,803,917,1038,1177,1311,1440,1564,1711,1814,1971,2099,2236,2381,2515,2634,2740,2875,2965,3084,3188,3321", "endColumns": "156,127,116,88,142,113,113,120,138,133,128,123,146,102,156,127,136,144,133,118,105,134,89,118,103,132,103", "endOffsets": "207,335,452,541,684,798,912,1033,1172,1306,1435,1559,1706,1809,1966,2094,2231,2376,2510,2629,2735,2870,2960,3079,3183,3316,3420"}, "to": {"startLines": "35,36,63,65,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,146,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3277,3434,6498,6721,6967,7110,7541,7655,7776,7915,8049,8178,8302,8449,8552,8709,8837,8974,9119,14042,14349,14455,14590,14680,14799,14903,15036", "endColumns": "156,127,116,88,142,113,113,120,138,133,128,123,146,102,156,127,136,144,133,118,105,134,89,118,103,132,103", "endOffsets": "3429,3557,6610,6805,7105,7219,7650,7771,7910,8044,8173,8297,8444,8547,8704,8832,8969,9114,9248,14156,14450,14585,14675,14794,14898,15031,15135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,14161", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,14243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "13733,13845", "endColumns": "111,116", "endOffsets": "13840,13957"}}]}]}