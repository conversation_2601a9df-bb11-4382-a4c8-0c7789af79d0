package com.example.budgettracker

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import java.text.SimpleDateFormat
import java.util.*

class Item2Adapter(private val budgetsList: List<Budgets>) : RecyclerView.Adapter<Item2Adapter.ViewHolder>() {

    private var lastPosition = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.list_bug, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val budget = budgetsList[position]

        // Set transaction name
        val name = budget.getName()
        holder.nameTextView.text = name

        // Format and set date
        val formattedDate = formatDate(budget.getDate())
        holder.dateTextView.text = formattedDate

        // Format and set amount with currency symbol
        holder.amountTextView.text = String.format("+$%d", budget.getAmount())

        // Add click listener for item interaction
        holder.itemView.setOnClickListener { v ->
            val details = String.format(
                "Transaction: %s\nAmount: $%d\nDate: %s",
                name, budget.getAmount(), formattedDate
            )
            Toast.makeText(v.context, details, Toast.LENGTH_SHORT).show()
        }

        // Add long click for additional details
        holder.itemView.setOnLongClickListener { v ->
            val detailedInfo = String.format(
                "Transaction Details:\n• Name: %s\n• Amount: $%d\n• Date: %s\n• Type: Budget Addition",
                name, budget.getAmount(), formattedDate
            )
            Toast.makeText(v.context, detailedInfo, Toast.LENGTH_LONG).show()
            true
        }

        // Add animation
        setAnimation(holder.itemView, position)
    }

    private fun setAnimation(viewToAnimate: View, position: Int) {
        // If the bound view wasn't previously displayed on screen, it's animated
        if (position > lastPosition) {
            val animation = AnimationUtils.loadAnimation(viewToAnimate.context, R.anim.item_animation_fall_down)
            viewToAnimate.startAnimation(animation)
            lastPosition = position
        }
    }

    private fun formatDate(dateString: String): String {
        return try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
            val date = inputFormat.parse(dateString)
            outputFormat.format(date!!)
        } catch (e: Exception) {
            // If parsing fails, return original string
            dateString
        }
    }

    private fun getRelativeTime(dateString: String): String {
        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val date = sdf.parse(dateString)
            val now = Date()

            val diffInMillis = now.time - date!!.time
            val diffInDays = diffInMillis / (24 * 60 * 60 * 1000)

            when {
                diffInDays == 0L -> "Today"
                diffInDays == 1L -> "Yesterday"
                diffInDays < 7 -> "$diffInDays days ago"
                else -> formatDate(dateString)
            }
        } catch (e: Exception) {
            formatDate(dateString)
        }
    }

    override fun getItemCount(): Int {
        return budgetsList.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val nameTextView: TextView = itemView.findViewById(R.id.itemName)
        val dateTextView: TextView = itemView.findViewById(R.id.itemDate)
        val amountTextView: TextView = itemView.findViewById(R.id.itemAmount)
    }
}
