// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class AddexpenseBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final Spinner categorySpinner;

  @NonNull
  public final Button datePickerButton;

  @NonNull
  public final EditText editText2;

  @NonNull
  public final TextView editText3;

  @NonNull
  public final EditText editText4;

  @NonNull
  public final BaseBinding footer;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final Button saveButton;

  @NonNull
  public final RelativeLayout spinnerContainer;

  @NonNull
  public final TextView textView;

  private AddexpenseBinding(@NonNull RelativeLayout rootView, @NonNull ImageButton backButton,
      @NonNull Spinner categorySpinner, @NonNull Button datePickerButton,
      @NonNull EditText editText2, @NonNull TextView editText3, @NonNull EditText editText4,
      @NonNull BaseBinding footer, @NonNull LinearLayout headerLayout,
      @NonNull RelativeLayout rootLayout, @NonNull Button saveButton,
      @NonNull RelativeLayout spinnerContainer, @NonNull TextView textView) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.categorySpinner = categorySpinner;
    this.datePickerButton = datePickerButton;
    this.editText2 = editText2;
    this.editText3 = editText3;
    this.editText4 = editText4;
    this.footer = footer;
    this.headerLayout = headerLayout;
    this.rootLayout = rootLayout;
    this.saveButton = saveButton;
    this.spinnerContainer = spinnerContainer;
    this.textView = textView;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static AddexpenseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AddexpenseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.addexpense, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AddexpenseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.category_spinner;
      Spinner categorySpinner = ViewBindings.findChildViewById(rootView, id);
      if (categorySpinner == null) {
        break missingId;
      }

      id = R.id.date_picker_button;
      Button datePickerButton = ViewBindings.findChildViewById(rootView, id);
      if (datePickerButton == null) {
        break missingId;
      }

      id = R.id.editText2;
      EditText editText2 = ViewBindings.findChildViewById(rootView, id);
      if (editText2 == null) {
        break missingId;
      }

      id = R.id.editText3;
      TextView editText3 = ViewBindings.findChildViewById(rootView, id);
      if (editText3 == null) {
        break missingId;
      }

      id = R.id.editText4;
      EditText editText4 = ViewBindings.findChildViewById(rootView, id);
      if (editText4 == null) {
        break missingId;
      }

      id = R.id.footer;
      View footer = ViewBindings.findChildViewById(rootView, id);
      if (footer == null) {
        break missingId;
      }
      BaseBinding binding_footer = BaseBinding.bind(footer);

      id = R.id.header_layout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      RelativeLayout rootLayout = (RelativeLayout) rootView;

      id = R.id.save_button;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      id = R.id.textView;
      TextView textView = ViewBindings.findChildViewById(rootView, id);
      if (textView == null) {
        break missingId;
      }

      return new AddexpenseBinding((RelativeLayout) rootView, backButton, categorySpinner,
          datePickerButton, editText2, editText3, editText4, binding_footer, headerLayout,
          rootLayout, saveButton, spinnerContainer, textView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
