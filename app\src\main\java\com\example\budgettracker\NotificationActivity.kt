package com.example.budgettracker

import android.annotation.SuppressLint
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ImageButton
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.budgettracker.adapters.NotificationAdapter
import com.example.budgettracker.models.NotificationItem
import java.text.SimpleDateFormat
import java.util.*

class NotificationActivity : AppCompatActivity() {
    
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var notificationsRecyclerView: RecyclerView
    private lateinit var notificationAdapter: NotificationAdapter
    private lateinit var emptyState: LinearLayout
    private lateinit var notifications: MutableList<NotificationItem>
    private lateinit var firebaseManager: FirebaseManager
    lateinit var user_id: String

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid() ?: ""
        }
        setContentView(R.layout.notifications)

        initializeViews()
        setupRecyclerView()
        loadNotifications()

        // Setup navigation buttons
        setupNavigationButtons()

        // Schedule periodic notification checks (only if Firebase is enabled)
        if (AppConfig.shouldUseFirebase()) {
            NotificationScheduler.schedulePeriodicNotificationChecks(this)
        }

        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@NotificationActivity, DashboardActivity::class.java)
            startActivity(intent)
        }

        val clearAllButton = findViewById<Button>(R.id.clear_all_button)
        clearAllButton.setOnClickListener { clearAllNotifications() }
    }

    private fun initializeViews() {
        dbHelper = DatabaseHelper(this)
        firebaseManager = FirebaseManager()
        notificationsRecyclerView = findViewById(R.id.notifications_recycler_view)
        emptyState = findViewById(R.id.empty_state)
        notifications = mutableListOf()
        NotificationHelper.createNotificationChannel(this)
    }

    private fun setupRecyclerView() {
        notificationAdapter = NotificationAdapter(notifications, object : NotificationAdapter.OnNotificationClickListener {
            override fun onNotificationClick(notification: NotificationItem, position: Int) {
                <EMAIL>(notification, position)
            }
        })
        notificationsRecyclerView.layoutManager = LinearLayoutManager(this)
        notificationsRecyclerView.adapter = notificationAdapter
    }

    private fun onNotificationClick(notification: NotificationItem, position: Int) {
        if (!notification.isRead()) {
            notification.setRead(true)
            notificationAdapter.notifyItemChanged(position)

            if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) && 
                firebaseManager.isUserAuthenticated() && notification.getId() != null) {
                firebaseManager.markNotificationAsRead(notification.getId()!!, object : FirebaseManager.FirebaseCallback<Void> {
                    override fun onSuccess(result: Void?) {
                        Log.d("NotificationActivity", "Notification marked as read in Firebase")
                    }

                    override fun onFailure(e: Exception) {
                        Log.e("NotificationActivity", "Failed to mark notification as read in Firebase", e)
                    }
                })
            } else {
                Log.d("NotificationActivity", "Notification marked as read locally")
            }
        }
    }

    private fun loadNotifications() {
        notifications.clear()

        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) && 
            firebaseManager.isUserAuthenticated()) {
            loadNotificationsFromFirebase()
        } else {
            loadNotificationsFromLocal()
        }
    }

    private fun loadNotificationsFromFirebase() {
        firebaseManager.getNotifications(object : FirebaseManager.FirebaseCallback<List<Map<String, Any>>> {
            override fun onSuccess(result: List<Map<String, Any>>?) {
                if (result == null) {
                    Log.e("NotificationActivity", "Notifications result is null")
                    return
                }

                notifications.clear()

                for (notificationData in result) {
                    val id = notificationData["id"] as? String
                    val title = notificationData["title"] as? String ?: ""
                    val message = notificationData["message"] as? String ?: ""
                    val type = notificationData["type"] as? String
                    val timestamp = notificationData["timestamp"] as? String ?: ""
                    val isRead = notificationData["is_read"] as? Boolean ?: false

                    val notificationType = getNotificationTypeFromString(type)
                    val notification = NotificationItem(title, message, getRelativeTime(timestamp), notificationType)
                    notification.setId(id)
                    notification.setRead(isRead)
                    notifications.add(notification)
                }

                // Also generate real-time notifications based on current data
                generateRealTimeNotifications()

                updateUI()
            }

            override fun onFailure(e: Exception) {
                Log.e("NotificationActivity", "Failed to load Firebase notifications", e)
                // Fallback to local notifications
                loadNotificationsFromLocal()
            }
        })
    }

    private fun loadNotificationsFromLocal() {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)

        if (AppConfig.shouldUseFirebase() && firebaseManager.isUserAuthenticated()) {
            generateNotificationsFromDataOnline(monthYear)
        } else {
            generateNotificationsFromData(monthYear)
        }
    }

    private fun generateNotificationsFromData(monthYear: String) {
        val db = dbHelper.readableDatabase

        // Check budget status
        checkBudgetStatus(db, monthYear)

        // Check recent transactions
        checkRecentTransactions(db)

        // Check recent expenses
        checkRecentExpenses(db)

        // Check low balance alerts
        checkLowBalanceLocal(db, monthYear)

        updateUI()
    }

    private fun checkLowBalanceLocal(db: SQLiteDatabase, monthYear: String) {
        val query = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id))

        if (cursor.moveToFirst()) {
            @SuppressLint("Range") 
            val balance = cursor.getDouble(cursor.getColumnIndex("trans_balance"))

            // Define low balance threshold
            val lowBalanceThreshold = 100.0

            if (balance <= lowBalanceThreshold && balance > 0) {
                notifications.add(NotificationItem(
                    "Low Balance Alert",
                    String.format("Your current balance is low: $%.2f. Consider adding funds.", balance),
                    "Today",
                    NotificationItem.NotificationType.LOW_BALANCE
                ))
            }
        }
        cursor.close()
    }

    private fun checkBudgetStatus(db: SQLiteDatabase, monthYear: String) {
        val query = "SELECT trans_balance, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id))

        if (cursor.moveToFirst()) {
            @SuppressLint("Range") 
            val balance = cursor.getDouble(cursor.getColumnIndex("trans_balance"))
            @SuppressLint("Range") 
            val target = cursor.getDouble(cursor.getColumnIndex("trans_target"))

            if (target > 0) {
                val percentage = (balance / target) * 100

                if (percentage >= 100) {
                    notifications.add(NotificationItem(
                        "Budget Exceeded!",
                        String.format("You've exceeded your budget by $%.2f this month", balance - target),
                        "Today",
                        NotificationItem.NotificationType.BUDGET_EXCEEDED
                    ))
                } else if (percentage >= 80) {
                    notifications.add(NotificationItem(
                        "Budget Warning",
                        String.format("You've spent %.0f%% of your budget for this month", percentage),
                        "Today",
                        NotificationItem.NotificationType.BUDGET_WARNING
                    ))
                }

                if (balance >= target) {
                    notifications.add(NotificationItem(
                        "Goal Achieved!",
                        "Congratulations! You've reached your savings goal for this month.",
                        "Today",
                        NotificationItem.NotificationType.GOAL_ACHIEVED
                    ))
                }
            }
        }
        cursor.close()
    }

    private fun checkRecentTransactions(db: SQLiteDatabase) {
        val query = "SELECT bud_name, bud_amount, bud_date FROM budget WHERE user_id = ? ORDER BY bud_date DESC LIMIT 3"
        val cursor = db.rawQuery(query, arrayOf(user_id))

        while (cursor.moveToNext()) {
            @SuppressLint("Range") 
            val name = cursor.getString(cursor.getColumnIndex("bud_name"))
            @SuppressLint("Range") 
            val amount = cursor.getInt(cursor.getColumnIndex("bud_amount"))
            @SuppressLint("Range") 
            val date = cursor.getString(cursor.getColumnIndex("bud_date"))

            notifications.add(NotificationItem(
                "Transaction Added",
                String.format("Added transaction: %s", name),
                getRelativeTime(date),
                NotificationItem.NotificationType.TRANSACTION_ADDED,
                String.format("$%d", amount)
            ))
        }
        cursor.close()
    }

    private fun checkRecentExpenses(db: SQLiteDatabase) {
        val query = "SELECT name, amount, date FROM expenses WHERE user_id = ? ORDER BY date DESC LIMIT 3"
        val cursor = db.rawQuery(query, arrayOf(user_id))

        while (cursor.moveToNext()) {
            @SuppressLint("Range") 
            val name = cursor.getString(cursor.getColumnIndex("name"))
            @SuppressLint("Range") 
            val amount = cursor.getInt(cursor.getColumnIndex("amount"))
            @SuppressLint("Range") 
            val date = cursor.getString(cursor.getColumnIndex("date"))

            notifications.add(NotificationItem(
                "Expense Added",
                String.format("Added expense: %s", name),
                getRelativeTime(date),
                NotificationItem.NotificationType.EXPENSE_ADDED,
                String.format("$%d", amount)
            ))
        }
        cursor.close()
    }

    private fun getRelativeTime(dateString: String): String {
        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val date = sdf.parse(dateString)
            val now = Date()

            val diffInMillis = now.time - date!!.time
            val diffInDays = diffInMillis / (24 * 60 * 60 * 1000)

            when {
                diffInDays == 0L -> "Today"
                diffInDays == 1L -> "Yesterday"
                diffInDays < 7 -> "$diffInDays days ago"
                else -> "1 week ago"
            }
        } catch (e: Exception) {
            "Recently"
        }
    }

    private fun generateNotificationsFromDataOnline(monthYear: String) {
        if (!firebaseManager.isUserAuthenticated()) {
            generateNotificationsFromData(monthYear)
            return
        }

        // Check budget status from Firebase
        checkBudgetStatusOnline(monthYear)

        // Check recent transactions from Firebase
        checkRecentTransactionsOnline()

        // Check recent expenses from Firebase
        checkRecentExpensesOnline()

        // Generate real-time notifications
        generateRealTimeNotifications()
    }

    private fun checkBudgetStatusOnline(monthYear: String) {
        firebaseManager.getOrCreateTransactionSummary(monthYear, object : FirebaseManager.FirebaseCallback<Map<String, Any>> {
            override fun onSuccess(result: Map<String, Any>?) {
                if (result == null) {
                    Log.e("NotificationActivity", "Budget status result is null")
                    return
                }

                var balance = 0.0
                var target = 0.0

                if (result.containsKey("trans_balance")) {
                    balance = (result["trans_balance"] as Number).toDouble()
                }
                if (result.containsKey("trans_target")) {
                    target = (result["trans_target"] as Number).toDouble()
                }

                if (target > 0) {
                    val percentage = (balance / target) * 100

                    if (percentage >= 100) {
                        notifications.add(NotificationItem(
                            "Budget Exceeded!",
                            String.format("You've exceeded your budget by $%.2f this month", balance - target),
                            "Today",
                            NotificationItem.NotificationType.BUDGET_EXCEEDED
                        ))
                    } else if (percentage >= 80) {
                        notifications.add(NotificationItem(
                            "Budget Warning",
                            String.format("You've spent %.0f%% of your budget for this month", percentage),
                            "Today",
                            NotificationItem.NotificationType.BUDGET_WARNING
                        ))
                    }

                    // Check goal achievement
                    if (balance >= target) {
                        notifications.add(NotificationItem(
                            "Goal Achieved!",
                            "Congratulations! You've reached your savings goal for this month.",
                            "Today",
                            NotificationItem.NotificationType.GOAL_ACHIEVED
                        ))
                    }

                    // Check low balance
                    if (balance <= 100.0 && balance > 0) {
                        notifications.add(NotificationItem(
                            "Low Balance Alert",
                            String.format("Your current balance is low: $%.2f. Consider adding funds.", balance),
                            "Today",
                            NotificationItem.NotificationType.LOW_BALANCE
                        ))
                    }
                }
                updateUI()
            }

            override fun onFailure(e: Exception) {
                Log.e("NotificationActivity", "Failed to check budget status online", e)
            }
        })
    }

    private fun checkRecentTransactionsOnline() {
        firebaseManager.getTransactions(object : FirebaseManager.FirebaseCallback<List<Map<String, Any>>> {
            override fun onSuccess(result: List<Map<String, Any>>?) {
                if (result == null) {
                    Log.e("NotificationActivity", "Transactions result is null")
                    return
                }

                // Get the 3 most recent transactions
                val count = minOf(3, result.size)
                for (i in 0 until count) {
                    val transaction = result[i]
                    val name = transaction["bud_name"] as? String
                    val amountNum = transaction["bud_amount"] as? Number
                    val date = transaction["bud_date"] as? String

                    if (name != null && amountNum != null) {
                        notifications.add(NotificationItem(
                            "Transaction Added",
                            String.format("Added transaction: %s", name),
                            getRelativeTime(date ?: ""),
                            NotificationItem.NotificationType.TRANSACTION_ADDED,
                            String.format("$%.2f", amountNum.toDouble())
                        ))
                    }
                }
                updateUI()
            }

            override fun onFailure(e: Exception) {
                Log.e("NotificationActivity", "Failed to check recent transactions online", e)
            }
        })
    }

    private fun checkRecentExpensesOnline() {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)

        firebaseManager.getExpensesForMonth(monthYear, object : FirebaseManager.FirebaseCallback<List<Map<String, Any>>> {
            override fun onSuccess(result: List<Map<String, Any>>?) {
                if (result == null) {
                    Log.e("NotificationActivity", "Expenses result is null")
                    return
                }

                // Get the 3 most recent expenses
                val count = minOf(3, result.size)
                for (i in 0 until count) {
                    val expense = result[i]
                    val name = expense["name"] as? String
                    val amountNum = expense["amount"] as? Number
                    val date = expense["date"] as? String

                    if (name != null && amountNum != null) {
                        notifications.add(NotificationItem(
                            "Expense Added",
                            String.format("Added expense: %s", name),
                            getRelativeTime(date ?: ""),
                            NotificationItem.NotificationType.EXPENSE_ADDED,
                            String.format("$%.2f", amountNum.toDouble())
                        ))
                    }
                }
                updateUI()
            }

            override fun onFailure(e: Exception) {
                Log.e("NotificationActivity", "Failed to check recent expenses online", e)
            }
        })
    }

    private fun updateUI() {
        if (notifications.isEmpty()) {
            notificationsRecyclerView.visibility = View.GONE
            emptyState.visibility = View.VISIBLE
        } else {
            notificationsRecyclerView.visibility = View.VISIBLE
            emptyState.visibility = View.GONE
            notificationAdapter.updateNotifications(notifications)
        }
    }

    private fun clearAllNotifications() {
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) &&
            firebaseManager.isUserAuthenticated()) {
            firebaseManager.clearAllNotifications(object : FirebaseManager.FirebaseCallback<Void> {
                override fun onSuccess(result: Void?) {
                    notifications.clear()
                    updateUI()
                }

                override fun onFailure(e: Exception) {
                    Log.e("NotificationActivity", "Failed to clear Firebase notifications", e)
                    notifications.clear()
                    updateUI()
                }
            })
        } else {
            notifications.clear()
            updateUI()
        }
    }

    private fun getNotificationTypeFromString(type: String?): NotificationItem.NotificationType {
        if (type == null) return NotificationItem.NotificationType.TRANSACTION_ADDED

        return when (type) {
            "BUDGET_WARNING" -> NotificationItem.NotificationType.BUDGET_WARNING
            "BUDGET_EXCEEDED" -> NotificationItem.NotificationType.BUDGET_EXCEEDED
            "GOAL_ACHIEVED" -> NotificationItem.NotificationType.GOAL_ACHIEVED
            "TRANSACTION_ADDED" -> NotificationItem.NotificationType.TRANSACTION_ADDED
            "EXPENSE_ADDED" -> NotificationItem.NotificationType.EXPENSE_ADDED
            "LOW_BALANCE" -> NotificationItem.NotificationType.LOW_BALANCE
            else -> NotificationItem.NotificationType.TRANSACTION_ADDED
        }
    }

    private fun generateRealTimeNotifications() {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)

        // Check budget status and create Firebase notifications if needed
        if (firebaseManager.isUserAuthenticated()) {
            firebaseManager.getOrCreateTransactionSummary(monthYear, object : FirebaseManager.FirebaseCallback<Map<String, Any>> {
                override fun onSuccess(result: Map<String, Any>?) {
                    if (result == null) {
                        Log.e("NotificationActivity", "Real-time notifications result is null")
                        return
                    }

                    var balance = 0.0
                    var target = 0.0
                    var budget = 0.0

                    if (result.containsKey("trans_balance")) {
                        balance = (result["trans_balance"] as Number).toDouble()
                    }
                    if (result.containsKey("trans_target")) {
                        target = (result["trans_target"] as Number).toDouble()
                    }
                    if (result.containsKey("trans_budget")) {
                        budget = (result["trans_budget"] as Number).toDouble()
                    }

                    // Check budget alerts
                    if (target > 0) {
                        val percentage = (balance / target) * 100

                        if (percentage >= 80) {
                            firebaseManager.createBudgetAlert(monthYear, percentage, object : FirebaseManager.FirebaseCallback<Void> {
                                override fun onSuccess(result: Void?) {
                                    Log.d("NotificationActivity", "Budget alert notification created")
                                }

                                override fun onFailure(e: Exception) {
                                    Log.e("NotificationActivity", "Failed to create budget alert", e)
                                }
                            })
                        }
                    }

                    // Check goal achievements
                    if (target > 0 && balance >= target) {
                        firebaseManager.createGoalAchievementNotification("savings", object : FirebaseManager.FirebaseCallback<Void> {
                            override fun onSuccess(result: Void?) {
                                Log.d("NotificationActivity", "Goal achievement notification created")
                            }

                            override fun onFailure(e: Exception) {
                                Log.e("NotificationActivity", "Failed to create goal achievement notification", e)
                            }
                        })
                    }

                    // Check low balance alerts
                    checkLowBalanceAlert(balance)
                }

                override fun onFailure(e: Exception) {
                    Log.e("NotificationActivity", "Failed to get transaction summary", e)
                }
            })
        }
    }

    private fun checkLowBalanceAlert(currentBalance: Double) {
        // Define low balance threshold (e.g., $100 or less)
        val lowBalanceThreshold = 100.0

        if (currentBalance <= lowBalanceThreshold && currentBalance > 0) {
            firebaseManager.saveNotification(
                "Low Balance Alert",
                String.format("Your current balance is low: $%.2f. Consider adding funds.", currentBalance),
                "LOW_BALANCE",
                object : FirebaseManager.FirebaseCallback<Void> {
                    override fun onSuccess(result: Void?) {
                        Log.d("NotificationActivity", "Low balance notification created")
                    }

                    override fun onFailure(e: Exception) {
                        Log.e("NotificationActivity", "Failed to create low balance notification", e)
                    }
                }
            )
        }
    }

    private fun setupNavigationButtons() {
        val homeButton = findViewById<ImageButton>(R.id.button_home)
        val expensesButton = findViewById<ImageButton>(R.id.button_expenses)
        val transactionsButton = findViewById<ImageButton>(R.id.button_transactions)
        val profileButton = findViewById<ImageButton>(R.id.button_profile)
        val notificationButton = findViewById<ImageButton>(R.id.button_notification)

        homeButton.setOnClickListener { navigateTo(DashboardActivity::class.java) }
        expensesButton.setOnClickListener { navigateTo(ExpensesActivity::class.java) }
        transactionsButton.setOnClickListener { navigateTo(TransactionsActivity::class.java) }
        profileButton.setOnClickListener { navigateTo(SettingsActivity::class.java) }
        notificationButton.setOnClickListener {
            // Already in notifications activity, do nothing or refresh
            loadNotifications()
        }
    }

    private fun navigateTo(activityClass: Class<*>) {
        if (this.javaClass != activityClass) {
            val intent = Intent(this, activityClass)
            startActivity(intent)
        }
    }
}
