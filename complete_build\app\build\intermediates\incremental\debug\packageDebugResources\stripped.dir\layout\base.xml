<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="?attr/colorSurface"
    android:elevation="8dp"
    android:paddingVertical="8dp">

    <!-- Footer button 1 -->
    <ImageButton
        android:id="@+id/button_home"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_weight="1"
        android:src="@drawable/home"
        android:background="@drawable/nav_button_background"
        android:contentDescription="@string/home_description"
        android:scaleType="fitCenter"
        android:tint="?attr/colorOnSurface"
        android:padding="12dp" />

    <!-- Footer button 2 -->
    <ImageButton
        android:id="@+id/button_expenses"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_weight="1"
        android:src="@drawable/attach_money"
        android:background="@drawable/nav_button_background"
        android:contentDescription="@string/expense_description"
        android:scaleType="fitCenter"
        android:tint="?attr/colorOnSurface"
        android:padding="12dp" />

    <!-- Footer button 3 -->
    <ImageButton
        android:id="@+id/button_transactions"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_weight="1"
        android:src="@drawable/credit_card"
        android:background="@drawable/nav_button_background"
        android:contentDescription="@string/transaction_description"
        android:scaleType="fitCenter"
        android:tint="?attr/colorOnSurface"
        android:padding="12dp" />

    <!-- Footer button 4 -->
    <ImageButton
        android:id="@+id/button_notification"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_weight="1"
        android:src="@drawable/notifications"
        android:background="@drawable/nav_button_background"
        android:contentDescription="@string/notifications"
        android:scaleType="fitCenter"
        android:tint="?attr/colorOnSurface"
        android:padding="12dp" />

    <!-- Footer button 5 -->
    <ImageButton
        android:id="@+id/button_profile"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_weight="1"
        android:src="@drawable/person"
        android:background="@drawable/nav_button_background"
        android:contentDescription="@string/profile_description"
        android:scaleType="fitCenter"
        android:tint="?attr/colorOnSurface"
        android:padding="12dp" />

</LinearLayout>
