C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\DashboardActivity.kt:526: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String.format("$%.0f", value)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\DashboardActivity.kt:526: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String.format("$%.0f", value)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\ExpensesActivity.kt:170: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                            String.format("Added %s: $%d (Synced to cloud)", selectedCategory, budgetAmount)
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\ExpensesActivity.kt:197: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String.format("Added %s: $%d (Saved locally)", selectedCategory, budgetAmount)
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\ExpensesActivity.kt:226: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("Added %s: $%d", name, amount)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\ExpensesActivity.kt:261: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("Added %s: $%d", name, amount)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\FirebaseManager.kt:469: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                message = String.format("You've exceeded your budget by %.1f%% this month", spentPercentage - 100)
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\FirebaseManager.kt:473: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                message = String.format("You've spent %.0f%% of your budget for this month", spentPercentage)
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\FirebaseManager.kt:498: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val message = String.format("Added transaction: %s ($%.2f)", transactionName, amount)
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\FirebaseManager.kt:507: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val message = String.format("Added expense: %s ($%.2f)", expenseName, amount)
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\Item2Adapter.kt:35: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        holder.amountTextView.text = String.format("+$%d", budget.getAmount())
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\Item2Adapter.kt:35: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        holder.amountTextView.text = String.format("+$%d", budget.getAmount())
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\Item2Adapter.kt:39: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            val details = String.format(
                          ^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\Item2Adapter.kt:48: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            val detailedInfo = String.format(
                               ^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\ItemAdapter.kt:29: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        holder.amountTextView.text = String.format("$%d", expense.getAmount())
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\ItemAdapter.kt:29: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        holder.amountTextView.text = String.format("$%d", expense.getAmount())
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\ItemAdapter.kt:77: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            val details = String.format(
                          ^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:196: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String.format("Your current balance is low: $%.2f. Consider adding funds.", balance),
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:221: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        String.format("You've exceeded your budget by $%.2f this month", balance - target),
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:228: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        String.format("You've spent %.0f%% of your budget for this month", percentage),
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:264: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("$%d", amount)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:287: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("$%d", amount)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:356: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                            String.format("You've exceeded your budget by $%.2f this month", balance - target),
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:363: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                            String.format("You've spent %.0f%% of your budget for this month", percentage),
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:383: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                            String.format("Your current balance is low: $%.2f. Consider adding funds.", balance),
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:420: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                            String.format("$%.2f", amountNum.toDouble())
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:459: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                            String.format("$%.2f", amountNum.toDouble())
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationActivity.kt:590: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("Your current balance is low: $%.2f. Consider adding funds.", currentBalance),
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationWorker.kt:101: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val message = String.format("You've spent %.0f%% of your budget this month", percentage)
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationWorker.kt:118: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val message = String.format("You've exceeded your budget by $%.2f this month", exceededAmount)
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationWorker.kt:134: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val message = String.format("Your current balance is low: $%.2f. Consider adding funds.", balance)
                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:402: Warning: Duplicate id @+id/button_notification, defined or included multiple times in layout/dashboard.xml: [layout/dashboard.xml defines @+id/button_notification, layout/dashboard.xml => layout/base.xml defines @+id/button_notification] [DuplicateIncludedIds]
    <include
    ^
    C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:48: Defined here
    C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml:52: Defined here, included via layout/dashboard.xml => layout/base.xml defines @+id/button_notification

   Explanation for issues of type "DuplicateIncludedIds":
   It's okay for two independent layouts to use the same ids. However, if
   layouts are combined with include tags, then the id's need to be unique
   within any chain of included layouts, or Activity#findViewById() can return
   an unexpected view.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:14: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk = 34
        ~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\LanguageActivity.kt:41: Warning: Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the bundle configuration [AppBundleLocaleChanges]
        config.setLocale(locale)
        ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AppBundleLocaleChanges":
   When changing locales at runtime (e.g. to provide an in-app language
   switcher), the Android App Bundle must be configured to not split by locale
   or the Play Core library must be used to download additional locales at
   runtime.

   https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes

C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2) [AndroidGradlePluginVersion]
agp = "8.5.1"
      ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2) [AndroidGradlePluginVersion]
agp = "8.5.1"
      ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2) [AndroidGradlePluginVersion]
agp = "8.5.1"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:67: Warning: BOM should be added with a call to platform() [BomWithoutPlatform]
    implementation("com.google.firebase:firebase-bom:33.1.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "BomWithoutPlatform":
   When including a BOM, the dependency's coordinates must be wrapped in a
   call to platform() for Gradle to interpret it correctly.

   https://developer.android.com/r/tools/gradle-bom-docs

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:47: Warning: A newer version of org.jetbrains.kotlin:kotlin-stdlib than 1.9.10 is available: 1.9.22 [GradleDependency]
    implementation("org.jetbrains.kotlin:kotlin-stdlib:1.9.10")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:67: Warning: A newer version of com.google.firebase:firebase-bom than 33.1.2 is available: 33.15.0 [GradleDependency]
    implementation("com.google.firebase:firebase-bom:33.1.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:68: Warning: A newer version of androidx.work:work-runtime than 2.8.0 is available: 2.10.1 [GradleDependency]
    implementation ("androidx.work:work-runtime:2.8.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:12: Warning: A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.6.0"
                     ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:12: Warning: A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.6.0"
                     ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:12: Warning: A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.6.0"
                     ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:13: Warning: A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationUi = "2.6.0"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:13: Warning: A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationUi = "2.6.0"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:13: Warning: A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationUi = "2.6.0"
               ~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:14: Warning: A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0 [GradleDependency]
firebaseBom = "32.1.2"
              ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:14: Warning: A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0 [GradleDependency]
firebaseBom = "32.1.2"
              ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:14: Warning: A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0 [GradleDependency]
firebaseBom = "32.1.2"
              ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:16: Warning: A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1 [GradleDependency]
firebaseAuth = "23.0.0"
               ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:16: Warning: A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1 [GradleDependency]
firebaseAuth = "23.0.0"
               ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\gradle\libs.versions.toml:16: Warning: A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1 [GradleDependency]
firebaseAuth = "23.0.0"
               ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:29: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="?attr/colorOnSurface" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:29: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="?attr/colorOnSurface" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:244: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                            android:tint="?attr/colorPrimary"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml:21: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="?attr/colorOnSurface"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml:34: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="?attr/colorOnSurface"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml:47: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="?attr/colorOnSurface"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml:60: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="?attr/colorOnSurface"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml:73: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="?attr/colorOnSurface"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\item_notification.xml:31: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@color/white" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_bug.xml:34: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="?attr/colorOnSecondaryContainer" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_item.xml:36: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="?attr/colorPrimary" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\login.xml:90: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="?attr/colorOnSurface" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\login.xml:135: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="?attr/colorPrimary" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:29: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="?attr/colorOnSurface" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:227: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="?attr/colorOnSurface" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:276: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                                android:tint="?attr/colorOnSurface" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:29: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="?attr/colorOnSurface" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:282: Warning: Use app:drawableTopCompat instead of android:drawableTop [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableTop="@drawable/attach_money"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:283: Warning: Use app:drawableTint instead of android:drawableTint [UseCompatTextViewDrawableXml from androidx.appcompat]
                        android:drawableTint="?attr/colorOnSurfaceVariant"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseCompatTextViewDrawableXml":
   TextView uses android: compound drawable attributes instead of app: ones

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:55: Error: "budget_info" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="budget_info">Your budget for this month is $%d</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:66: Error: "zulu" is not translated in "af" (Afrikaans) or "zu" (Zulu) [MissingTranslation]
    <string name="zulu">Zulu</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:67: Error: "name_bug" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="name_bug">Name</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:68: Error: "bug_amount" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="bug_amount">Amount</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:69: Error: "exp_amount" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="exp_amount">Amount</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:70: Error: "ext_name" is not translated in "af" (Afrikaans) or "zu" (Zulu) [MissingTranslation]
    <string name="ext_name">Name</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:71: Error: "exp_date" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="exp_date">exp_date</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:72: Error: "date_x" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="date_x">Date</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:73: Error: "main_cash" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="main_cash">Main_cash</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:74: Error: "_0" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="_0">$0</string>
            ~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:75: Error: "month" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="month">This month</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:76: Error: "pick_date" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="pick_date">Pick Date</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:77: Error: "budget" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="budget">Budget</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:78: Error: "target" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="target">Target</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:79: Error: "balance" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="balance">Balance</string>
            ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:80: Error: "set_new_target" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="set_new_target">Month Target</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:81: Error: "set" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="set">Set</string>
            ~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:82: Error: "sign_up" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="sign_up">Sign Up</string>
            ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:83: Error: "add_expense" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="add_expense">Add Expense</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:84: Error: "view_all" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu) [MissingTranslation]
    <string name="view_all">View All</string>
            ~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingTranslation":
   If an application has more than one locale, then all the strings declared
   in one language should also be translated in all other languages.

   If the string should not be translated, you can add the attribute
   translatable="false" on the <string> element, or you can define all your
   non-translatable strings in a resource file called donottranslate.xml. Or,
   you can ignore the issue with a tools:ignore="MissingTranslation"
   attribute.

   You can tell lint (and other tools) which language is the default language
   in your res/values/ folder by specifying tools:locale="languageCode" for
   the root <resources> element in your resource file. (The tools prefix
   refers to the namespace declaration http://schemas.android.com/tools.)

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\adapters\NotificationAdapter.kt:84: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged()
        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\NotificationHelper.kt:59: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\eye_close.xml:9: Warning: Very long vector path (999 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M644,532L586,474Q595,427 559,386Q523,345 466,354L408,296Q425,288 442.5,284Q460,280 480,280Q555,280 607.5,332.5Q660,385 660,460Q660,480 656,497.5Q652,515 644,532ZM772,658L714,602Q752,573 781.5,538.5Q811,504 832,460Q782,359 688.5,299.5Q595,240 480,240Q451,240 423,244Q395,248 368,256L306,194Q347,177 390,168.5Q433,160 480,160Q631,160 749,243.5Q867,327 920,460Q897,519 859.5,569.5Q822,620 772,658ZM792,904L624,738Q589,749 553.5,754.5Q518,760 480,760Q329,760 211,676.5Q93,593 40,460Q61,407 93,361.5Q125,316 166,280L56,168L112,112L848,848L792,904ZM222,336Q193,362 169,393Q145,424 128,460Q178,561 271.5,620.5Q365,680 480,680Q500,680 519,677.5Q538,675 558,672L522,634Q511,637 501,638.5Q491,640 480,640Q405,640 352.5,587.5Q300,535 300,460Q300,449 301.5,439Q303,429 306,418L222,336ZM541,429L541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429ZM390,504Q390,504 390,504Q390,504 390,504L390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\fingerprint.xml:9: Warning: Very long vector path (1875 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M481,179Q587,179 681,224.5Q775,270 838,356Q845,365 842.5,372Q840,379 834,384Q828,389 820,388.5Q812,388 806,380Q751,302 664.5,260.5Q578,219 481,219Q384,219 299,260.5Q214,302 158,380Q152,389 144,390Q136,391 130,386Q123,381 121.5,373.5Q120,366 126,358Q188,273 281.5,226Q375,179 481,179ZM481,273Q616,273 713,363Q810,453 810,586Q810,636 774.5,669.5Q739,703 688,703Q637,703 600.5,669.5Q564,636 564,586Q564,553 539.5,530.5Q515,508 481,508Q447,508 422.5,530.5Q398,553 398,586Q398,683 455.5,748Q513,813 604,839Q613,842 616,849Q619,856 617,864Q615,871 609,876Q603,881 594,879Q490,853 424,775.5Q358,698 358,586Q358,536 394,502Q430,468 481,468Q532,468 568,502Q604,536 604,586Q604,619 629,641.5Q654,664 688,664Q722,664 746,641.5Q770,619 770,586Q770,470 685,391Q600,312 482,312Q364,312 279,391Q194,470 194,585Q194,609 198.5,645Q203,681 220,729Q223,738 219.5,745Q216,752 208,755Q200,758 192.5,754.5Q185,751 182,743Q167,704 160.5,665.5Q154,627 154,586Q154,453 250.5,363Q347,273 481,273ZM481,81Q545,81 606,96.5Q667,112 724,141Q733,146 734.5,153Q736,160 733,167Q730,174 723,178Q716,182 706,177Q653,150 596.5,135.5Q540,121 481,121Q423,121 367,134.5Q311,148 260,177Q252,182 244,179.5Q236,177 232,169Q228,161 230,154.5Q232,148 240,143Q296,113 357,97Q418,81 481,81ZM481,370Q574,370 641,432.5Q708,495 708,586Q708,595 702.5,600.5Q697,606 688,606Q680,606 674,600.5Q668,595 668,586Q668,511 612.5,460.5Q557,410 481,410Q405,410 350.5,460.5Q296,511 296,586Q296,667 324,723.5Q352,780 406,837Q412,843 412,851Q412,859 406,865Q400,871 392,871Q384,871 378,865Q319,803 287.5,738.5Q256,674 256,586Q256,495 322,432.5Q388,370 481,370ZM480,566Q489,566 494.5,572Q500,578 500,586Q500,661 554,709Q608,757 680,757Q686,757 697,756Q708,755 720,753Q729,751 735.5,755.5Q742,760 744,769Q746,777 741,783Q736,789 728,791Q710,796 696.5,796.5Q683,797 680,797Q591,797 525.5,737Q460,677 460,586Q460,578 465.5,572Q471,566 480,566Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\person.xml:9: Warning: Very long vector path (922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M480,480Q414,480 367,433Q320,386 320,320Q320,254 367,207Q414,160 480,160Q546,160 593,207Q640,254 640,320Q640,386 593,433Q546,480 480,480ZM160,800L160,688Q160,654 177.5,625.5Q195,597 224,582Q286,551 350,535.5Q414,520 480,520Q546,520 610,535.5Q674,551 736,582Q765,597 782.5,625.5Q800,654 800,688L800,800L160,800ZM240,720L720,720L720,688Q720,677 714.5,668Q709,659 700,654Q646,627 591,613.5Q536,600 480,600Q424,600 369,613.5Q314,627 260,654Q251,659 245.5,668Q240,677 240,688L240,720ZM480,400Q513,400 536.5,376.5Q560,353 560,320Q560,287 536.5,263.5Q513,240 480,240Q447,240 423.5,263.5Q400,287 400,320Q400,353 423.5,376.5Q447,400 480,400ZM480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320ZM480,720L480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720L480,720Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\settings_24px.xml:9: Warning: Very long vector path (1388 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M370,880L354,752Q341,747 329.5,740Q318,733 307,725L188,775L78,585L181,507Q180,500 180,493.5Q180,487 180,480Q180,473 180,466.5Q180,460 181,453L78,375L188,185L307,235Q318,227 330,220Q342,213 354,208L370,80L590,80L606,208Q619,213 630.5,220Q642,227 653,235L772,185L882,375L779,453Q780,460 780,466.5Q780,473 780,480Q780,487 780,493.5Q780,500 778,507L881,585L771,775L653,725Q642,733 630,740Q618,747 606,752L590,880L370,880ZM440,800L519,800L533,694Q564,686 590.5,670.5Q617,655 639,633L738,674L777,606L691,541Q696,527 698,511.5Q700,496 700,480Q700,464 698,448.5Q696,433 691,419L777,354L738,286L639,328Q617,305 590.5,289.5Q564,274 533,266L520,160L441,160L427,266Q396,274 369.5,289.5Q343,305 321,327L222,286L183,354L269,418Q264,433 262,448Q260,463 260,480Q260,496 262,511Q264,526 269,541L183,606L222,674L321,632Q343,655 369.5,670.5Q396,686 427,694L440,800ZM482,620Q540,620 581,579Q622,538 622,480Q622,422 581,381Q540,340 482,340Q423,340 382.5,381Q342,422 342,480Q342,538 382.5,579Q423,620 482,620ZM480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\item_notification.xml:10: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_bug.xml:14: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:7: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:7: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\base.xml:7: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:7: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\language.xml:5: Warning: Possible overdraw: Root element paints background @color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\login.xml:7: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base_Theme_BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\notifications.xml:7: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:7: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:8: Warning: Possible overdraw: Root element paints background ?attr/colorSurface with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="?attr/colorSurface">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\activity_main.xml:2: Warning: The resource R.layout.activity_main appears to be unused [UnusedResources]
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\backg_button.xml:2: Warning: The resource R.drawable.backg_button appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\menu\bottom_navigation_menu.xml:2: Warning: The resource R.menu.bottom_navigation_menu appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\button_ripple.xml:2: Warning: The resource R.drawable.button_ripple appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.primary appears to be unused [UnusedResources]
    <color name="primary">#0061A4</color>
           ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.steelblue appears to be unused [UnusedResources]
    <color name="steelblue">#4682B4</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.onPrimary appears to be unused [UnusedResources]
    <color name="onPrimary">#ffffff</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.primary_blue appears to be unused [UnusedResources]
    <color name="primary_blue">#0061A4</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:11: Warning: The resource R.color.border_color appears to be unused [UnusedResources]
    <color name="border_color">#C3C7CF</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.back_ground appears to be unused [UnusedResources]
    <color name="back_ground">#b0463a</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:38: Warning: The resource R.color.md_theme_light_outline appears to be unused [UnusedResources]
    <color name="md_theme_light_outline">#73777F</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:39: Warning: The resource R.color.md_theme_light_outlineVariant appears to be unused [UnusedResources]
    <color name="md_theme_light_outlineVariant">#C3C7CF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:40: Warning: The resource R.color.md_theme_light_scrim appears to be unused [UnusedResources]
    <color name="md_theme_light_scrim">#000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\colors.xml:43: Warning: The resource R.color.md_theme_light_inversePrimary appears to be unused [UnusedResources]
    <color name="md_theme_light_inversePrimary">#9ECAFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\content_main.xml:2: Warning: The resource R.layout.content_main appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\dimens.xml:2: Warning: The resource R.dimen.fab_margin appears to be unused [UnusedResources]
    <dimen name="fab_margin">16dp</dimen>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher.webp: Warning: The resource R.mipmap.ic_launcher appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp: Warning: The resource R.mipmap.ic_launcher_round appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\icon_button_background.xml:2: Warning: The resource R.drawable.icon_button_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\logos.png: Warning: The resource R.drawable.logos appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\logos.png: Warning: The resource R.mipmap.logos appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\mail_24px.xml:1: Warning: The resource R.drawable.mail_24px appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\menu\menu_main.xml:1: Warning: The resource R.menu.menu_main appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\modern_button_background.xml:2: Warning: The resource R.drawable.modern_button_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\monitoring.xml:1: Warning: The resource R.drawable.monitoring appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\navigation\nav_graph.xml:2: Warning: The resource R.navigation.nav_graph appears to be unused [UnusedResources]
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\search.xml:1: Warning: The resource R.drawable.search appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\settings_24px.xml:1: Warning: The resource R.drawable.settings_24px appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\spinner_item.xml:2: Warning: The resource R.layout.spinner_item appears to be unused [UnusedResources]
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.action_settings appears to be unused [UnusedResources]
    <string name="action_settings">Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.first_fragment_label appears to be unused [UnusedResources]
    <string name="first_fragment_label">First Fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.second_fragment_label appears to be unused [UnusedResources]
    <string name="second_fragment_label">Second Fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.next appears to be unused [UnusedResources]
    <string name="next">Next</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.previous appears to be unused [UnusedResources]
    <string name="previous">Previous</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.search_description appears to be unused [UnusedResources]
    <string name="search_description">Search</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.chart_description appears to be unused [UnusedResources]
    <string name="chart_description">Chart</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.budgethero appears to be unused [UnusedResources]
    <string name="budgethero">BudgetHero</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.today appears to be unused [UnusedResources]
    <string name="today">Today</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.add_a_note appears to be unused [UnusedResources]
    <string name="add_a_note">Add a note</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.select_category appears to be unused [UnusedResources]
    <string name="select_category">Select category</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:27: Warning: The resource R.string._0_00 appears to be unused [UnusedResources]
    <string name="_0_00">$0</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.save appears to be unused [UnusedResources]
    <string name="save">Save</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:31: Warning: The resource R.string._5_943 appears to be unused [UnusedResources]
    <string name="_5_943">$5,943</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:32: Warning: The resource R.string.last_30_days appears to be unused [UnusedResources]
    <string name="last_30_days">This month</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:33: Warning: The resource R.string._12 appears to be unused [UnusedResources]
    <string name="_12">+12%</string>
            ~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.meta appears to be unused [UnusedResources]
    <string name="meta">META</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.meta_platforms appears to be unused [UnusedResources]
    <string name="meta_platforms">Meta Platforms</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.warning_icon appears to be unused [UnusedResources]
    <string name="warning_icon">Warning Icon</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.you_ve_spent_70_of_your_budget_for_the_month appears to be unused [UnusedResources]
    <string name="you_ve_spent_70_of_your_budget_for_the_month">You’ve spent 70% of your budget for the month</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.budget_alert appears to be unused [UnusedResources]
    <string name="budget_alert">Budget alert</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.currency_icon appears to be unused [UnusedResources]
    <string name="currency_icon">Currency Icon</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:46: Warning: The resource R.string.transaction appears to be unused [UnusedResources]
    <string name="transaction">Transaction</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:47: Warning: The resource R.string.you_ve_received_25_from_sarah appears to be unused [UnusedResources]
    <string name="you_ve_received_25_from_sarah">You’ve received $25 from Sarah</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:53: Warning: The resource R.string.type appears to be unused [UnusedResources]
    <string name="type">Type</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:57: Warning: The resource R.string.add_money appears to be unused [UnusedResources]
    <string name="add_money">Add money</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:61: Warning: The resource R.string.user_profile appears to be unused [UnusedResources]
    <string name="user_profile">user profile</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:72: Warning: The resource R.string.date_x appears to be unused [UnusedResources]
    <string name="date_x">Date</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:73: Warning: The resource R.string.main_cash appears to be unused [UnusedResources]
    <string name="main_cash">Main_cash</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:75: Warning: The resource R.string.month appears to be unused [UnusedResources]
    <string name="month">This month</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:77: Warning: The resource R.string.budget appears to be unused [UnusedResources]
    <string name="budget">Budget</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:78: Warning: The resource R.string.target appears to be unused [UnusedResources]
    <string name="target">Target</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:79: Warning: The resource R.string.balance appears to be unused [UnusedResources]
    <string name="balance">Balance</string>
            ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:80: Warning: The resource R.string.set_new_target appears to be unused [UnusedResources]
    <string name="set_new_target">Month Target</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\strings.xml:81: Warning: The resource R.string.set appears to be unused [UnusedResources]
    <string name="set">Set</string>
            ~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\styles.xml:3: Warning: The resource R.style.AppTheme appears to be unused [UnusedResources]
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\styles.xml:14: Warning: The resource R.style.App_Widget_BottomNavigationView_ActiveIndicator appears to be unused [UnusedResources]
    <style name="App.Widget.BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\values\styles.xml:44: Warning: The resource R.style.IconButton appears to be unused [UnusedResources]
    <style name="IconButton">
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\work_24px.xml:1: Warning: The resource R.drawable.work_24px appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-hdpi\logo.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-mdpi\logo.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xhdpi\logo.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxhdpi\logo.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\logo.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]

   Explanation for issues of type "IconLauncherShape":
   According to the Android Design Guide
   (https://d.android.com/r/studio-ui/designer/material/iconography) your
   launcher icons should "use a distinct silhouette", a "three-dimensional,
   front view, with a slight perspective as if viewed from above, so that
   users perceive some depth."

   The unique silhouette implies that your launcher icon should not be a
   filled square.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\logo.png: Warning: The image logo.png varies significantly in its density-independent (dip) size across the various density versions: mipmap-hdpi/logo.png: 397x279 dp (595x419 px), mipmap-mdpi/logo.png: 595x419 dp (595x419 px), mipmap-xhdpi/logo.png: 298x210 dp (595x419 px), mipmap-xxhdpi/logo.png: 198x140 dp (595x419 px), mipmap-xxxhdpi/logo.png: 149x105 dp (595x419 px) [IconDipSize]

   Explanation for issues of type "IconDipSize":
   Checks the all icons which are provided in multiple densities, all compute
   to roughly the same density-independent pixel (dip) size. This catches
   errors where images are either placed in the wrong folder, or icons are
   changed to new sizes but some folders are forgotten.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\logo.png: Warning: Found bitmap drawable res/drawable/logo.png in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\drawable\logos.png: Warning: Found bitmap drawable res/drawable/logos.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\logo.png: Warning: The following unrelated icon files have identical contents: logo.png, logos.png, logo.png, logo.png, logo.png, logo.png, logo.png [IconDuplicates]

   Explanation for issues of type "IconDuplicates":
   If an icon is repeated under different names, you can consolidate and just
   use one of the icons and delete the others to make your application
   smaller. However, duplicated icons usually are not intentional and can
   sometimes point to icons that were accidentally overwritten or accidentally
   not updated.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:257: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:271: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:285: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:299: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:313: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                        <Button
                         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:268: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                            android:textSize="8sp"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:278: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                            android:textSize="8sp"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:292: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                            android:textSize="8sp"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:306: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                            android:textSize="8sp"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:320: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                            android:textSize="8sp"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:375: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
                            android:textSize="9sp"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_bug.xml:95: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                android:textSize="10sp"
                ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:130: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:179: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:143: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:192: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\login.xml:47: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\login.xml:71: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:131: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:169: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:208: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:257: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:105: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:143: Warning: Missing autofillHints attribute [Autofill]
                            <EditText
                             ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:47: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlin:kotlin-stdlib:1.9.10")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:56: Warning: Use version catalog instead (com.google.firebase:firebase-firestore is already available as firebase-firestore, but using version 25.1.4 instead) [UseTomlInstead]
    implementation("com.google.firebase:firebase-firestore")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:57: Warning: Use version catalog instead (com.google.firebase:firebase-messaging is already available as firebase-messaging, but using version 24.1.1 instead) [UseTomlInstead]
    implementation("com.google.firebase:firebase-messaging")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:63: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:64: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:65: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.jjoe64:graphview:4.2.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:66: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.google.code.gson:gson:2.10.1")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:67: Warning: Use version catalog instead (com.google.firebase:firebase-bom is already available as firebase-bom, but using version 32.1.2 instead) [UseTomlInstead]
    implementation("com.google.firebase:firebase-bom:33.1.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:68: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("androidx.work:work-runtime:2.8.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\build.gradle.kts:69: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.guava:guava:32.0.1-android")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:240: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:27: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_bug.xml:29: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_item.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\notifications.xml:83: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:65: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\SettingsActivity.kt:160: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                balanceTextView.text = "0.00"
                                       ~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\SettingsActivity.kt:160: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                balanceTextView.text = "0.00"
                                        ~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\SettingsActivity.kt:161: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                targetTextView.text = "0.00"
                                      ~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\SettingsActivity.kt:161: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                targetTextView.text = "0.00"
                                       ~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\SettingsActivity.kt:162: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                budgetTextView.text = "0.00"
                                      ~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\java\com\example\budgettracker\SettingsActivity.kt:162: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                budgetTextView.text = "0.00"
                                       ~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:76: Warning: Hardcoded string "Expense Details", should use @string resource [HardcodedText]
                        android:text="Expense Details"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:93: Warning: Hardcoded string "Category", should use @string resource [HardcodedText]
                            android:text="Category"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:118: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
                            android:text="Description"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:134: Warning: Hardcoded string "Enter expense description", should use @string resource [HardcodedText]
                                android:hint="Enter expense description"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:157: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
                            android:text="Amount"
                            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:173: Warning: Hardcoded string "$", should use @string resource [HardcodedText]
                                android:text="$"
                                ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:183: Warning: Hardcoded string "0.00", should use @string resource [HardcodedText]
                                android:hint="0.00"
                                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:205: Warning: Hardcoded string "Date", should use @string resource [HardcodedText]
                            android:text="Date"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addexpense.xml:224: Warning: Hardcoded string "Select Date", should use @string resource [HardcodedText]
                                android:text="Select Date"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:36: Warning: Hardcoded string "Add Transaction", should use @string resource [HardcodedText]
            android:text="Add Transaction"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:76: Warning: Hardcoded string "Current Budget", should use @string resource [HardcodedText]
                        android:text="Current Budget"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:87: Warning: Hardcoded string "$0.00", should use @string resource [HardcodedText]
                        android:text="$0.00"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:114: Warning: Hardcoded string "Transaction Details", should use @string resource [HardcodedText]
                        android:text="Transaction Details"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:131: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
                            android:text="Description"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:147: Warning: Hardcoded string "Enter transaction description", should use @string resource [HardcodedText]
                                android:hint="Enter transaction description"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:170: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
                            android:text="Amount"
                            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:186: Warning: Hardcoded string "$", should use @string resource [HardcodedText]
                                android:text="$"
                                ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:196: Warning: Hardcoded string "0.00", should use @string resource [HardcodedText]
                                android:hint="0.00"
                                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:251: Warning: Hardcoded string "Recent Transactions", should use @string resource [HardcodedText]
                            android:text="Recent Transactions"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:260: Warning: Hardcoded string "View All", should use @string resource [HardcodedText]
                            android:text="View All"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\addtransaction.xml:276: Warning: Hardcoded string "No recent transactions found.nAdd your first transaction above!", should use @string resource [HardcodedText]
                        android:text="No recent transactions found.\nAdd your first transaction above!"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:140: Warning: Hardcoded string "Spending vs Target:", should use @string resource [HardcodedText]
                            android:text="Spending vs Target:"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:150: Warning: Hardcoded string "0% of target spent", should use @string resource [HardcodedText]
                            android:text="0% of target spent"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:169: Warning: Hardcoded string "Balance vs Budget:", should use @string resource [HardcodedText]
                            android:text="Balance vs Budget:"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:179: Warning: Hardcoded string "0% remaining", should use @string resource [HardcodedText]
                            android:text="0% remaining"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:198: Warning: Hardcoded string "Total Spent:", should use @string resource [HardcodedText]
                            android:text="Total Spent:"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:208: Warning: Hardcoded string "$0", should use @string resource [HardcodedText]
                            android:text="$0"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:237: Warning: Hardcoded string "Spending Overview", should use @string resource [HardcodedText]
                        android:text="Spending Overview"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:419: Warning: Hardcoded string "Add Expense", should use @string resource [HardcodedText]
        android:text="Add Expense"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:440: Warning: Hardcoded string "Add Expense", should use @string resource [HardcodedText]
        android:contentDescription="Add Expense"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:458: Warning: Hardcoded string "Add Transaction", should use @string resource [HardcodedText]
        android:text="Add Transaction"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:479: Warning: Hardcoded string "Add Budget", should use @string resource [HardcodedText]
        android:contentDescription="Add Budget"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\dashboard.xml:496: Warning: Hardcoded string "Add New Entry", should use @string resource [HardcodedText]
        android:contentDescription="Add New Entry"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\item_notification.xml:29: Warning: Hardcoded string "Notification icon", should use @string resource [HardcodedText]
                android:contentDescription="Notification icon"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\item_notification.xml:45: Warning: Hardcoded string "Notification Title", should use @string resource [HardcodedText]
                android:text="Notification Title"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\item_notification.xml:56: Warning: Hardcoded string "Notification message content", should use @string resource [HardcodedText]
                android:text="Notification message content"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\item_notification.xml:67: Warning: Hardcoded string "Just now", should use @string resource [HardcodedText]
                android:text="Just now"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_bug.xml:94: Warning: Hardcoded string "Budget", should use @string resource [HardcodedText]
                android:text="Budget"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_item.xml:90: Warning: Hardcoded string "View more details", should use @string resource [HardcodedText]
                android:contentDescription="View more details"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_item.xml:115: Warning: Hardcoded string "Full description will appear here", should use @string resource [HardcodedText]
                android:text="Full description will appear here"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\list_item.xml:125: Warning: Hardcoded string "Category", should use @string resource [HardcodedText]
                android:text="Category"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\notifications.xml:93: Warning: Hardcoded string "No notifications yet", should use @string resource [HardcodedText]
                    android:text="No notifications yet"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\notifications.xml:102: Warning: Hardcoded string "You'll see important updates about your budget here", should use @string resource [HardcodedText]
                    android:text="You'll see important updates about your budget here"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:36: Warning: Hardcoded string "Create Account", should use @string resource [HardcodedText]
            android:text="Create Account"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:76: Warning: Hardcoded string "Join Budget Tracker", should use @string resource [HardcodedText]
                    android:text="Join Budget Tracker"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:86: Warning: Hardcoded string "Create your account to start managing your finances", should use @string resource [HardcodedText]
                    android:text="Create your account to start managing your finances"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:119: Warning: Hardcoded string "Username", should use @string resource [HardcodedText]
                            android:text="Username"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:135: Warning: Hardcoded string "Enter your username", should use @string resource [HardcodedText]
                                android:hint="Enter your username"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:157: Warning: Hardcoded string "Email Address", should use @string resource [HardcodedText]
                            android:text="Email Address"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:173: Warning: Hardcoded string "Enter your email", should use @string resource [HardcodedText]
                                android:hint="Enter your email"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:195: Warning: Hardcoded string "Password", should use @string resource [HardcodedText]
                            android:text="Password"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:213: Warning: Hardcoded string "Enter your password", should use @string resource [HardcodedText]
                                android:hint="Enter your password"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:225: Warning: Hardcoded string "Toggle password visibility", should use @string resource [HardcodedText]
                                android:contentDescription="Toggle password visibility"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:244: Warning: Hardcoded string "Confirm Password", should use @string resource [HardcodedText]
                            android:text="Confirm Password"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:262: Warning: Hardcoded string "Confirm your password", should use @string resource [HardcodedText]
                                android:hint="Confirm your password"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:274: Warning: Hardcoded string "Toggle password visibility", should use @string resource [HardcodedText]
                                android:contentDescription="Toggle password visibility"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:286: Warning: Hardcoded string "Create Account", should use @string resource [HardcodedText]
                        android:text="Create Account" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\register.xml:296: Warning: Hardcoded string "By creating an account, you agree to our Terms of Service and Privacy Policy", should use @string resource [HardcodedText]
                android:text="By creating an account, you agree to our Terms of Service and Privacy Policy"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:76: Warning: Hardcoded string "Budget Settings", should use @string resource [HardcodedText]
                        android:text="Budget Settings"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:93: Warning: Hardcoded string "Monthly Budget", should use @string resource [HardcodedText]
                            android:text="Monthly Budget"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:110: Warning: Hardcoded string "Enter budget amount", should use @string resource [HardcodedText]
                                android:hint="Enter budget amount"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:131: Warning: Hardcoded string "Target Amount", should use @string resource [HardcodedText]
                            android:text="Target Amount"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:148: Warning: Hardcoded string "Enter target amount", should use @string resource [HardcodedText]
                                android:hint="Enter target amount"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:176: Warning: Hardcoded string "Budget Period", should use @string resource [HardcodedText]
                                android:text="Budget Period"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:187: Warning: Hardcoded string "Select Date", should use @string resource [HardcodedText]
                                android:text="Select Date"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:197: Warning: Hardcoded string "Pick Date", should use @string resource [HardcodedText]
                            android:text="Pick Date" />
                            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:205: Warning: Hardcoded string "Save Settings", should use @string resource [HardcodedText]
                        android:text="Save Settings" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:228: Warning: Hardcoded string "App Settings", should use @string resource [HardcodedText]
                        android:text="App Settings"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:268: Warning: Hardcoded string "Current Budget Summary", should use @string resource [HardcodedText]
                        android:text="Current Budget Summary"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:285: Warning: Hardcoded string "Balance:", should use @string resource [HardcodedText]
                            android:text="Balance:"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:293: Warning: Hardcoded string "$0.00", should use @string resource [HardcodedText]
                            android:text="$0.00"
                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:310: Warning: Hardcoded string "Target:", should use @string resource [HardcodedText]
                            android:text="Target:"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:318: Warning: Hardcoded string "$0.00", should use @string resource [HardcodedText]
                            android:text="$0.00"
                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:334: Warning: Hardcoded string "Budget:", should use @string resource [HardcodedText]
                            android:text="Budget:"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\res\layout\settings.xml:342: Warning: Hardcoded string "$0.00", should use @string resource [HardcodedText]
                            android:text="$0.00"
                            ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

37 errors, 287 warnings
