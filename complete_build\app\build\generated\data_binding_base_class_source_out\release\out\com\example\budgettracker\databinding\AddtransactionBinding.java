// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class AddtransactionBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final TextView budgetInfo;

  @NonNull
  public final EditText editText4;

  @NonNull
  public final EditText editText5;

  @NonNull
  public final TextView emptyTransactionsMessage;

  @NonNull
  public final BaseBinding footer;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final Button saveButton;

  @NonNull
  public final RelativeLayout spinnerContainer;

  private AddtransactionBinding(@NonNull RelativeLayout rootView, @NonNull ImageButton backButton,
      @NonNull TextView budgetInfo, @NonNull EditText editText4, @NonNull EditText editText5,
      @NonNull TextView emptyTransactionsMessage, @NonNull BaseBinding footer,
      @NonNull LinearLayout headerLayout, @NonNull RecyclerView recyclerView,
      @NonNull RelativeLayout rootLayout, @NonNull Button saveButton,
      @NonNull RelativeLayout spinnerContainer) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.budgetInfo = budgetInfo;
    this.editText4 = editText4;
    this.editText5 = editText5;
    this.emptyTransactionsMessage = emptyTransactionsMessage;
    this.footer = footer;
    this.headerLayout = headerLayout;
    this.recyclerView = recyclerView;
    this.rootLayout = rootLayout;
    this.saveButton = saveButton;
    this.spinnerContainer = spinnerContainer;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static AddtransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AddtransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.addtransaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AddtransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.budget_info;
      TextView budgetInfo = ViewBindings.findChildViewById(rootView, id);
      if (budgetInfo == null) {
        break missingId;
      }

      id = R.id.editText4;
      EditText editText4 = ViewBindings.findChildViewById(rootView, id);
      if (editText4 == null) {
        break missingId;
      }

      id = R.id.editText5;
      EditText editText5 = ViewBindings.findChildViewById(rootView, id);
      if (editText5 == null) {
        break missingId;
      }

      id = R.id.empty_transactions_message;
      TextView emptyTransactionsMessage = ViewBindings.findChildViewById(rootView, id);
      if (emptyTransactionsMessage == null) {
        break missingId;
      }

      id = R.id.footer;
      View footer = ViewBindings.findChildViewById(rootView, id);
      if (footer == null) {
        break missingId;
      }
      BaseBinding binding_footer = BaseBinding.bind(footer);

      id = R.id.header_layout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      RelativeLayout rootLayout = (RelativeLayout) rootView;

      id = R.id.save_button;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      return new AddtransactionBinding((RelativeLayout) rootView, backButton, budgetInfo, editText4,
          editText5, emptyTransactionsMessage, binding_footer, headerLayout, recyclerView,
          rootLayout, saveButton, spinnerContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
