{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-58:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "34,38,39,40", "startColumns": "4,4,4,4", "startOffsets": "6628,7462,7661,7872", "endColumns": "199,198,210,201", "endOffsets": "6823,7656,7867,8069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,11253", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,11434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,300,515,724,917,1153,1358,1572,1791,2023,2264,2494,2715,2948,3150,3401,3622,3851,4092,4314,4537,4733,4959,5144,5357,5556,5785", "endColumns": "244,214,208,192,235,204,213,218,231,240,229,220,232,201,250,220,228,240,221,222,195,225,184,212,198,228,198", "endOffsets": "295,510,719,912,1148,1353,1567,1786,2018,2259,2489,2710,2943,3145,3396,3617,3846,4087,4309,4532,4728,4954,5139,5352,5551,5780,5979"}, "to": {"startLines": "31,32,33,35,36,37,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5959,6204,6419,6828,7021,7257,8074,8288,8507,8739,8980,9210,9431,9664,9866,10117,10338,10567,10808,11030,11643,11839,12065,12250,12463,12662,12891", "endColumns": "244,214,208,192,235,204,213,218,231,240,229,220,232,201,250,220,228,240,221,222,195,225,184,212,198,228,198", "endOffsets": "6199,6414,6623,7016,7252,7457,8283,8502,8734,8975,9205,9426,9659,9861,10112,10333,10562,10803,11025,11248,11834,12060,12245,12458,12657,12886,13085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "203", "endOffsets": "254"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "11439", "endColumns": "203", "endOffsets": "11638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,270", "endColumns": "214,215", "endOffsets": "265,481"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "5528,5743", "endColumns": "214,215", "endOffsets": "5738,5954"}}]}]}