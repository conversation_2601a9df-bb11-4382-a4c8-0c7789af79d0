<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.budgettracker.ExpensesActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.LanguageActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.RegisterActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.SettingsActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.NotificationActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.DashboardActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.TransactionsActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.ProfileActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="NotificationPermission">
        <entry
            name="className"
            string="com/google/firebase/messaging/DisplayNotification"/>
        <entry
            name="source"
            boolean="true"/>
        <location id="class"
            file="$GRADLE_USER_HOME/caches/transforms-4/2699a329bcfc4e2352aad3337847a1e7/transformed/jetified-firebase-messaging-24.0.0/jars/classes.jar"/>
    </map>
    <map id="AppBundleLocaleChanges">
        <location id="localeChangeLocation"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/LanguageActivity.kt"
            line="41"
            column="9"
            startOffset="1319"
            endLine="41"
            endColumn="33"
            endOffset="1343"/>
    </map>
    <map id="UnusedResources">
        <location id="R.string.today"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1228"
            endLine="24"
            endColumn="25"
            endOffset="1240"/>
        <location id="R.color.md_theme_light_inversePrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="43"
            column="12"
            startOffset="2327"
            endLine="43"
            endColumn="48"
            endOffset="2363"/>
        <location id="R.string.budgethero"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="790"
            endLine="16"
            endColumn="30"
            endOffset="807"/>
        <location id="R.drawable.button_ripple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_ripple.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="10"
            endOffset="227"/>
        <location id="R.string.budget"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="77"
            column="13"
            startOffset="3989"
            endLine="77"
            endColumn="26"
            endOffset="4002"/>
        <location id="R.string.action_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="114"
            endLine="4"
            endColumn="35"
            endOffset="136"/>
        <location id="R.string.set"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="81"
            column="13"
            startOffset="4177"
            endLine="81"
            endColumn="23"
            endOffset="4187"/>
        <location id="R.string.previous"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="339"
            endLine="8"
            endColumn="28"
            endOffset="354"/>
        <location id="R.string.date_x"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="3766"
            endLine="72"
            endColumn="26"
            endOffset="3779"/>
        <location id="R.string.meta"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="1881"
            endLine="39"
            endColumn="24"
            endOffset="1892"/>
        <location id="R.color.primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="203"
            endLine="6"
            endColumn="26"
            endOffset="217"/>
        <location id="R.string.you_ve_received_25_from_sarah"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="2403"
            endLine="47"
            endColumn="49"
            endOffset="2439"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="1731"/>
        <location id="R.string.you_ve_spent_70_of_your_budget_for_the_month"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="2118"
            endLine="43"
            endColumn="64"
            endOffset="2169"/>
        <entry
            name="model"
            string="anim[item_animation_fall_down(U)],array[categories_array(U)],attr[colorControlNormal(R),colorControlHighlight(E),colorSurface(R),colorOutline(R),colorOnSurfaceVariant(R),colorOnPrimary(R),colorPrimary(R),colorPrimaryContainer(R),colorSurfaceVariant(R),colorSecondaryContainer(R),colorSecondary(R),actionBarSize(E),colorOnSurface(R),selectableItemBackgroundBorderless(R),colorOnSecondary(R),colorTertiary(R),colorOnTertiary(R),colorOnSecondaryContainer(R),colorOutlineVariant(R)],color[primary_light(U),primary_variant(U),scrim_overlay(U),white(U),radio_dot_color(U),text_color(U),black(U),primary(D),steelblue(D),onPrimary(D),primary_blue(D),border_color(D),back_ground(D),md_theme_light_primary(U),md_theme_light_onPrimary(U),md_theme_light_primaryContainer(U),md_theme_light_onPrimaryContainer(U),md_theme_light_secondary(U),md_theme_light_onSecondary(U),md_theme_light_secondaryContainer(U),md_theme_light_onSecondaryContainer(U),md_theme_light_tertiary(U),md_theme_light_onTertiary(U),md_theme_light_tertiaryContainer(U),md_theme_light_onTertiaryContainer(U),md_theme_light_error(U),md_theme_light_onError(U),md_theme_light_errorContainer(U),md_theme_light_onErrorContainer(U),md_theme_light_background(U),md_theme_light_onBackground(U),md_theme_light_surface(U),md_theme_light_onSurface(U),md_theme_light_surfaceVariant(U),md_theme_light_onSurfaceVariant(U),md_theme_light_outline(D),md_theme_light_outlineVariant(D),md_theme_light_scrim(D),md_theme_light_inverseSurface(U),md_theme_light_inverseOnSurface(U),md_theme_light_inversePrimary(D),material_dynamic_primary10(E),material_dynamic_primary20(E),material_dynamic_secondary10(E),material_dynamic_secondary20(E),material_dynamic_secondary30(E),material_dynamic_secondary50(E)],dimen[fab_margin(D)],drawable[attach_money(U),back(U),backg_button(D),button_background(U),button_danger_background(U),button_ripple(D),card_background(U),chip_background(U),circle_background(U),credit_card(U),edittext_background(U),expand_more(U),eye_close(U),eye_icon(U),fab_label_background(U),fingerprint(U),gradient_primary_background(U),home(U),ic_add(U),ic_launcher_background(D),ic_launcher_foreground(D),ic_launcher_foreground_1(E),icon_button_background(D),logo(U),logos(D),logout(U),mail_24px(D),medium_button_background(U),modern_button_background(D),monitoring(D),nav_button_background(U),notification_info_background(U),notification_success_background(U),notification_warning_background(U),notifications(U),person(U),search(D),settings_24px(D),shopping(U),small_button_background(U),small_chip_background(U),small_chip_selected_background(U),spinner_background(U),spinner_press(U),spinner_select(U),spinner_normal(U),star_24px(U),transaction_icon_background(U),transaction_type_chip(U),warning_24px(U),work_24px(D)],id[toolbar(D),fab(D),root_layout(U),header_layout(U),back_button(U),textView(D),footer(U),category_spinner(U),editText2(U),editText4(U),editText3(U),date_picker_button(U),save_button(U),spinner_container(D),budget_info(U),editText5(U),empty_transactions_message(U),recyclerView(U),button_home(U),button_expenses(U),button_transactions(U),button_notification(U),button_profile(U),nav_host_fragment_content_main(U),app_logo(D),firstTextView(D),secondTextView(U),button_logout(U),budget_id(U),spending_vs_target(U),balance_vs_budget(U),total_expenditure(U),graph(U),btn_1d(U),btn_1w(U),btn_1m(U),btn_3m(U),btn_1y(U),itemName(U),fab_label_expense(D),fab_add_expense(U),fab_label_budget(D),fab_add_budget(U),fab_add_transaction(D),notification_icon_background(U),notification_icon(U),notification_title(U),notification_message(U),notification_timestamp(U),radio_group(U),radio_english(U),radio_afrikaans(U),radio_zulu(U),list_icon(D),itemDate(U),itemAmount(U),expand_button(U),expandable_details(U),itemFullDescription(U),itemCategory(U),welcome_icon(U),description(U),email_container(U),email_input(U),password_container(U),password_input(U),password_eye(U),login_button(U),or_login_with(U),biometric_card(U),fingerprint_icon(U),fingerprint_instead(U),signup_instead(U),clear_all_button(U),notifications_recycler_view(U),empty_state(U),register_logo(D),username_container(D),username_input(U),password_container_confrim(D),password_input_confrim(U),password_eye_confrim(U),signup_button(U),title(D),title_date(U),buttonLanguageSettings(U),buttonClearUserData(U),trans_balance(U),trans_target(U),trans_budget(U),loading_spinner(U),loading_text(D),action_settings(D),nav_graph(D),FirstFragment(U),action_FirstFragment_to_SecondFragment(R),closeIcon(D),profile_section(D),header_section(D)],layout[activity_main(D),content_main(D),addexpense(U),base(U),spinner(U),addtransaction(U),dashboard(U),item_notification(U),language(U),list_bug(U),list_item(U),login(U),notifications(U),register(U),settings(U),spinner_item(D)],menu[bottom_navigation_menu(D),menu_main(D)],mipmap[logo(U),ic_launcher(D),ic_launcher_round(D),logos(D)],navigation[nav_graph(D)],string[app_name(U),todo(U),back_description(U),add_a_new_expense(U),pick_date(U),add_expense(U),add(U),home_description(U),expense_description(U),transaction_description(U),notifications(U),profile_description(U),logo_name(U),user_name(U),account_balance(U),_0(U),_1d(U),_1w(U),_1m(U),_3m(U),_1y(U),heading_list_dash(U),view_all(U),english(U),afrikaans(U),zulu(U),language(U),name_bug(U),exp_date(U),bug_amount(U),ext_name(U),exp_amount(U),your_personal_budget_tracker(U),enter_email(U),enter_password(U),log_in(U),or_log_in_with(U),fingerprint_icon_description(U),touch_id(U),sign_up(U),clear_all(U),settings(U),language_settings(U),clear_user_data(U),action_settings(D),first_fragment_label(D),second_fragment_label(D),next(D),previous(D),search_description(D),chart_description(D),budgethero(D),today(D),add_a_note(D),select_category(D),_0_00(D),save(D),_5_943(D),last_30_days(D),_12(D),meta(D),meta_platforms(D),warning_icon(D),you_ve_spent_70_of_your_budget_for_the_month(D),budget_alert(D),currency_icon(D),transaction(D),you_ve_received_25_from_sarah(D),type(D),budget_info(U),add_money(D),user_profile(D),date_x(D),main_cash(D),month(D),budget(D),target(D),balance(D),set_new_target(D),set(D)],style[Base_Theme_BudgetHero(U),SmallButton(U),MediumButton(U),AppTheme(D),Theme_MaterialComponents_Light_NoActionBar(E),App_Widget_BottomNavigationView_ActiveIndicator(D),Widget_Material3_BottomNavigationView_ActiveIndicator(E),IconButton(D),Theme_Material3_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U)];45^2,46^2,4a^3^48,4b^4^5,4d^15^16,4e^2,50^6,51^2,52^2,53^4^5,54^2,56^2,57^7,59^5a,5e^2,5f^2,60^8,62^2,63^9,67^2,68^2,69^2,6a^2,6b^2,6c^8,6d^a^5,6e^8,6f^70^71^72,73^2,74^b^c,75^b^c,76^2,77^2,db^d^dc^f3^44,dc^f1,dd^4^48^f4^46^e^f5^7e^7b^6f^4f^f6^143^f7^144^de^17^df,de^4^63^f9^56^e^fa^45^fb^4e^fc^67^fd^68,df^d2,e0^4^48^f4^46^e^7e^7b^55^7^4f^f8^144^45^8^f^6^de^17^df,e1^4^5c^fe^8^48^fc^67^e^ff^5e^7e^7b^55^100^7^101^4c^18^6d^102^103^6e^104^105^106^107^108^de^53^a0^c^45^10^a2^11^4e^12^57^17^df,e2^64^67^18^e^8,e3^18^19^109^1a^10a^10b^48^f4^46^1b^7c^10c^de,e4^74^45^13^10d^e^10e^6^10f^c^75,e5^4d^6b^8^110^e^10e^6^111^f^50^14^4c,e6^4^f3^5c^b4^112^e^4f^b5^113^b6^114^52^b8^115^144^bb^116^4b^bc^117^54^8^118^bd^119^17^df,e7^4^48^f4^46^e^fc^8^11a^7^143^7e^7b^67^de,e8^4^48^f4^46^e^7b^5c^4f^52^144^17^df,e9^4^48^f4^46^e^11b^7e^7b^4f^143^144^11c^49^11d^8^de^17^df,eb^56^f9^45^fa^62^fb^67^fc^68^fd,ec^11e,ee^58^59,ef^58^59,f1^d6^11f^e6,142^14a^22^23^24^25^26^27^28^29^2a^2b^2c^2d^2e^2f^30^31^34^35^36^37^3b^3c^32^33,143^18^6c,144^18^60,145^146^3e^3f^40^41^42^43,147^148^9,149^5b^e;;;"/>
        <location id="R.string.month"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="75"
            column="13"
            startOffset="3894"
            endLine="75"
            endColumn="25"
            endOffset="3906"/>
        <location id="R.style.IconButton"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="44"
            column="12"
            startOffset="2173"
            endLine="44"
            endColumn="29"
            endOffset="2190"/>
        <location id="R.mipmap.ic_launcher"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp"/>
        <location id="R.menu.bottom_navigation_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="27"
            endColumn="8"
            endOffset="906"/>
        <location id="R.mipmap.logos"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/logos.png"/>
        <location id="R.string.target"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="4032"
            endLine="78"
            endColumn="26"
            endOffset="4045"/>
        <location id="R.drawable.backg_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/backg_button.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="8"
            endColumn="9"
            endOffset="344"/>
        <location id="R.layout.spinner_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/spinner_item.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="31"
            endOffset="344"/>
        <location id="R.string.currency_icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2293"
            endLine="45"
            endColumn="33"
            endOffset="2313"/>
        <location id="R.color.md_theme_light_outlineVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="39"
            column="12"
            startOffset="2074"
            endLine="39"
            endColumn="48"
            endOffset="2110"/>
        <location id="R.menu.menu_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="8"
            endOffset="422"/>
        <location id="R.string.select_category"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1320"
            endLine="26"
            endColumn="35"
            endOffset="1342"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="170"
            endColumn="10"
            endOffset="5774"/>
        <location id="R.layout.activity_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="34"
            endColumn="55"
            endOffset="1422"/>
        <location id="R.string.second_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="233"
            endLine="6"
            endColumn="41"
            endOffset="261"/>
        <location id="R.color.steelblue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="246"
            endLine="7"
            endColumn="28"
            endOffset="262"/>
        <location id="R.color.md_theme_light_scrim"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="40"
            column="12"
            startOffset="2139"
            endLine="40"
            endColumn="39"
            endOffset="2166"/>
        <location id="R.string._0_00"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1381"
            endLine="27"
            endColumn="25"
            endOffset="1393"/>
        <location id="R.color.primary_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="336"
            endLine="9"
            endColumn="31"
            endOffset="355"/>
        <location id="R.drawable.logos"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logos.png"/>
        <location id="R.string.save"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1419"
            endLine="28"
            endColumn="24"
            endOffset="1430"/>
        <location id="R.color.md_theme_light_outline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="38"
            column="12"
            startOffset="2016"
            endLine="38"
            endColumn="41"
            endOffset="2045"/>
        <location id="R.style.AppTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="27"
            endOffset="79"/>
        <location id="R.string.main_cash"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="73"
            column="13"
            startOffset="3807"
            endLine="73"
            endColumn="29"
            endOffset="3823"/>
        <location id="R.string.meta_platforms"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="2004"
            endLine="41"
            endColumn="34"
            endOffset="2025"/>
        <location id="R.string.next"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="300"
            endLine="7"
            endColumn="24"
            endOffset="311"/>
        <location id="R.string.search_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="386"
            endLine="9"
            endColumn="38"
            endOffset="411"/>
        <location id="R.layout.content_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="18"
            endColumn="53"
            endOffset="894"/>
        <location id="R.string.add_money"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="3025"
            endLine="57"
            endColumn="29"
            endOffset="3041"/>
        <location id="R.drawable.modern_button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/modern_button_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="293"/>
        <location id="R.string.first_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="168"
            endLine="5"
            endColumn="40"
            endOffset="195"/>
        <location id="R.navigation.nav_graph"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/navigation/nav_graph.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="26"
            endColumn="14"
            endOffset="1020"/>
        <location id="R.string.budget_alert"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2238"
            endLine="44"
            endColumn="32"
            endOffset="2257"/>
        <location id="R.drawable.work_24px"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/work_24px.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="1045"/>
        <location id="R.string.warning_icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="2063"
            endLine="42"
            endColumn="32"
            endOffset="2082"/>
        <location id="R.string._12"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="1663"
            endLine="33"
            endColumn="23"
            endOffset="1673"/>
        <location id="R.string._5_943"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1567"
            endLine="31"
            endColumn="26"
            endOffset="1580"/>
        <location id="R.drawable.settings_24px"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="1717"/>
        <location id="R.drawable.monitoring"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="11"
            endColumn="10"
            endOffset="648"/>
        <location id="R.drawable.icon_button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/icon_button_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="292"/>
        <location id="R.color.onPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="291"
            endLine="8"
            endColumn="28"
            endOffset="307"/>
        <location id="R.color.back_ground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="529"
            endLine="13"
            endColumn="30"
            endOffset="547"/>
        <location id="R.string.transaction"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="2350"
            endLine="46"
            endColumn="31"
            endOffset="2368"/>
        <location id="R.string.last_30_days"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="1610"
            endLine="32"
            endColumn="32"
            endOffset="1629"/>
        <location id="R.string.add_a_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1269"
            endLine="25"
            endColumn="30"
            endOffset="1286"/>
        <location id="R.color.border_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="430"
            endLine="11"
            endColumn="31"
            endOffset="449"/>
        <location id="R.string.user_profile"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="3247"
            endLine="61"
            endColumn="32"
            endOffset="3266"/>
        <location id="R.string.type"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2790"
            endLine="53"
            endColumn="24"
            endOffset="2801"/>
        <location id="R.string.set_new_target"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="4120"
            endLine="80"
            endColumn="34"
            endOffset="4141"/>
        <location id="R.string.chart_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="441"
            endLine="10"
            endColumn="37"
            endOffset="465"/>
        <location id="R.drawable.mail_24px"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mail_24px.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="777"/>
        <location id="R.style.App_Widget_BottomNavigationView_ActiveIndicator"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="14"
            column="12"
            startOffset="748"
            endLine="14"
            endColumn="66"
            endOffset="802"/>
        <location id="R.dimen.fab_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="2"
            column="12"
            startOffset="24"
            endLine="2"
            endColumn="29"
            endOffset="41"/>
        <location id="R.drawable.search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="687"/>
        <location id="R.mipmap.ic_launcher_round"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp"/>
        <location id="R.string.balance"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="79"
            column="13"
            startOffset="4075"
            endLine="79"
            endColumn="27"
            endOffset="4089"/>
    </map>

</incidents>
