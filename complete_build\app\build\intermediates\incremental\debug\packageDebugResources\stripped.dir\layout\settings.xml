<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root_layout"
    android:background="?attr/colorSurface">

    <!-- Modern Header -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:scaleType="fitCenter"
            android:tint="?attr/colorOnSurface" />

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="@string/settings"
            android:textColor="?attr/colorOnSurface"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_layout"
        android:layout_above="@id/footer"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Budget Settings Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Budget Settings"
                        android:textColor="?attr/colorOnSurface"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginBottom="16dp" />

                    <!-- Budget Amount -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Monthly Budget"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:background="@drawable/edittext_background"
                            android:padding="12dp">

                            <EditText
                                android:id="@+id/editText2"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="Enter budget amount"
                                android:background="@android:color/transparent"
                                android:inputType="numberDecimal"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Target Amount -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Target Amount"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:background="@drawable/edittext_background"
                            android:padding="12dp">

                            <EditText
                                android:id="@+id/editText4"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="Enter target amount"
                                android:background="@android:color/transparent"
                                android:inputType="numberDecimal"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Date Picker -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Budget Period"
                                android:textColor="?attr/colorOnSurface"
                                android:textSize="14sp"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:id="@+id/title_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/edittext_background"
                                android:padding="12dp"
                                android:text="Select Date"
                                android:textColor="?attr/colorOnSurface"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <Button
                            android:id="@+id/date_picker_button"
                            style="@style/SmallButton"
                            android:layout_marginStart="16dp"
                            android:text="Pick Date" />

                    </LinearLayout>

                    <!-- Save Button -->
                    <Button
                        android:id="@+id/save_button"
                        style="@style/MediumButton"
                        android:text="Save Settings" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App Settings Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="App Settings"
                        android:textColor="?attr/colorOnSurface"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginBottom="16dp" />

                    <Button
                        android:id="@+id/buttonLanguageSettings"
                        style="@style/MediumButton"
                        android:layout_marginBottom="12dp"
                        android:text="@string/language_settings" />

                    <Button
                        android:id="@+id/buttonClearUserData"
                        style="@style/MediumButton"
                        android:background="@drawable/button_danger_background"
                        android:text="@string/clear_user_data" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Budget Summary Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="80dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Current Budget Summary"
                        android:textColor="?attr/colorOnSurface"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Balance:"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/trans_balance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$0.00"
                            android:textColor="?attr/colorPrimary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Target:"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/trans_target"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$0.00"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Budget:"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/trans_budget"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$0.00"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>
    <include
        android:id="@+id/footer"
        layout="@layout/base"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

    <!-- Loading Spinner Overlay -->
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="@color/scrim_overlay">

        <include layout="@layout/spinner" />

    </RelativeLayout>

</RelativeLayout>
