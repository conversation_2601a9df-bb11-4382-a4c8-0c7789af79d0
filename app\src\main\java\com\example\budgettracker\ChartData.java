package com.example.budgettracker;

public class ChartData {
    private String date;
    private int amount;

    // Constructors
    public ChartData(String date, int amount) {
        this.date = date;
        this.amount = amount;
    }

    // Getters and Setters
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }
    public int getXValue() {
        return Integer.parseInt(date.replace("/", ""));
    }

    public double getYValue() {
            return amount;
    }
}
