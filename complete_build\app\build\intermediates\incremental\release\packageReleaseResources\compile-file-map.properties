#Tue Jun 10 00:30:29 EAT 2025
com.example.budgettracker.app-main-5\:/anim/item_animation_fall_down.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\item_animation_fall_down.xml
com.example.budgettracker.app-main-5\:/drawable/attach_money.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\attach_money.xml
com.example.budgettracker.app-main-5\:/drawable/back.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\back.xml
com.example.budgettracker.app-main-5\:/drawable/backg_button.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\backg_button.xml
com.example.budgettracker.app-main-5\:/drawable/button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_background.xml
com.example.budgettracker.app-main-5\:/drawable/button_danger_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_danger_background.xml
com.example.budgettracker.app-main-5\:/drawable/button_ripple.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\button_ripple.xml
com.example.budgettracker.app-main-5\:/drawable/card_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\card_background.xml
com.example.budgettracker.app-main-5\:/drawable/chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\chip_background.xml
com.example.budgettracker.app-main-5\:/drawable/circle_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\circle_background.xml
com.example.budgettracker.app-main-5\:/drawable/credit_card.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\credit_card.xml
com.example.budgettracker.app-main-5\:/drawable/edittext_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\edittext_background.xml
com.example.budgettracker.app-main-5\:/drawable/expand_more.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\expand_more.xml
com.example.budgettracker.app-main-5\:/drawable/eye_close.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\eye_close.xml
com.example.budgettracker.app-main-5\:/drawable/eye_icon.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\eye_icon.xml
com.example.budgettracker.app-main-5\:/drawable/fab_label_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\fab_label_background.xml
com.example.budgettracker.app-main-5\:/drawable/fingerprint.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\fingerprint.xml
com.example.budgettracker.app-main-5\:/drawable/gradient_primary_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\gradient_primary_background.xml
com.example.budgettracker.app-main-5\:/drawable/home.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\home.xml
com.example.budgettracker.app-main-5\:/drawable/ic_add.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_add.xml
com.example.budgettracker.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_background.xml
com.example.budgettracker.app-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_foreground.xml
com.example.budgettracker.app-main-5\:/drawable/icon_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_button_background.xml
com.example.budgettracker.app-main-5\:/drawable/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logo.png
com.example.budgettracker.app-main-5\:/drawable/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logos.png
com.example.budgettracker.app-main-5\:/drawable/logout.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\logout.xml
com.example.budgettracker.app-main-5\:/drawable/mail_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\mail_24px.xml
com.example.budgettracker.app-main-5\:/drawable/medium_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\medium_button_background.xml
com.example.budgettracker.app-main-5\:/drawable/modern_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\modern_button_background.xml
com.example.budgettracker.app-main-5\:/drawable/monitoring.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\monitoring.xml
com.example.budgettracker.app-main-5\:/drawable/nav_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\nav_button_background.xml
com.example.budgettracker.app-main-5\:/drawable/notification_info_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\notification_info_background.xml
com.example.budgettracker.app-main-5\:/drawable/notification_success_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\notification_success_background.xml
com.example.budgettracker.app-main-5\:/drawable/notification_warning_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\notification_warning_background.xml
com.example.budgettracker.app-main-5\:/drawable/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\notifications.xml
com.example.budgettracker.app-main-5\:/drawable/person.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\person.xml
com.example.budgettracker.app-main-5\:/drawable/search.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\search.xml
com.example.budgettracker.app-main-5\:/drawable/settings_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\settings_24px.xml
com.example.budgettracker.app-main-5\:/drawable/shopping.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\shopping.xml
com.example.budgettracker.app-main-5\:/drawable/small_button_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\small_button_background.xml
com.example.budgettracker.app-main-5\:/drawable/small_chip_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\small_chip_background.xml
com.example.budgettracker.app-main-5\:/drawable/small_chip_selected_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\small_chip_selected_background.xml
com.example.budgettracker.app-main-5\:/drawable/spinner_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\spinner_background.xml
com.example.budgettracker.app-main-5\:/drawable/spinner_normal.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\spinner_normal.xml
com.example.budgettracker.app-main-5\:/drawable/spinner_press.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\spinner_press.xml
com.example.budgettracker.app-main-5\:/drawable/spinner_select.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\spinner_select.xml
com.example.budgettracker.app-main-5\:/drawable/star_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\star_24px.xml
com.example.budgettracker.app-main-5\:/drawable/transaction_icon_background.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\transaction_icon_background.xml
com.example.budgettracker.app-main-5\:/drawable/transaction_type_chip.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\transaction_type_chip.xml
com.example.budgettracker.app-main-5\:/drawable/warning_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\warning_24px.xml
com.example.budgettracker.app-main-5\:/drawable/work_24px.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\work_24px.xml
com.example.budgettracker.app-main-5\:/menu/bottom_navigation_menu.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\menu\\bottom_navigation_menu.xml
com.example.budgettracker.app-main-5\:/menu/menu_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\menu\\menu_main.xml
com.example.budgettracker.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.budgettracker.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.budgettracker.app-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-5\:/mipmap-hdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\logo.png
com.example.budgettracker.app-main-5\:/mipmap-hdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\logos.png
com.example.budgettracker.app-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-5\:/mipmap-mdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\logo.png
com.example.budgettracker.app-main-5\:/mipmap-mdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\logos.png
com.example.budgettracker.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-5\:/mipmap-xhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\logo.png
com.example.budgettracker.app-main-5\:/mipmap-xhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\logos.png
com.example.budgettracker.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-5\:/mipmap-xxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\logo.png
com.example.budgettracker.app-main-5\:/mipmap-xxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\logos.png
com.example.budgettracker.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.budgettracker.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.budgettracker.app-main-5\:/mipmap-xxxhdpi/logo.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\logo.png
com.example.budgettracker.app-main-5\:/mipmap-xxxhdpi/logos.png=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\logos.png
com.example.budgettracker.app-main-5\:/navigation/nav_graph.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\navigation\\nav_graph.xml
com.example.budgettracker.app-main-5\:/xml/backup_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\backup_rules.xml
com.example.budgettracker.app-main-5\:/xml/data_extraction_rules.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\data_extraction_rules.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/activity_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_main.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/addexpense.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\addexpense.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/addtransaction.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\addtransaction.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/base.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\base.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/content_main.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\content_main.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/dashboard.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dashboard.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/item_notification.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_notification.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/language.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\language.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/list_bug.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\list_bug.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/list_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\list_item.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/login.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\login.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/notifications.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\notifications.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/register.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\register.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/settings.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\settings.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/spinner.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\spinner.xml
com.example.budgettracker.app-packageReleaseResources-3\:/layout/spinner_item.xml=C\:\\Users\\user\\Desktop\\New folder\\BudgetTracker\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\spinner_item.xml
