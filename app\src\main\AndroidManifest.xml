<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- Permission to use biometric authentication -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Feature declaration for fingerprint hardware -->
    <uses-feature android:name="android.hardware.fingerprint" android:required="false" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/logo"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/logo"
        android:supportsRtl="true"
        android:theme="@style/Base.Theme.BudgetTracker"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Base.Theme.BudgetTracker">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".DashboardActivity"
            android:exported="true" />

        <activity android:name=".ExpensesActivity"  android:exported="true"/>
        <activity android:name=".TransactionsActivity"  android:exported="true"/>
        <activity android:name=".ProfileActivity"  android:exported="true"/>
        <activity android:name=".SettingsActivity"  android:exported="true"/>
        <activity android:name=".LanguageActivity"  android:exported="true"/>
        <activity android:name=".NotificationActivity"  android:exported="true"/>
        <activity android:name=".RegisterActivity"  android:exported="true"/>

        <!-- Firebase Cloud Messaging Service -->
        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>

</manifest>