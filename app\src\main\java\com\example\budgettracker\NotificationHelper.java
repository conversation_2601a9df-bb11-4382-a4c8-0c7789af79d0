package com.example.budgettracker;

import android.annotation.SuppressLint;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

public class NotificationHelper {

    private static final String CHANNEL_ID = "budget_notifications_channel";

    public static void createNotificationChannel(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CharSequence name = "Budget Notifications";
            String description = "Channel for budget notifications";
            int importance = NotificationManager.IMPORTANCE_HIGH;  // High importance for pop-ups
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name, importance);
            channel.setDescription(description);
            channel.enableLights(true);  // Enable LED light
            channel.enableVibration(true);  // Enable vibration
            channel.setShowBadge(true);  // Show badge on app icon
            channel.setLockscreenVisibility(android.app.Notification.VISIBILITY_PUBLIC);  // Show on lock screen

            NotificationManager notificationManager = context.getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }


    public static void sendNotification(Context context, String title, String content) {
        // Check for notification permission on Android 13+ (API 33+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ActivityCompat.checkSelfPermission(context, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                // Permission not granted, log and return
                android.util.Log.w("NotificationHelper", "Notification permission not granted");
                return;
            }
        }

        // Create notification channel if needed
        createNotificationChannel(context);

        // Create intent for when notification is tapped
        Intent intent = new Intent(context, DashboardActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);

        @SuppressLint("UnspecifiedImmutableFlag")
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ?
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE :
                PendingIntent.FLAG_UPDATE_CURRENT
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.notifications)
                .setContentTitle(title)
                .setContentText(content)
                .setPriority(NotificationCompat.PRIORITY_HIGH)  // High priority for pop-up
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL)  // Sound, vibration, lights
                .setStyle(new NotificationCompat.BigTextStyle().bigText(content))
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)  // Show on lock screen
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)  // Message category for pop-up
                .setFullScreenIntent(pendingIntent, false);  // Helps with pop-up display

        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);

        try {
            notificationManager.notify((int) System.currentTimeMillis(), builder.build());
            android.util.Log.d("NotificationHelper", "Notification sent: " + title);
        } catch (SecurityException e) {
            android.util.Log.e("NotificationHelper", "Failed to send notification: " + e.getMessage());
        }
    }

    // Test method to send a test notification
    public static void sendTestNotification(Context context) {
        sendNotification(context, "🎉 Test Notification", "If you can see this, notifications are working perfectly!");
    }
}
