[{"merged": "com.example.budgettracker.app-release-60:/layout_notifications.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/notifications.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_transaction_icon_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/transaction_icon_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_home.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/home.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_card_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/card_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_spinner_press.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_press.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-hdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-hdpi/logos.png"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_edittext_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/edittext_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_button_ripple.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/button_ripple.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_list_bug.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/list_bug.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_addtransaction.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/addtransaction.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-hdpi_logo.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-hdpi/logo.png"}, {"merged": "com.example.budgettracker.app-release-60:/layout_dashboard.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/dashboard.xml"}, {"merged": "com.example.budgettracker.app-release-60:/xml_backup_rules.xml.flat", "source": "com.example.budgettracker.app-main-61:/xml/backup_rules.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_nav_button_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/nav_button_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_spinner.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/spinner.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_spinner_item.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/spinner_item.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_item_notification.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/item_notification.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_search.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/search.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_shopping.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/shopping.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_backg_button.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/backg_button.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_modern_button_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/modern_button_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_eye_icon.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/eye_icon.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_eye_close.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/eye_close.xml"}, {"merged": "com.example.budgettracker.app-release-60:/menu_menu_main.xml.flat", "source": "com.example.budgettracker.app-main-61:/menu/menu_main.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxhdpi_logo.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxhdpi/logo.png"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxxhdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxxhdpi/logos.png"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_attach_money.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/attach_money.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-release-60:/anim_item_animation_fall_down.xml.flat", "source": "com.example.budgettracker.app-main-61:/anim/item_animation_fall_down.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_small_chip_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/small_chip_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_addexpense.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/addexpense.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_chip_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/chip_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-release-60:/layout_register.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/register.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_spinner_select.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_select.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_activity_main.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/activity_main.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_logout.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/logout.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxxhdpi_logo.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxxhdpi/logo.png"}, {"merged": "com.example.budgettracker.app-release-60:/xml_data_extraction_rules.xml.flat", "source": "com.example.budgettracker.app-main-61:/xml/data_extraction_rules.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-mdpi_logo.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-mdpi/logo.png"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_expand_more.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/expand_more.xml"}, {"merged": "com.example.budgettracker.app-release-60:/menu_bottom_navigation_menu.xml.flat", "source": "com.example.budgettracker.app-main-61:/menu/bottom_navigation_menu.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_logo.png.flat", "source": "com.example.budgettracker.app-main-61:/drawable/logo.png"}, {"merged": "com.example.budgettracker.app-release-60:/layout_language.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/language.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_list_item.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/list_item.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_settings.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/settings.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_small_button_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/small_button_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_warning_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/warning_24px.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_spinner_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_monitoring.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/monitoring.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_medium_button_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/medium_button_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_small_chip_selected_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/small_chip_selected_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_button_danger_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/button_danger_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_settings_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/settings_24px.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_notification_warning_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/notification_warning_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xhdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xhdpi/logos.png"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_fingerprint.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/fingerprint.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-mdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-mdpi/logos.png"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_fab_label_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/fab_label_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxhdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxhdpi/logos.png"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_icon_button_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/icon_button_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_notification_success_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/notification_success_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_star_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/star_24px.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_circle_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/circle_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_spinner_normal.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_normal.xml"}, {"merged": "com.example.budgettracker.app-release-60:/navigation_nav_graph.xml.flat", "source": "com.example.budgettracker.app-main-61:/navigation/nav_graph.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_work_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/work_24px.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_credit_card.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/credit_card.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/drawable/logos.png"}, {"merged": "com.example.budgettracker.app-release-60:/layout_base.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/base.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_gradient_primary_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/gradient_primary_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_notifications.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/notifications.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_notification_info_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/notification_info_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_person.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/person.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_mail_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/mail_24px.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_ic_add.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/ic_add.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_login.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/login.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xhdpi_logo.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xhdpi/logo.png"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_button_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/button_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_back.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/back.xml"}, {"merged": "com.example.budgettracker.app-release-60:/layout_content_main.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/content_main.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_transaction_type_chip.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/transaction_type_chip.xml"}, {"merged": "com.example.budgettracker.app-release-60:/drawable_ic_launcher_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.budgettracker.app-release-60:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xhdpi/ic_launcher_round.webp"}]