package com.example.budgettracker

class LoginRes {
    private var success: Boolean = false
    private var token: String? = null // Example field for a token or other response data

    fun isSuccess(): Boolean {
        return success
    }

    fun setSuccess(success: <PERSON>olean) {
        this.success = success
    }

    fun getToken(): String? {
        return token
    }

    fun setToken(token: String?) {
        this.token = token
    }
}
