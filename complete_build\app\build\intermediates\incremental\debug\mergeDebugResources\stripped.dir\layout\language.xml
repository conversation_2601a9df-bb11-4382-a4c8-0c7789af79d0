<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="16dp">
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:orientation="vertical"
        android:padding="16dp">
        <RadioGroup
            android:id="@+id/radio_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:ignore="UselessParent">

            <RadioButton
                android:id="@+id/radio_english"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:buttonTint="@color/radio_dot_color"
                android:text="@string/english"
                android:textColor="@color/text_color"
                android:padding="12dp"
                android:textSize="19sp"
                android:textStyle="bold"/>

            <RadioButton
                android:id="@+id/radio_afrikaans"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:buttonTint="@color/radio_dot_color"
                android:text="@string/afrikaans"
                android:textColor="@color/text_color"
                android:padding="12dp"
                android:textSize="19sp"
                android:textStyle="bold"

                />
            <RadioButton
                android:id="@+id/radio_zulu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:buttonTint="@color/radio_dot_color"
                android:text="@string/zulu"
                android:textColor="@color/text_color"
                android:padding="12dp"
                android:textSize="19sp"
                android:textStyle="bold"

                />


        </RadioGroup>
    </LinearLayout>

    <ImageButton
            android:id="@+id/back_button"
            android:layout_width="92dp"
            android:layout_height="80dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:tint="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseAppTint" />

    <TextView
        android:layout_width="249dp"
        android:layout_height="48dp"
        android:layout_below="@id/back_button"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="98dp"
        android:layout_marginTop="-61dp"
        android:layout_marginEnd="48dp"
        android:text="@string/language"
        android:textColor="@color/text_color"
        android:textSize="28sp"
        android:textStyle="bold" />

    <include
        android:id="@+id/footer"
        layout="@layout/base"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

</RelativeLayout>


