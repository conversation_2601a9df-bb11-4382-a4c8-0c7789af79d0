// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.jjoe64.graphview.GraphView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DashboardBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView appLogo;

  @NonNull
  public final TextView balanceVsBudget;

  @NonNull
  public final Button btn1d;

  @NonNull
  public final Button btn1m;

  @NonNull
  public final Button btn1w;

  @NonNull
  public final Button btn1y;

  @NonNull
  public final Button btn3m;

  @NonNull
  public final TextView budgetId;

  @NonNull
  public final ImageButton buttonLogout;

  @NonNull
  public final ImageButton buttonNotification;

  @NonNull
  public final FloatingActionButton fabAddBudget;

  @NonNull
  public final FloatingActionButton fabAddExpense;

  @NonNull
  public final FloatingActionButton fabAddTransaction;

  @NonNull
  public final TextView fabLabelBudget;

  @NonNull
  public final TextView fabLabelExpense;

  @NonNull
  public final TextView firstTextView;

  @NonNull
  public final BaseBinding footer;

  @NonNull
  public final GraphView graph;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final TextView itemName;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final TextView secondTextView;

  @NonNull
  public final TextView spendingVsTarget;

  @NonNull
  public final RelativeLayout spinnerContainer;

  @NonNull
  public final TextView totalExpenditure;

  private DashboardBinding(@NonNull RelativeLayout rootView, @NonNull ImageView appLogo,
      @NonNull TextView balanceVsBudget, @NonNull Button btn1d, @NonNull Button btn1m,
      @NonNull Button btn1w, @NonNull Button btn1y, @NonNull Button btn3m,
      @NonNull TextView budgetId, @NonNull ImageButton buttonLogout,
      @NonNull ImageButton buttonNotification, @NonNull FloatingActionButton fabAddBudget,
      @NonNull FloatingActionButton fabAddExpense, @NonNull FloatingActionButton fabAddTransaction,
      @NonNull TextView fabLabelBudget, @NonNull TextView fabLabelExpense,
      @NonNull TextView firstTextView, @NonNull BaseBinding footer, @NonNull GraphView graph,
      @NonNull LinearLayout headerLayout, @NonNull TextView itemName,
      @NonNull RecyclerView recyclerView, @NonNull RelativeLayout rootLayout,
      @NonNull TextView secondTextView, @NonNull TextView spendingVsTarget,
      @NonNull RelativeLayout spinnerContainer, @NonNull TextView totalExpenditure) {
    this.rootView = rootView;
    this.appLogo = appLogo;
    this.balanceVsBudget = balanceVsBudget;
    this.btn1d = btn1d;
    this.btn1m = btn1m;
    this.btn1w = btn1w;
    this.btn1y = btn1y;
    this.btn3m = btn3m;
    this.budgetId = budgetId;
    this.buttonLogout = buttonLogout;
    this.buttonNotification = buttonNotification;
    this.fabAddBudget = fabAddBudget;
    this.fabAddExpense = fabAddExpense;
    this.fabAddTransaction = fabAddTransaction;
    this.fabLabelBudget = fabLabelBudget;
    this.fabLabelExpense = fabLabelExpense;
    this.firstTextView = firstTextView;
    this.footer = footer;
    this.graph = graph;
    this.headerLayout = headerLayout;
    this.itemName = itemName;
    this.recyclerView = recyclerView;
    this.rootLayout = rootLayout;
    this.secondTextView = secondTextView;
    this.spendingVsTarget = spendingVsTarget;
    this.spinnerContainer = spinnerContainer;
    this.totalExpenditure = totalExpenditure;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_logo;
      ImageView appLogo = ViewBindings.findChildViewById(rootView, id);
      if (appLogo == null) {
        break missingId;
      }

      id = R.id.balance_vs_budget;
      TextView balanceVsBudget = ViewBindings.findChildViewById(rootView, id);
      if (balanceVsBudget == null) {
        break missingId;
      }

      id = R.id.btn_1d;
      Button btn1d = ViewBindings.findChildViewById(rootView, id);
      if (btn1d == null) {
        break missingId;
      }

      id = R.id.btn_1m;
      Button btn1m = ViewBindings.findChildViewById(rootView, id);
      if (btn1m == null) {
        break missingId;
      }

      id = R.id.btn_1w;
      Button btn1w = ViewBindings.findChildViewById(rootView, id);
      if (btn1w == null) {
        break missingId;
      }

      id = R.id.btn_1y;
      Button btn1y = ViewBindings.findChildViewById(rootView, id);
      if (btn1y == null) {
        break missingId;
      }

      id = R.id.btn_3m;
      Button btn3m = ViewBindings.findChildViewById(rootView, id);
      if (btn3m == null) {
        break missingId;
      }

      id = R.id.budget_id;
      TextView budgetId = ViewBindings.findChildViewById(rootView, id);
      if (budgetId == null) {
        break missingId;
      }

      id = R.id.button_logout;
      ImageButton buttonLogout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLogout == null) {
        break missingId;
      }

      id = R.id.button_notification;
      ImageButton buttonNotification = ViewBindings.findChildViewById(rootView, id);
      if (buttonNotification == null) {
        break missingId;
      }

      id = R.id.fab_add_budget;
      FloatingActionButton fabAddBudget = ViewBindings.findChildViewById(rootView, id);
      if (fabAddBudget == null) {
        break missingId;
      }

      id = R.id.fab_add_expense;
      FloatingActionButton fabAddExpense = ViewBindings.findChildViewById(rootView, id);
      if (fabAddExpense == null) {
        break missingId;
      }

      id = R.id.fab_add_transaction;
      FloatingActionButton fabAddTransaction = ViewBindings.findChildViewById(rootView, id);
      if (fabAddTransaction == null) {
        break missingId;
      }

      id = R.id.fab_label_budget;
      TextView fabLabelBudget = ViewBindings.findChildViewById(rootView, id);
      if (fabLabelBudget == null) {
        break missingId;
      }

      id = R.id.fab_label_expense;
      TextView fabLabelExpense = ViewBindings.findChildViewById(rootView, id);
      if (fabLabelExpense == null) {
        break missingId;
      }

      id = R.id.firstTextView;
      TextView firstTextView = ViewBindings.findChildViewById(rootView, id);
      if (firstTextView == null) {
        break missingId;
      }

      id = R.id.footer;
      View footer = ViewBindings.findChildViewById(rootView, id);
      if (footer == null) {
        break missingId;
      }
      BaseBinding binding_footer = BaseBinding.bind(footer);

      id = R.id.graph;
      GraphView graph = ViewBindings.findChildViewById(rootView, id);
      if (graph == null) {
        break missingId;
      }

      id = R.id.header_layout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.itemName;
      TextView itemName = ViewBindings.findChildViewById(rootView, id);
      if (itemName == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.root_layout;
      RelativeLayout rootLayout = ViewBindings.findChildViewById(rootView, id);
      if (rootLayout == null) {
        break missingId;
      }

      id = R.id.secondTextView;
      TextView secondTextView = ViewBindings.findChildViewById(rootView, id);
      if (secondTextView == null) {
        break missingId;
      }

      id = R.id.spending_vs_target;
      TextView spendingVsTarget = ViewBindings.findChildViewById(rootView, id);
      if (spendingVsTarget == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      id = R.id.total_expenditure;
      TextView totalExpenditure = ViewBindings.findChildViewById(rootView, id);
      if (totalExpenditure == null) {
        break missingId;
      }

      return new DashboardBinding((RelativeLayout) rootView, appLogo, balanceVsBudget, btn1d, btn1m,
          btn1w, btn1y, btn3m, budgetId, buttonLogout, buttonNotification, fabAddBudget,
          fabAddExpense, fabAddTransaction, fabLabelBudget, fabLabelExpense, firstTextView,
          binding_footer, graph, headerLayout, itemName, recyclerView, rootLayout, secondTextView,
          spendingVsTarget, spinnerContainer, totalExpenditure);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
