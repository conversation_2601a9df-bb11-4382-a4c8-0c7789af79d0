package com.example.budgettracker

import android.annotation.SuppressLint
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.util.Log
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.google.gson.Gson
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class SyncWorker(context: Context, params: WorkerParameters) : Worker(context, params) {

    private val apiService: ApiService = RetrofitClient.getClient().create(ApiService::class.java)
    private val dbHelper: DatabaseHelper = DatabaseHelper(context)

    override fun doWork(): Result {
        Log.d("SyncWorker", "SyncWorker is running")
//        return try {
//            syncData()
//            Result.success()
//        } catch (e: Exception) {
//            Log.e("SyncWorker", "Sync failed: $e")
//            Result.failure()
//        }
        return Result.success()
    }

    private fun transformTransactions(transactions: List<Transaction>): List<Map<String, Any>> {
        val transformedList = mutableListOf<Map<String, Any>>()
        for (trans in transactions) {
            val transMap = HashMap<String, Any>()
            transMap["trans_balance"] = trans.getBalance()
            transMap["trans_budget"] = trans.getTotal()
            transMap["trans_target"] = trans.getTransTarget()
            transMap["trans_month"] = trans.getDate()
            transMap["user_id"] = trans.getuser_id()
            transformedList.add(transMap)
        }
        return transformedList
    }

    private fun transformUsers(users: List<User>): List<Map<String, Any>> {
        val transformedList = mutableListOf<Map<String, Any>>()
        for (user in users) {
            val userMap = HashMap<String, Any>()
            userMap["user_id"] = user.getuser_id()
            userMap["created_date"] = user.getCreatedDate()
            userMap["user_email"] = user.getUserEmail()
            userMap["user_status"] = user.getUserStatus()
            userMap["user_name"] = user.getUserName()
            transformedList.add(userMap)
        }
        return transformedList
    }

    private fun transformBudgets(budgets: List<Budgets>): List<Map<String, Any>> {
        val transformedList = mutableListOf<Map<String, Any>>()
        for (budget in budgets) {
            val budgetMap = HashMap<String, Any>()
            budgetMap["user_id"] = budget.getuser_id()
            budgetMap["bud_date"] = budget.getDate()
            budgetMap["bud_amount"] = budget.getAmount()
            budgetMap["bud_name"] = budget.getName()
            transformedList.add(budgetMap)
        }
        return transformedList
    }

    private fun syncData() {
        val db = dbHelper.readableDatabase
        val expenses = fetchExpenses(db)
        val users = fetchUsers(db)
        val budgets = fetchBudgets(db)
        val transactions = fetchTransactions(db)
//        val transformedTransactions = transformTransactions(transactions)
//        val transformedUsers = transformUsers(users)
//        val transformedBudgets = transformBudgets(budgets)

        val data = HashMap<String, Any>()
        data["expenses"] = expenses
        data["user"] = users
        data["budget"] = budgets
        data["transactions"] = transactions

        Log.d("SyncWorker", "Sync data: ${Gson().toJson(data)}")

        val call = apiService.syncAllData(data)
        call.enqueue(object : Callback<Void> {
            override fun onResponse(call: Call<Void>, response: Response<Void>) {
                if (response.isSuccessful) {
                    deleteAllData(db)
                } else {
                    Log.e("SyncWorker", "Sync failed: $response")
                }
            }

            override fun onFailure(call: Call<Void>, t: Throwable) {
                Log.e("SyncWorker", "Sync error: $t")
            }
        })

        db.close()
    }

    @SuppressLint("Range")
    private fun fetchExpenses(db: SQLiteDatabase): List<Expenses> {
        val expensesList = mutableListOf<Expenses>()
        val cursor = db.rawQuery("SELECT * FROM expenses", null)
        while (cursor.moveToNext()) {
            val expense = Expenses(
                cursor.getString(cursor.getColumnIndex("name")),
                cursor.getString(cursor.getColumnIndex("date")),
                cursor.getString(cursor.getColumnIndex("note")),
                cursor.getInt(cursor.getColumnIndex("amount")),
                cursor.getString(cursor.getColumnIndex("user_id"))
            )
            expensesList.add(expense)
        }
        cursor.close()
        return expensesList
    }

    @SuppressLint("Range")
    private fun fetchUsers(db: SQLiteDatabase): List<User> {
        val usersList = mutableListOf<User>()
        val cursor = db.rawQuery("SELECT * FROM user", null)
        while (cursor.moveToNext()) {
            val user = User(
                cursor.getString(cursor.getColumnIndex("user_id")),
                cursor.getString(cursor.getColumnIndex("created_date")),
                cursor.getString(cursor.getColumnIndex("user_name")),
                cursor.getInt(cursor.getColumnIndex("user_status")),
                cursor.getString(cursor.getColumnIndex("user_email"))
            )
            usersList.add(user)
        }
        cursor.close()
        Log.w("Trial fetchUsers", usersList.toString())
        return usersList
    }

    @SuppressLint("Range")
    private fun fetchBudgets(db: SQLiteDatabase): List<Budgets> {
        val budgetsList = mutableListOf<Budgets>()
        val cursor = db.rawQuery("SELECT * FROM budget", null)
        while (cursor.moveToNext()) {
            val budget = Budgets(
                cursor.getString(cursor.getColumnIndex("bud_name")),
                cursor.getString(cursor.getColumnIndex("bud_date")),
                cursor.getInt(cursor.getColumnIndex("bud_amount")),
                cursor.getString(cursor.getColumnIndex("user_id"))
            )
            budgetsList.add(budget)
        }
        cursor.close()
        Log.w("Trial fetchBudgets", budgetsList.toString())
        return budgetsList
    }

    @SuppressLint("Range")
    private fun fetchTransactions(db: SQLiteDatabase): List<Transaction> {
        val transactionsList = mutableListOf<Transaction>()
        val cursor = db.rawQuery("SELECT * FROM transactions", null)
        while (cursor.moveToNext()) {
            val trans = Transaction(
                cursor.getFloat(cursor.getColumnIndex("trans_budget")).toDouble(),
                cursor.getFloat(cursor.getColumnIndex("trans_balance")).toDouble(),
                cursor.getFloat(cursor.getColumnIndex("trans_target")).toDouble(),
                cursor.getString(cursor.getColumnIndex("user_id")),
                cursor.getString(cursor.getColumnIndex("trans_month"))
            )
            transactionsList.add(trans)
        }
        cursor.close()
        Log.w("Trial fetchTransactions", transactionsList.toString())
        return transactionsList
    }

    private fun deleteAllData(db: SQLiteDatabase) {
//        db.execSQL("DELETE FROM expenses")
//        db.execSQL("DELETE FROM user")
//        db.execSQL("DELETE FROM budget")
//        db.execSQL("DELETE FROM transactions")
    }
}
