// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BaseBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton buttonExpenses;

  @NonNull
  public final ImageButton buttonHome;

  @NonNull
  public final ImageButton buttonNotification;

  @NonNull
  public final ImageButton buttonProfile;

  @NonNull
  public final ImageButton buttonTransactions;

  private BaseBinding(@NonNull LinearLayout rootView, @NonNull ImageButton buttonExpenses,
      @NonNull ImageButton buttonHome, @NonNull ImageButton buttonNotification,
      @NonNull ImageButton buttonProfile, @NonNull ImageButton buttonTransactions) {
    this.rootView = rootView;
    this.buttonExpenses = buttonExpenses;
    this.buttonHome = buttonHome;
    this.buttonNotification = buttonNotification;
    this.buttonProfile = buttonProfile;
    this.buttonTransactions = buttonTransactions;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BaseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BaseBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.base, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BaseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_expenses;
      ImageButton buttonExpenses = ViewBindings.findChildViewById(rootView, id);
      if (buttonExpenses == null) {
        break missingId;
      }

      id = R.id.button_home;
      ImageButton buttonHome = ViewBindings.findChildViewById(rootView, id);
      if (buttonHome == null) {
        break missingId;
      }

      id = R.id.button_notification;
      ImageButton buttonNotification = ViewBindings.findChildViewById(rootView, id);
      if (buttonNotification == null) {
        break missingId;
      }

      id = R.id.button_profile;
      ImageButton buttonProfile = ViewBindings.findChildViewById(rootView, id);
      if (buttonProfile == null) {
        break missingId;
      }

      id = R.id.button_transactions;
      ImageButton buttonTransactions = ViewBindings.findChildViewById(rootView, id);
      if (buttonTransactions == null) {
        break missingId;
      }

      return new BaseBinding((LinearLayout) rootView, buttonExpenses, buttonHome,
          buttonNotification, buttonProfile, buttonTransactions);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
