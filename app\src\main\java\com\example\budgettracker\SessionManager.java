package com.example.budgettracker;
import java.util.HashMap;
import static androidx.constraintlayout.helper.widget.MotionEffect.TAG;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.Log;

import java.util.Map;

public class SessionManager {
    private static final String KEY_IS_LOGGED_IN = "is_logged_in";
    private static final String KEY_USER_EMAIL = "user_email";
    private static final String KEY_USER_NAME = "user_name";
    private static final String KEY_USER_UID = "user_uid"; // Add UID key
    private SharedPreferences sharedPreferences;
    private SharedPreferences.Editor editor;

    public SessionManager(Context context) {
        sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
        editor = sharedPreferences.edit();
    }

    public void createLoginSession(String email, String userName, String uid) {
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.putString(KEY_USER_EMAIL, email);
        editor.putString(KEY_USER_NAME, userName);
        editor.putString(KEY_USER_UID, uid);
        editor.apply();
    }
    public boolean isLoggedIn() {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false);
    }
    public void logoutUser() {
        editor.clear();
        editor.apply();
    }
    public String getUserEmail() {
        return sharedPreferences.getString(KEY_USER_EMAIL, null);
    }
    public String getUserName() {return sharedPreferences.getString(KEY_USER_NAME, null);}
    public String getUserUid() {return sharedPreferences.getString(KEY_USER_UID, null);} // Retrieve UID from session}

    public Map<String, String> getAllSessionDetails() {
        Map<String, String> sessionDetails = new HashMap<>();
        sessionDetails.put("isLoggedIn", String.valueOf(isLoggedIn()));
        sessionDetails.put("email", getUserEmail());
        sessionDetails.put("userName", getUserName());
        sessionDetails.put("uid", getUserUid());
        return sessionDetails;
    }
}
