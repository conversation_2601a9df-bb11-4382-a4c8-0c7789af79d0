<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Legacy colors for backward compatibility -->
    <color name="black">#FF000000</color>
    <color name="white">#ffffff</color>
    <color name="primary">#0061A4</color>
    <color name="steelblue">#4682B4</color>
    <color name="onPrimary">#ffffff</color>
    <color name="primary_blue">#0061A4</color>
    <color name="text_color">#1A1C1E</color>
    <color name="border_color">#C3C7CF</color>
    <color name="radio_dot_color">#1A1C1E</color>
    <color name="back_ground">#b0463a</color>

    <!-- Material 3 Light Theme Colors -->
    <color name="md_theme_light_primary">#0061A4</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_primaryContainer">#D1E4FF</color>
    <color name="md_theme_light_onPrimaryContainer">#001D36</color>
    <color name="md_theme_light_secondary">#535F70</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_secondaryContainer">#D7E3F7</color>
    <color name="md_theme_light_onSecondaryContainer">#101C2B</color>
    <color name="md_theme_light_tertiary">#6B5778</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_tertiaryContainer">#F2DAFF</color>
    <color name="md_theme_light_onTertiaryContainer">#251431</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_background">#FCFCFF</color>
    <color name="md_theme_light_onBackground">#1A1C1E</color>
    <color name="md_theme_light_surface">#FCFCFF</color>
    <color name="md_theme_light_onSurface">#1A1C1E</color>
    <color name="md_theme_light_surfaceVariant">#DFE2EB</color>
    <color name="md_theme_light_onSurfaceVariant">#43474E</color>
    <color name="md_theme_light_outline">#73777F</color>
    <color name="md_theme_light_outlineVariant">#C3C7CF</color>
    <color name="md_theme_light_scrim">#000000</color>
    <color name="md_theme_light_inverseSurface">#2F3033</color>
    <color name="md_theme_light_inverseOnSurface">#F1F0F4</color>
    <color name="md_theme_light_inversePrimary">#9ECAFF</color>

    <!-- Additional colors for modern UI -->
    <color name="scrim_overlay">#80000000</color>

    <!-- Colors for item adapter -->
    <color name="primary_light">#E3F2FD</color>
    <color name="primary_variant">#1976D2</color>
</resources>