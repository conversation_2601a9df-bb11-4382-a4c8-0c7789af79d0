<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <LinearLayout
            android:id="@+id/notification_icon_background"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/notification_info_background"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/notification_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="Notification icon"
                android:src="@drawable/notifications"
                android:tint="@color/white" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/notification_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Notification Title"
                android:textColor="?attr/colorOnSurface"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/notification_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Notification message content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp"
                android:alpha="0.7"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/notification_timestamp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Just now"
                android:textColor="?attr/colorPrimary"
                android:textSize="12sp"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
