// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ListBugBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView itemAmount;

  @NonNull
  public final TextView itemDate;

  @NonNull
  public final TextView itemName;

  @NonNull
  public final ImageView listIcon;

  private ListBugBinding(@NonNull MaterialCardView rootView, @NonNull TextView itemAmount,
      @NonNull TextView itemDate, @NonNull TextView itemName, @NonNull ImageView listIcon) {
    this.rootView = rootView;
    this.itemAmount = itemAmount;
    this.itemDate = itemDate;
    this.itemName = itemName;
    this.listIcon = listIcon;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ListBugBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ListBugBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.list_bug, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ListBugBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.itemAmount;
      TextView itemAmount = ViewBindings.findChildViewById(rootView, id);
      if (itemAmount == null) {
        break missingId;
      }

      id = R.id.itemDate;
      TextView itemDate = ViewBindings.findChildViewById(rootView, id);
      if (itemDate == null) {
        break missingId;
      }

      id = R.id.itemName;
      TextView itemName = ViewBindings.findChildViewById(rootView, id);
      if (itemName == null) {
        break missingId;
      }

      id = R.id.list_icon;
      ImageView listIcon = ViewBindings.findChildViewById(rootView, id);
      if (listIcon == null) {
        break missingId;
      }

      return new ListBugBinding((MaterialCardView) rootView, itemAmount, itemDate, itemName,
          listIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
