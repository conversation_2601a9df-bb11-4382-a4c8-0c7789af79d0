package com.example.budgettracker

class ExpenseRes {
    private var success: Boolean = false
    private var message: String? = null

    // Get<PERSON> and Setter for success
    fun isSuccess(): Boolean {
        return success
    }

    fun setSuccess(success: Boolean) {
        this.success = success
    }

    // Getter and Setter for message
    fun getMessage(): String? {
        return message
    }

    fun setMessage(message: String?) {
        this.message = message
    }
}
