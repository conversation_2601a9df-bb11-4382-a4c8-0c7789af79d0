<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="login" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/login_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="175" endOffset="16"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="163" startOffset="4" endLine="173" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="171" startOffset="8" endLine="171" endOffset="43"/></Target><Target id="@+id/welcome_icon" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="19" endOffset="39"/></Target><Target id="@+id/description" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="33" endOffset="41"/></Target><Target id="@+id/email_container" view="LinearLayout"><Expressions/><location startLine="36" startOffset="4" endLine="57" endOffset="18"/></Target><Target id="@+id/email_input" view="EditText"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="37"/></Target><Target id="@+id/password_container" view="LinearLayout"><Expressions/><location startLine="60" startOffset="4" endLine="91" endOffset="18"/></Target><Target id="@+id/password_input" view="EditText"><Expressions/><location startLine="70" startOffset="8" endLine="80" endOffset="37"/></Target><Target id="@+id/password_eye" view="ImageView"><Expressions/><location startLine="82" startOffset="8" endLine="89" endOffset="49"/></Target><Target id="@+id/login_button" view="Button"><Expressions/><location startLine="94" startOffset="4" endLine="99" endOffset="39"/></Target><Target id="@+id/or_login_with" view="TextView"><Expressions/><location startLine="102" startOffset="4" endLine="111" endOffset="41"/></Target><Target id="@+id/biometric_card" view="LinearLayout"><Expressions/><location startLine="114" startOffset="4" endLine="146" endOffset="18"/></Target><Target id="@+id/fingerprint_icon" view="ImageView"><Expressions/><location startLine="126" startOffset="8" endLine="134" endOffset="47"/></Target><Target id="@+id/fingerprint_instead" view="TextView"><Expressions/><location startLine="136" startOffset="8" endLine="144" endOffset="52"/></Target><Target id="@+id/signup_instead" view="Button"><Expressions/><location startLine="149" startOffset="4" endLine="160" endOffset="48"/></Target></Targets></Layout>