
package com.example.budgettracker;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class Item2Adapter extends RecyclerView.Adapter<Item2Adapter.ViewHolder> {

    private List<Budgets> BudgetsList;
    private int lastPosition = -1;

    public Item2Adapter(List<Budgets> BudgetsList) {
        this.BudgetsList = BudgetsList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_bug, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Budgets budget = BudgetsList.get(position);

        // Set transaction name
        String name = budget.getName();
        holder.nameTextView.setText(name);

        // Format and set date
        String formattedDate = formatDate(budget.getDate());
        holder.dateTextView.setText(formattedDate);

        // Format and set amount with currency symbol
        holder.amountTextView.setText(String.format("+$%d", budget.getAmount()));

        // Add click listener for item interaction
        holder.itemView.setOnClickListener(v -> {
            String details = String.format("Transaction: %s\nAmount: $%d\nDate: %s",
                name, budget.getAmount(), formattedDate);
            Toast.makeText(v.getContext(), details, Toast.LENGTH_SHORT).show();
        });

        // Add long click for additional details
        holder.itemView.setOnLongClickListener(v -> {
            String detailedInfo = String.format("Transaction Details:\n• Name: %s\n• Amount: $%d\n• Date: %s\n• Type: Budget Addition",
                name, budget.getAmount(), formattedDate);
            Toast.makeText(v.getContext(), detailedInfo, Toast.LENGTH_LONG).show();
            return true;
        });

        // Add animation
        setAnimation(holder.itemView, position);
    }

    private void setAnimation(View viewToAnimate, int position) {
        // If the bound view wasn't previously displayed on screen, it's animated
        if (position > lastPosition) {
            Animation animation = AnimationUtils.loadAnimation(viewToAnimate.getContext(), R.anim.item_animation_fall_down);
            viewToAnimate.startAnimation(animation);
            lastPosition = position;
        }
    }

    private String formatDate(String dateString) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            SimpleDateFormat outputFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
            Date date = inputFormat.parse(dateString);
            return outputFormat.format(date);
        } catch (Exception e) {
            // If parsing fails, return original string
            return dateString;
        }
    }

    private String getRelativeTime(String dateString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            Date date = sdf.parse(dateString);
            Date now = new Date();

            long diffInMillis = now.getTime() - date.getTime();
            long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);

            if (diffInDays == 0) {
                return "Today";
            } else if (diffInDays == 1) {
                return "Yesterday";
            } else if (diffInDays < 7) {
                return diffInDays + " days ago";
            } else {
                return formatDate(dateString);
            }
        } catch (Exception e) {
            return formatDate(dateString);
        }
    }

    @Override
    public int getItemCount() {
        return BudgetsList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView nameTextView;
        public TextView dateTextView;
        public TextView amountTextView;

        public ViewHolder(View itemView) {
            super(itemView);
            nameTextView = itemView.findViewById(R.id.itemName);
            dateTextView = itemView.findViewById(R.id.itemDate);
            amountTextView = itemView.findViewById(R.id.itemAmount);
        }
    }
}

