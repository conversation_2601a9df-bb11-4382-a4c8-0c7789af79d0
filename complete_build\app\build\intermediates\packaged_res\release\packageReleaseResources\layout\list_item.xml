<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Main content row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Icon -->
            <ImageView
                android:id="@+id/list_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/shopping"
                android:background="@drawable/circle_background"
                android:padding="8dp"
                android:layout_marginEnd="12dp"
                android:tint="?attr/colorPrimary" />

            <!-- Content column -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- Name with ellipsize -->
                <TextView
                    android:id="@+id/itemName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/ext_name"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium" />

                <!-- Date -->
                <TextView
                    android:id="@+id/itemDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/exp_date"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <!-- Amount -->
            <TextView
                android:id="@+id/itemAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/exp_amount"
                android:textSize="16sp"
                android:textColor="?attr/colorPrimary"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                android:layout_marginStart="8dp" />

            <!-- Expand/Collapse button -->
            <ImageButton
                android:id="@+id/expand_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/expand_more"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginStart="8dp"
                android:contentDescription="View more details"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Expandable details section -->
        <LinearLayout
            android:id="@+id/expandable_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="8dp"
            android:visibility="gone">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="?attr/colorOutlineVariant"
                android:layout_marginVertical="8dp" />

            <!-- Full description -->
            <TextView
                android:id="@+id/itemFullDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Full description will appear here"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:lineSpacingExtra="2dp" />

            <!-- Additional details if needed -->
            <TextView
                android:id="@+id/itemCategory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Category"
                android:textSize="12sp"
                android:textColor="?attr/colorPrimary"
                android:background="@drawable/chip_background"
                android:padding="4dp"
                android:layout_marginTop="8dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>