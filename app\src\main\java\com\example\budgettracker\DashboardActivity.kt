package com.example.budgettracker

import android.annotation.SuppressLint
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jjoe64.graphview.DefaultLabelFormatter
import com.jjoe64.graphview.GraphView
import com.jjoe64.graphview.GridLabelRenderer
import com.jjoe64.graphview.series.DataPoint
import com.jjoe64.graphview.series.LineGraphSeries
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*

class DashboardActivity : AppCompatActivity() {
    
    private lateinit var apiService: ApiService
    private lateinit var listContainer: LinearLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var itemAdapter: ItemAdapter
    private lateinit var dbHelper: DatabaseHelper
    private var user_id: String = ""
    private var totalBudget = 0f
    private var balance = 0f
    private var trans_target = 0f
    private lateinit var loadingSpinner: LoadingSpinner

    // FAB variables
    private var fabMain: FloatingActionButton? = null
    private var fabAddExpense: FloatingActionButton? = null
    private var fabAddBudget: FloatingActionButton? = null
    private var fabLabelExpense: TextView? = null
    private var fabLabelBudget: TextView? = null
    private var isFabMenuOpen = false

    // Time period filter variables
    private lateinit var btn1d: Button
    private lateinit var btn1w: Button
    private lateinit var btn1m: Button
    private lateinit var btn3m: Button
    private lateinit var btn1y: Button
    private lateinit var currentSelectedButton: Button
    private var currentTimePeriod = "1M" // Default to 1 month

    // Session and UI variables
    private lateinit var sessionManager: SessionManager
    private lateinit var secondTextView: TextView

    @SuppressLint("WrongViewCast")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dashboard)
        dbHelper = DatabaseHelper(this)
        recyclerView = findViewById(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(this)
        sessionManager = SessionManager(this)
        secondTextView = findViewById(R.id.secondTextView)
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        val sessionDetails = sessionManager.getAllSessionDetails()
        Log.w("MainActivity", "Session Details: $sessionDetails")
        
        if (sessionManager.isLoggedIn()) {
            val displayName = sessionManager.getUserName()
            Log.w("DashboardActivity", "Retrieved username from session: $displayName")
            if (!displayName.isNullOrBlank()) {
                secondTextView.text = displayName
                Log.w("DashboardActivity", "Username set to TextView: $displayName")
            } else {
                Log.w("DashboardActivity", "Username is null or empty, trying to fetch from database")
                // Try to get username from local database or Firebase
                fetchAndSetUsername()
            }
            user_id = sessionManager.getUserUid() ?: ""
        } else {
            val intent = Intent(this@DashboardActivity, MainActivity::class.java)
            startActivity(intent)
            finish()
        }

        setupNavigationButtons()

        // Initialize with 1M (current month) data by default
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH) + 1
        val monthYear = String.format(Locale.getDefault(), "%d-%02d", year, month)

        // Load default 1M data
        refreshDataForPeriod("1M")

        val logoutButton = findViewById<ImageButton>(R.id.button_logout)
        logoutButton.setOnClickListener {
            sessionManager.logoutUser()
            val intent = Intent(this@DashboardActivity, MainActivity::class.java)
            startActivity(intent)
            finish()
        }

        // Setup FloatingActionButton menu (disabled for now)
        // setupFabMenu()

        // Setup time period filter buttons
        setupTimePeriodButtons()

        // Check for sync when app starts and user is online
        checkAndSyncData()
    }

    private fun setupTimePeriodButtons() {
        // Initialize button references
        btn1d = findViewById(R.id.btn_1d)
        btn1w = findViewById(R.id.btn_1w)
        btn1m = findViewById(R.id.btn_1m)
        btn3m = findViewById(R.id.btn_3m)
        btn1y = findViewById(R.id.btn_1y)

        // Set default selected button (1M)
        currentSelectedButton = btn1m

        // Set click listeners
        btn1d.setOnClickListener { onTimePeriodSelected("1D", btn1d) }
        btn1w.setOnClickListener { onTimePeriodSelected("1W", btn1w) }
        btn1m.setOnClickListener { onTimePeriodSelected("1M", btn1m) }
        btn3m.setOnClickListener { onTimePeriodSelected("3M", btn3m) }
        btn1y.setOnClickListener { onTimePeriodSelected("1Y", btn1y) }
    }

    private fun onTimePeriodSelected(period: String, selectedButton: Button) {
        // Update visual state
        updateButtonSelection(selectedButton)

        // Update current period
        currentTimePeriod = period

        // Refresh data based on selected period
        refreshDataForPeriod(period)
    }

    private fun updateButtonSelection(selectedButton: Button) {
        // Reset all buttons to unselected state
        resetButtonState(btn1d)
        resetButtonState(btn1w)
        resetButtonState(btn1m)
        resetButtonState(btn3m)
        resetButtonState(btn1y)

        // Set selected button state
        selectedButton.setBackgroundResource(R.drawable.small_chip_selected_background)
        selectedButton.setTextColor(getColor(R.color.md_theme_light_onPrimary))

        currentSelectedButton = selectedButton
    }

    private fun resetButtonState(button: Button) {
        button.setBackgroundResource(R.drawable.small_chip_background)
        button.setTextColor(getColor(R.color.md_theme_light_onSurface))
    }

    private fun refreshDataForPeriod(period: String) {
        val dateFilter = calculateDateFilter(period)

        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            fetchAndSetOnlineForPeriod(dateFilter, period)
        } else {
            fetchAndSetDataForPeriod(dateFilter, period)
        }

        calculateBalanceForPeriod(dateFilter, period)
    }

    private fun calculateDateFilter(period: String): String {
        val calendar = Calendar.getInstance()
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

        return when (period) {
            "1D" -> {
                // Today only
                sdf.format(calendar.time)
            }
            "1W" -> {
                // Last 7 days
                calendar.add(Calendar.DAY_OF_MONTH, -7)
                sdf.format(calendar.time)
            }
            "1M" -> {
                // Current month
                val year = calendar.get(Calendar.YEAR)
                val month = calendar.get(Calendar.MONTH) + 1
                String.format(Locale.getDefault(), "%d-%02d", year, month)
            }
            "3M" -> {
                // Last 3 months
                calendar.add(Calendar.MONTH, -3)
                sdf.format(calendar.time)
            }
            "1Y" -> {
                // Current year
                calendar.get(Calendar.YEAR).toString()
            }
            else -> {
                // Default to current month
                val defaultYear = calendar.get(Calendar.YEAR)
                val defaultMonth = calendar.get(Calendar.MONTH) + 1
                String.format(Locale.getDefault(), "%d-%02d", defaultYear, defaultMonth)
            }
        }
    }

    fun setupNavigationButtons() {
        val homeButton = findViewById<ImageButton>(R.id.button_home)
        val expensesButton = findViewById<ImageButton>(R.id.button_expenses)
        val transactionsButton = findViewById<ImageButton>(R.id.button_transactions)
        val profileButton = findViewById<ImageButton>(R.id.button_profile)
        val notificationButton = findViewById<ImageButton>(R.id.button_notification)

        homeButton.setOnClickListener { navigateTo(DashboardActivity::class.java) }
        expensesButton.setOnClickListener { navigateTo(ExpensesActivity::class.java) }
        transactionsButton.setOnClickListener { navigateTo(TransactionsActivity::class.java) }
        profileButton.setOnClickListener { navigateTo(SettingsActivity::class.java) }
        notificationButton.setOnClickListener { navigateTo(NotificationActivity::class.java) }
    }

    private fun calculateBalanceForMonth(monthYear: String) {
        if (NetworkUtils.isInternetAvailable(this)) {
            val apiService = RetrofitClient.getClient().create(ApiService::class.java)
            val call = apiService.getRecords("transactions", monthYear, user_id)

            call.enqueue(object : Callback<List<Record>> {
                override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                    if (response.isSuccessful && response.body() != null && response.body()!!.isNotEmpty()) {
                        for (record in response.body()!!) {
                            if (record is Transaction) {
                                totalBudget = record.getTotal().toFloat()
                                balance = record.getBalance().toFloat()
                                trans_target = record.getTransTarget().toFloat()
                                break
                            }
                        }
                        updateUI()
                    } else {
                        Log.e("TransactionsActivity", "Response not successful or empty.")
                    }
                }

                override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                    Toast.makeText(this@DashboardActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
        } else {
            val result = fetchDataFromLocalDatabase(monthYear)
            balance = result.getBalance()
            totalBudget = result.getTotalBudget()
            trans_target = result.getTransTarget()
            updateUI()
        }
    }

    private fun updateUI() {
        val budgetTextView = findViewById<TextView>(R.id.budget_id)
        @SuppressLint("DefaultLocale")
        val budgetInfo = String.format("$%.2f", balance)
        budgetTextView.text = budgetInfo

        // Calculate spending (expenses only, not including transactions)
        // Since transactions only add to balance, not budget, we need to calculate expenses differently
        val totalSpent = maxOf(0f, totalBudget - balance) // Ensure non-negative

        // For a more accurate calculation, let's get the actual expenses from the database
        calculateActualExpensesAndUpdateUI()
    }

    private fun calculateActualExpensesAndUpdateUI() {
        // Get current month for expense calculation
        val cal = Calendar.getInstance()
        val currentMonth = String.format(Locale.getDefault(), "%d-%02d", cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1)

        val dbHelper = DatabaseHelper(this)
        val db = dbHelper.readableDatabase

        // Get actual expenses for current month
        val expensesQuery = "SELECT SUM(amount) FROM expenses WHERE strftime('%Y-%m', date) = ? AND user_id = ?"
        val expensesCursor = db.rawQuery(expensesQuery, arrayOf(currentMonth, user_id))

        var actualExpenses = 0f
        if (expensesCursor.moveToFirst()) {
            actualExpenses = expensesCursor.getFloat(0)
        }
        expensesCursor.close()
        db.close()

        // Update all the new UI elements with actual expenses
        updateSpendingVsTarget(actualExpenses, trans_target)
        updateBalanceVsBudget(balance, totalBudget)
        updateTotalExpenditure(actualExpenses)
    }

    private fun fetchDataFromLocalDatabase(monthYear: String): BalanceResult {
        val dbHelper = DatabaseHelper(this)
        val db = dbHelper.readableDatabase

        // Get transaction summary (initial budget and target)
        val transactionsQuery = "SELECT trans_budget, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?"
        val transactionsCursor = db.rawQuery(transactionsQuery, arrayOf(monthYear, user_id))

        var initialBudget = 0f
        if (transactionsCursor.moveToFirst()) {
            initialBudget = transactionsCursor.getFloat(0)  // This is the starting budget
            trans_target = transactionsCursor.getFloat(1)
        }
        transactionsCursor.close()

        // Get current balance directly from transactions table (it already includes all transactions)
        val balanceQuery = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?"
        val balanceCursor = db.rawQuery(balanceQuery, arrayOf(monthYear, user_id))

        var currentBalance = initialBudget // Default to initial budget if no transactions record
        if (balanceCursor.moveToFirst()) {
            currentBalance = balanceCursor.getFloat(0)
        }
        balanceCursor.close()

        // Get all expenses for the month
        val expensesQuery = "SELECT SUM(amount) FROM expenses WHERE strftime('%Y-%m', date) = ? AND user_id = ?"
        val expensesCursor = db.rawQuery(expensesQuery, arrayOf(monthYear, user_id))

        var totalExpenses = 0f
        if (expensesCursor.moveToFirst()) {
            totalExpenses = expensesCursor.getFloat(0)
        }
        expensesCursor.close()

        // Calculate final balance: current balance (includes transactions) - total expenses
        balance = currentBalance - totalExpenses
        totalBudget = initialBudget  // Budget stays the same, only balance changes

        Log.d("DashboardActivity", "Balance calculation: Initial Budget=$initialBudget, Current Balance (with transactions)=$currentBalance, Expenses=$totalExpenses, Final Balance=$balance")

        db.close()
        return BalanceResult(totalBudget, balance, trans_target)
    }

    @SuppressLint("SetTextI18n")
    private fun updateSpendingVsTarget(spentAmount: Float, targetAmount: Float) {
        val spendingVsTargetTextView = findViewById<TextView>(R.id.spending_vs_target)

        if (targetAmount != 0f) {
            val spendingPercentage = (spentAmount / targetAmount) * 100
            var result = String.format(Locale.getDefault(), "%.1f%% of target", spendingPercentage)

            // Color coding based on spending percentage
            when {
                spendingPercentage >= 100 -> {
                    spendingVsTargetTextView.setTextColor(Color.RED)  // Over budget - red
                    result = String.format(Locale.getDefault(), "%.1f%% - OVER!", spendingPercentage)
                }
                spendingPercentage >= 80 -> {
                    spendingVsTargetTextView.setTextColor(Color.parseColor("#FF8C00"))  // Warning - orange
                }
                spendingPercentage >= 50 -> {
                    spendingVsTargetTextView.setTextColor(Color.parseColor("#FFD700"))  // Caution - yellow/gold
                }
                else -> {
                    spendingVsTargetTextView.setTextColor(Color.GREEN)  // Good - green
                }
            }
            spendingVsTargetTextView.text = result
        } else {
            spendingVsTargetTextView.text = "No target set"
            spendingVsTargetTextView.setTextColor(Color.GRAY)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateBalanceVsBudget(currentBalance: Float, totalBudget: Float) {
        val balanceVsBudgetTextView = findViewById<TextView>(R.id.balance_vs_budget)

        if (totalBudget != 0f) {
            val balancePercentage = (currentBalance / totalBudget) * 100
            val result = String.format(Locale.getDefault(), "%.1f%% remaining", balancePercentage)

            // Color coding based on remaining balance percentage
            when {
                balancePercentage <= 10 -> {
                    balanceVsBudgetTextView.setTextColor(Color.RED)  // Very low - red
                }
                balancePercentage <= 25 -> {
                    balanceVsBudgetTextView.setTextColor(Color.parseColor("#FF8C00"))  // Low - orange
                }
                balancePercentage <= 50 -> {
                    balanceVsBudgetTextView.setTextColor(Color.parseColor("#FFD700"))  // Medium - yellow/gold
                }
                else -> {
                    balanceVsBudgetTextView.setTextColor(Color.GREEN)  // Good - green
                }
            }
            balanceVsBudgetTextView.text = result
        } else {
            balanceVsBudgetTextView.text = "No budget set"
            balanceVsBudgetTextView.setTextColor(Color.GRAY)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateTotalExpenditure(totalSpent: Float) {
        val totalExpenditureTextView = findViewById<TextView>(R.id.total_expenditure)
        val result = String.format(Locale.getDefault(), "$%.2f", totalSpent)
        totalExpenditureTextView.text = result
        totalExpenditureTextView.setTextColor(Color.WHITE)
    }

    private fun navigateTo(destination: Class<*>) {
        val intent = Intent(this@DashboardActivity, destination)
        startActivity(intent)
        finish()
    }

    // Parse items JSON
    private fun parseItems(json: String): List<Expenses> {
        val gson = Gson()
        val expensesListType = object : TypeToken<List<Expenses>>() {}.type
        return gson.fromJson(json, expensesListType)
    }

    // Parse chart data JSON
    private fun parseChartData(json: String): List<ChartData> {
        val gson = Gson()
        val chartDataListType = object : TypeToken<List<ChartData>>() {}.type
        return gson.fromJson(json, chartDataListType)
    }

    private fun setupChart(amounts: List<Int>) {
        val graph = findViewById<GraphView>(R.id.graph)

        // Clear any existing series
        graph.removeAllSeries()

        if (amounts.isEmpty()) {
            return
        }

        // Create a meaningful chart with daily spending pattern
        val dailySpendingSeries = LineGraphSeries<DataPoint>()
        val cumulativeSpendingSeries = LineGraphSeries<DataPoint>()

        // Calculate both daily and cumulative spending
        var cumulativeSpending = 0.0
        for (i in amounts.indices) {
            // Daily spending (individual expenses)
            dailySpendingSeries.appendData(DataPoint((i + 1).toDouble(), amounts[i].toDouble()), true, amounts.size)

            // Cumulative spending
            cumulativeSpending += amounts[i]
            cumulativeSpendingSeries.appendData(DataPoint((i + 1).toDouble(), cumulativeSpending), true, amounts.size)
        }

        // Style the daily spending series with fallback colors
        try {
            dailySpendingSeries.color = getColor(R.color.md_theme_light_secondary)
        } catch (e: Exception) {
            dailySpendingSeries.color = Color.BLUE // Fallback color
        }
        dailySpendingSeries.thickness = 3
        dailySpendingSeries.isDrawDataPoints = true
        dailySpendingSeries.dataPointsRadius = 5f
        dailySpendingSeries.title = "Daily Spending"

        // Style the cumulative spending series with fallback colors
        try {
            cumulativeSpendingSeries.color = getColor(R.color.md_theme_light_primary)
        } catch (e: Exception) {
            cumulativeSpendingSeries.color = Color.RED // Fallback color
        }
        cumulativeSpendingSeries.thickness = 4
        cumulativeSpendingSeries.isDrawDataPoints = true
        cumulativeSpendingSeries.dataPointsRadius = 6f
        cumulativeSpendingSeries.title = "Cumulative Spending"

        // Add both series to the graph
        graph.addSeries(dailySpendingSeries)
        graph.addSeries(cumulativeSpendingSeries)

        // Configure the graph
        graph.title = "Spending Analysis"
        graph.titleTextSize = 40f

        // Configure viewport
        graph.viewport.isYAxisBoundsManual = true
        graph.viewport.setMinY(0.0)
        graph.viewport.setMaxY(cumulativeSpending * 1.1) // Add 10% padding

        graph.viewport.isXAxisBoundsManual = true
        graph.viewport.setMinX(1.0)
        graph.viewport.setMaxX(amounts.size.toDouble())

        // Enable scrolling and scaling
        graph.viewport.isScrollable = true
        graph.viewport.isScalable = true

        // Configure grid and labels
        val gridLabelRenderer = graph.gridLabelRenderer
        gridLabelRenderer.isHorizontalLabelsVisible = true
        gridLabelRenderer.isVerticalLabelsVisible = true
        gridLabelRenderer.gridStyle = GridLabelRenderer.GridStyle.BOTH
        gridLabelRenderer.isHighlightZeroLines = true

        // Format labels
        gridLabelRenderer.labelFormatter = object : DefaultLabelFormatter() {
            override fun formatLabel(value: Double, isValueX: Boolean): String {
                return if (isValueX) {
                    "Day ${value.toInt()}"
                } else {
                    String.format("$%.0f", value)
                }
            }
        }

        // Enable legend
        graph.legendRenderer.isVisible = true
        graph.legendRenderer.align = com.jjoe64.graphview.LegendRenderer.LegendAlign.TOP
    }

    private fun calculateBalanceForPeriod(dateFilter: String, period: String) {
        when (period) {
            "1M" -> calculateBalanceForMonth(dateFilter)
            else -> calculateBalanceForMonth(dateFilter) // For now, use month calculation for all periods
        }
    }

    private fun fetchAndSetDataForPeriod(dateFilter: String, period: String) {
        // For now, use the existing fetchAndSetData method
        // This can be enhanced later to handle different periods
        fetchAndSetData()
    }

    private fun fetchAndSetOnlineForPeriod(dateFilter: String, period: String) {
        // For now, use the existing fetchAndSetOnline method
        // This can be enhanced later to handle different periods
        fetchAndSetOnline()
    }

    private fun fetchAndSetData() {
        val db = dbHelper.readableDatabase
        val projection = arrayOf("name", "date", "note", "amount")
        val selection = "user_id = ?"
        val selectionArgs = arrayOf(user_id)
        val cursor = db.query(
            "expenses",
            projection,
            selection,
            selectionArgs,
            null,
            null,
            "date DESC"
        )
        val expensesList = mutableListOf<Expenses>()
        val amounts = mutableListOf<Int>()
        while (cursor.moveToNext()) {
            val name = cursor.getString(cursor.getColumnIndexOrThrow("name"))
            val date = cursor.getString(cursor.getColumnIndexOrThrow("date"))
            val note = cursor.getString(cursor.getColumnIndexOrThrow("note"))
            val amount = cursor.getInt(cursor.getColumnIndexOrThrow("amount"))
            amounts.add(amount)
            val expense = Expenses(name, date, note, amount, user_id)
            expensesList.add(expense)
        }
        cursor.close()
        db.close()

        // Update RecyclerView
        itemAdapter = ItemAdapter(expensesList)
        recyclerView.adapter = itemAdapter

        // Update chart
        setupChart(amounts)
    }

    private fun fetchAndSetOnline() {
        if (user_id.isEmpty()) {
            Log.e("DashboardActivity", "User ID is empty, cannot fetch online data")
            return
        }

        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("expenses", null, user_id)
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val items = getExpenses(response)
                    items?.let { expensesList ->
                        for (expense in expensesList) {
                            expense.setDate(trimDate(expense.getDate()))
                        }
                        itemAdapter = ItemAdapter(expensesList)
                        recyclerView.adapter = itemAdapter

                        // Extract amounts for chart
                        val amounts = expensesList.map { it.getAmount() }
                        setupChart(amounts)
                    }
                    loadingSpinner.hide()
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(applicationContext, "Failed to fetch data", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(applicationContext, "Error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun getExpenses(response: Response<List<Record>>): List<Expenses>? {
        val records = response.body()
        val expensesList = mutableListOf<Expenses>()
        records?.let {
            for (record in it) {
                if (record is Expenses) {
                    expensesList.add(record)
                }
            }
        }
        val gson = Gson()
        val jsonItems = gson.toJson(expensesList)
        val listType = object : TypeToken<List<Expenses>>() {}.type
        return gson.fromJson(jsonItems, listType)
    }

    private fun trimDate(dateTime: String): String {
        return try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
            val outputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            outputFormat.format(inputFormat.parse(dateTime) ?: return dateTime)
        } catch (e: Exception) {
            e.printStackTrace()
            dateTime // Return the original if parsing fails
        }
    }

    private fun fetchAndSetUsername() {
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            // Try to get username from Firebase
            val firebaseManager = FirebaseManager()
            if (firebaseManager.isUserAuthenticated()) {
                firebaseManager.getUserDisplayName(user_id, object : FirebaseManager.FirebaseCallback<String?> {
                    override fun onSuccess(result: String?) {
                        if (!result.isNullOrBlank()) {
                            secondTextView.text = result
                            // Update session with the retrieved username
                            sessionManager.createLoginSession(
                                sessionManager.getUserEmail() ?: "",
                                result,
                                user_id
                            )
                            Log.w("DashboardActivity", "Username retrieved from Firebase: $result")
                        } else {
                            // Fallback to email prefix
                            val email = sessionManager.getUserEmail()
                            val fallbackName = email?.split("@")?.get(0) ?: "User"
                            secondTextView.text = fallbackName
                            Log.w("DashboardActivity", "Using email prefix as fallback: $fallbackName")
                        }
                    }

                    override fun onFailure(e: Exception) {
                        Log.e("DashboardActivity", "Failed to get username from Firebase", e)
                        // Fallback to email prefix
                        val email = sessionManager.getUserEmail()
                        val fallbackName = email?.split("@")?.get(0) ?: "User"
                        secondTextView.text = fallbackName
                        Log.w("DashboardActivity", "Using email prefix as fallback after Firebase error: $fallbackName")
                    }
                })
            }
        } else {
            // Offline - try local database
            val localUsername = getUserDisplayNameFromLocal(user_id)
            if (!localUsername.isNullOrBlank()) {
                secondTextView.text = localUsername
                Log.w("DashboardActivity", "Username retrieved from local database: $localUsername")
            } else {
                // Final fallback to email prefix
                val email = sessionManager.getUserEmail()
                val fallbackName = email?.split("@")?.get(0) ?: "User"
                secondTextView.text = fallbackName
                Log.w("DashboardActivity", "Using email prefix as final fallback: $fallbackName")
            }
        }
    }

    private fun getUserDisplayNameFromLocal(userId: String): String? {
        val db = dbHelper.readableDatabase
        var displayName: String? = null
        val projection = arrayOf("user_name")
        val selection = "user_id = ?"
        val selectionArgs = arrayOf(userId)
        val cursor = db.query(
            "user",
            projection,
            selection,
            selectionArgs,
            null,
            null,
            null
        )
        if (cursor.moveToFirst()) {
            displayName = cursor.getString(cursor.getColumnIndexOrThrow("user_name"))
        }
        cursor.close()
        db.close()
        return displayName
    }

    private fun checkAndSyncData() {
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            // Schedule sync work for unsynced data
            val syncService = SyncService(this)
            syncService.syncLocalDataToFirebase(object : SyncService.SyncCallback {
                override fun onSyncComplete(success: Boolean, message: String) {
                    if (success) {
                        Log.d("DashboardActivity", "Data sync completed successfully")
                    } else {
                        Log.e("DashboardActivity", "Data sync failed: $message")
                    }
                }
            })
        }
    }

    /**
     * Setup the FloatingActionButton menu with animations
     */
    private fun setupFabMenu() {
        // FAB functionality disabled - resources not available in layout
        Log.w("DashboardActivity", "FAB functionality disabled - resources not available in layout")
        return

        // Initially hide sub FABs and labels
        fabAddExpense?.visibility = View.GONE
        fabAddBudget?.visibility = View.GONE
        fabLabelExpense?.visibility = View.GONE
        fabLabelBudget?.visibility = View.GONE

        // Set click listeners
        fabMain?.setOnClickListener {
            if (isFabMenuOpen) {
                closeFabMenu()
            } else {
                openFabMenu()
            }
        }

        fabAddExpense?.setOnClickListener {
            closeFabMenu()
            val intent = Intent(this@DashboardActivity, ExpensesActivity::class.java)
            startActivity(intent)
        }

        fabAddBudget?.setOnClickListener {
            closeFabMenu()
            val intent = Intent(this@DashboardActivity, TransactionsActivity::class.java)
            startActivity(intent)
        }

        // Add click listeners to labels as well for better UX
        fabLabelExpense?.setOnClickListener {
            closeFabMenu()
            val intent = Intent(this@DashboardActivity, ExpensesActivity::class.java)
            startActivity(intent)
        }

        fabLabelBudget?.setOnClickListener {
            closeFabMenu()
            val intent = Intent(this@DashboardActivity, TransactionsActivity::class.java)
            startActivity(intent)
        }
    }

    /**
     * Open the FAB menu with animation
     */
    private fun openFabMenu() {
        if (fabMain == null) return

        isFabMenuOpen = true

        // Rotate main FAB
        fabMain?.animate()?.rotation(45f)?.setDuration(300)?.start()

        // Show and animate sub FABs and labels
        fabAddExpense?.let { fab ->
            fab.visibility = View.VISIBLE
            fab.animate()
                .translationY(-200f) // Use hardcoded value instead of R.dimen.fab_margin_1
                .alpha(1f)
                .setDuration(300)
                .start()
        }

        fabAddBudget?.let { fab ->
            fab.visibility = View.VISIBLE
            fab.animate()
                .translationY(-350f) // Use hardcoded value instead of R.dimen.fab_margin_2
                .alpha(1f)
                .setDuration(300)
                .start()
        }

        fabLabelExpense?.let { label ->
            label.visibility = View.VISIBLE
            label.animate()
                .translationY(-200f) // Use hardcoded value instead of R.dimen.fab_margin_1
                .alpha(1f)
                .setDuration(300)
                .start()
        }

        fabLabelBudget?.let { label ->
            label.visibility = View.VISIBLE
            label.animate()
                .translationY(-350f) // Use hardcoded value instead of R.dimen.fab_margin_2
                .alpha(1f)
                .setDuration(300)
                .start()
        }
    }

    /**
     * Close the FAB menu with animation
     */
    private fun closeFabMenu() {
        if (fabMain == null) return

        isFabMenuOpen = false

        // Rotate main FAB back
        fabMain?.animate()?.rotation(0f)?.setDuration(300)?.start()

        // Hide and animate sub FABs and labels
        fabAddExpense?.animate()
            ?.translationY(0f)
            ?.alpha(0f)
            ?.setDuration(300)
            ?.withEndAction { fabAddExpense?.visibility = View.GONE }
            ?.start()

        fabAddBudget?.animate()
            ?.translationY(0f)
            ?.alpha(0f)
            ?.setDuration(300)
            ?.withEndAction { fabAddBudget?.visibility = View.GONE }
            ?.start()

        fabLabelExpense?.animate()
            ?.translationY(0f)
            ?.alpha(0f)
            ?.setDuration(300)
            ?.withEndAction { fabLabelExpense?.visibility = View.GONE }
            ?.start()

        fabLabelBudget?.animate()
            ?.translationY(0f)
            ?.alpha(0f)
            ?.setDuration(300)
            ?.withEndAction { fabLabelBudget?.visibility = View.GONE }
            ?.start()
    }

    /**
     * Shows a dialog with options to add either an expense or transaction
     */
    private fun showAddOptionsDialog() {
        AlertDialog.Builder(this)
            .setTitle("Add New Entry")
            .setMessage("What would you like to add?")
            .setPositiveButton("Add Expense") { _, _ ->
                // Navigate to Add Expense Activity
                val intent = Intent(this@DashboardActivity, ExpensesActivity::class.java)
                startActivity(intent)
            }
            .setNegativeButton("Add Transaction") { _, _ ->
                // Navigate to Add Transaction Activity
                val intent = Intent(this@DashboardActivity, TransactionsActivity::class.java)
                startActivity(intent)
            }
            .setNeutralButton("Cancel") { dialog, _ -> dialog.dismiss() }
            .show()
    }

    override fun onBackPressed() {
        if (isFabMenuOpen) {
            closeFabMenu()
        } else {
            super.onBackPressed()
        }
    }
}
