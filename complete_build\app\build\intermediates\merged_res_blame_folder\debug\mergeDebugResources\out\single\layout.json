[{"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/register.xml", "source": "com.example.budgettracker.app-main-62:/layout/register.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/language.xml", "source": "com.example.budgettracker.app-main-62:/layout/language.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/notifications.xml", "source": "com.example.budgettracker.app-main-62:/layout/notifications.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/content_main.xml", "source": "com.example.budgettracker.app-main-62:/layout/content_main.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/dashboard.xml", "source": "com.example.budgettracker.app-main-62:/layout/dashboard.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/login.xml", "source": "com.example.budgettracker.app-main-62:/layout/login.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/addtransaction.xml", "source": "com.example.budgettracker.app-main-62:/layout/addtransaction.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/item_notification.xml", "source": "com.example.budgettracker.app-main-62:/layout/item_notification.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/spinner_item.xml", "source": "com.example.budgettracker.app-main-62:/layout/spinner_item.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/settings.xml", "source": "com.example.budgettracker.app-main-62:/layout/settings.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/addexpense.xml", "source": "com.example.budgettracker.app-main-62:/layout/addexpense.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/activity_main.xml", "source": "com.example.budgettracker.app-main-62:/layout/activity_main.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/list_item.xml", "source": "com.example.budgettracker.app-main-62:/layout/list_item.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/base.xml", "source": "com.example.budgettracker.app-main-62:/layout/base.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/spinner.xml", "source": "com.example.budgettracker.app-main-62:/layout/spinner.xml"}, {"merged": "com.example.budgettracker.app-mergeDebugResources-59:/layout/list_bug.xml", "source": "com.example.budgettracker.app-main-62:/layout/list_bug.xml"}]