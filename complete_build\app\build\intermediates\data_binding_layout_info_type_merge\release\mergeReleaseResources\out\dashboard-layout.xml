<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dashboard" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\dashboard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dashboard_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="516" endOffset="16"/></Target><Target id="@+id/footer" tag="layout/dashboard_0" include="base"><Expressions/><location startLine="401" startOffset="4" endLine="406" endOffset="49"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="504" startOffset="4" endLine="514" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="512" startOffset="8" endLine="512" endOffset="43"/></Target><Target id="@+id/header_layout" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="77" endOffset="18"/></Target><Target id="@+id/app_logo" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="49"/></Target><Target id="@+id/firstTextView" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="56"/></Target><Target id="@+id/button_notification" view="ImageButton"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="45"/></Target><Target id="@+id/secondTextView" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="65" endOffset="52"/></Target><Target id="@+id/button_logout" view="ImageButton"><Expressions/><location startLine="67" startOffset="8" endLine="75" endOffset="45"/></Target><Target id="@+id/budget_id" view="TextView"><Expressions/><location startLine="117" startOffset="20" endLine="126" endOffset="57"/></Target><Target id="@+id/spending_vs_target" view="TextView"><Expressions/><location startLine="145" startOffset="24" endLine="153" endOffset="51"/></Target><Target id="@+id/balance_vs_budget" view="TextView"><Expressions/><location startLine="174" startOffset="24" endLine="182" endOffset="51"/></Target><Target id="@+id/total_expenditure" view="TextView"><Expressions/><location startLine="203" startOffset="24" endLine="211" endOffset="51"/></Target><Target id="@+id/graph" view="com.jjoe64.graphview.GraphView"><Expressions/><location startLine="243" startOffset="20" endLine="247" endOffset="60"/></Target><Target id="@+id/btn_1d" view="Button"><Expressions/><location startLine="256" startOffset="24" endLine="268" endOffset="54"/></Target><Target id="@+id/btn_1w" view="Button"><Expressions/><location startLine="270" startOffset="24" endLine="282" endOffset="55"/></Target><Target id="@+id/btn_1m" view="Button"><Expressions/><location startLine="284" startOffset="24" endLine="296" endOffset="55"/></Target><Target id="@+id/btn_3m" view="Button"><Expressions/><location startLine="298" startOffset="24" endLine="310" endOffset="55"/></Target><Target id="@+id/btn_1y" view="Button"><Expressions/><location startLine="312" startOffset="24" endLine="324" endOffset="55"/></Target><Target id="@+id/itemName" view="TextView"><Expressions/><location startLine="354" startOffset="24" endLine="363" endOffset="68"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="380" startOffset="20" endLine="384" endOffset="64"/></Target><Target id="@+id/root_layout" view="RelativeLayout"><Expressions/><location startLine="391" startOffset="12" endLine="394" endOffset="54"/></Target><Target id="@+id/fab_label_expense" view="TextView"><Expressions/><location startLine="410" startOffset="4" endLine="428" endOffset="35"/></Target><Target id="@+id/fab_add_expense" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="431" startOffset="4" endLine="446" endOffset="28"/></Target><Target id="@+id/fab_label_budget" view="TextView"><Expressions/><location startLine="449" startOffset="4" endLine="467" endOffset="35"/></Target><Target id="@+id/fab_add_budget" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="470" startOffset="4" endLine="485" endOffset="28"/></Target><Target id="@+id/fab_add_transaction" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="488" startOffset="4" endLine="501" endOffset="30"/></Target></Targets></Layout>