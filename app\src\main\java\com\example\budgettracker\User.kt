package com.example.budgettracker

import com.google.gson.annotations.SerializedName

class User(
    user_id: String,
    private var created_date: String,
    private var user_name: String,
    user_status: Int,
    private var user_email: String
) : Record(user_id) {
    
    private var user_status: String = user_status.toString()

    // Getters and Setters
    fun getUserId(): String {
        return super.getuser_id()
    }

    fun setUserId(user_id: String) {
        super.setuser_id(user_id)
    }

    fun getCreatedDate(): String {
        return created_date
    }

    fun setCreatedDate(created_date: String) {
        this.created_date = created_date
    }

    fun getUserName(): String {
        return user_name
    }

    fun setUserName(user_name: String) {
        this.user_name = user_name
    }

    fun getUserStatus(): String {
        return user_status
    }

    fun setUserStatus(user_status: String) {
        this.user_status = user_status
    }

    fun getUserEmail(): String {
        return user_email
    }

    fun setUserEmail(user_email: String) {
        this.user_email = user_email
    }
}
