package com.example.budgettracker;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

public class LoadingSpinner {
    private final View spinnerView;
    private final RelativeLayout rootLayout;

    public LoadingSpinner(Context context, RelativeLayout rootLayout) {
        this.rootLayout = rootLayout;
        LayoutInflater inflater = LayoutInflater.from(context);
        spinnerView = inflater.inflate(R.layout.spinner, rootLayout, false);
        rootLayout.addView(spinnerView);
        spinnerView.setVisibility(View.GONE);
    }

    public void show() {
        if (spinnerView != null) {
            spinnerView.setVisibility(View.VISIBLE);
            setAllViewsEnabled(rootLayout, false);
        }
    }

    public void hide() {
        if (spinnerView != null) {
            spinnerView.setVisibility(View.GONE);
            setAllViewsEnabled(rootLayout, true);
        }
    }

    private void setAllViewsEnabled(View view, boolean enabled) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                View child = viewGroup.getChildAt(i);
                child.setEnabled(enabled);
                if (child instanceof ViewGroup) {
                    setAllViewsEnabled(child, enabled);
                }
            }
        } else {
            view.setEnabled(enabled);
        }
    }
}


//public class LoadingSpinner {
//    private final View spinnerView;
//
//    public LoadingSpinner(Context context, RelativeLayout rootLayout) {
//        LayoutInflater inflater = LayoutInflater.from(context);
//        spinnerView = inflater.inflate(R.layout.spinner, rootLayout, false);
//        rootLayout.addView(spinnerView);
//        spinnerView.setVisibility(View.GONE);
//    }
//
//    public void show() {
//        if (spinnerView != null) {
//            spinnerView.setVisibility(View.VISIBLE);
//        }
//    }
//
//    public void hide() {
//        if (spinnerView != null) {
//            spinnerView.setVisibility(View.GONE);
//        }
//    }
//}

