<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="list_item" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\list_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/list_item_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="136" endOffset="51"/></Target><Target id="@+id/list_icon" view="ImageView"><Expressions/><location startLine="27" startOffset="12" endLine="35" endOffset="51"/></Target><Target id="@+id/itemName" view="TextView"><Expressions/><location startLine="45" startOffset="16" endLine="55" endOffset="60"/></Target><Target id="@+id/itemDate" view="TextView"><Expressions/><location startLine="58" startOffset="16" endLine="65" endOffset="52"/></Target><Target id="@+id/itemAmount" view="TextView"><Expressions/><location startLine="70" startOffset="12" endLine="79" endOffset="50"/></Target><Target id="@+id/expand_button" view="ImageButton"><Expressions/><location startLine="82" startOffset="12" endLine="90" endOffset="43"/></Target><Target id="@+id/expandable_details" view="LinearLayout"><Expressions/><location startLine="95" startOffset="8" endLine="132" endOffset="22"/></Target><Target id="@+id/itemFullDescription" view="TextView"><Expressions/><location startLine="110" startOffset="12" endLine="117" endOffset="48"/></Target><Target id="@+id/itemCategory" view="TextView"><Expressions/><location startLine="120" startOffset="12" endLine="130" endOffset="43"/></Target></Targets></Layout>