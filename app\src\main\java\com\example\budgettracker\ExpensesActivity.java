package com.example.budgettracker;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Build;
import android.os.Bundle;
import android.util.*;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageButton;
import android.content.Intent;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import android.app.DatePickerDialog;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import retrofit2.Call;

public class ExpensesActivity extends AppCompatActivity {

    private EditText editText2, editText4;
    private TextView editText3;
    private String selectedCategory = "Select Category";
    private final String selectedDate = "";
    private DatabaseHelper dbHelper;
    private SessionManager sessionManager;
    private FirebaseManager firebaseManager;
    public String user_id;
    private LoadingSpinner loadingSpinner;
    @SuppressLint("WrongViewCast")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.addexpense);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);
        loadingSpinner = new LoadingSpinner(this, rootLayout);

        // Initialize Firebase Manager
        firebaseManager = new FirebaseManager();
        // Initialize views
        editText3 = findViewById(R.id.editText3);
        editText2 = findViewById(R.id.editText2);
        editText4 = findViewById(R.id.editText4);
        Spinner categorySpinner = findViewById(R.id.category_spinner);
        Button saveButton = findViewById(R.id.save_button);
        Button datePickerButton = findViewById(R.id.date_picker_button);
        dbHelper = new DatabaseHelper(this);

        sessionManager = new SessionManager(this);
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid();
        }

        // Request notification permission for Android 13+
        requestNotificationPermission();

        //back button
        ImageButton backButton = findViewById(R.id.back_button);
        backButton.setOnClickListener(v -> {
            Intent intent = new Intent(ExpensesActivity.this, DashboardActivity.class);
            startActivity(intent);
        });

        // Setup navigation buttons
        setupNavigationButtons();
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
        String currentDate = dateFormat.format(calendar.getTime());
        editText3.setText(currentDate);

        editText4.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });

        editText4.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });
        editText2.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });


        // Set up Spinner for categories
        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                R.array.categories_array, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(adapter);
        categorySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                selectedCategory = parent.getItemAtPosition(position).toString();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedCategory = "Select Category";
            }
        });
        datePickerButton.setOnClickListener(v -> showDatePicker());
        saveButton.setOnClickListener(v -> {

            String note = editText2.getText().toString();
            String amountString = editText4.getText().toString();
            String date = editText3.getText().toString();
            String selectedCategory = categorySpinner.getSelectedItem().toString();

            if (note.isEmpty() || amountString.isEmpty() || date.isEmpty() || selectedCategory.equals("Select Category")) {
                Toast.makeText(ExpensesActivity.this, "Please fill in all fields", Toast.LENGTH_SHORT).show();

                return;
            }
            int budgetAmount;
            try {
                budgetAmount = Integer.parseInt(amountString);
            } catch (NumberFormatException e) {
                Toast.makeText(ExpensesActivity.this, "Invalid amount. Please enter a valid number.", Toast.LENGTH_SHORT).show();

                return;
            }
            // Save to Firebase with automatic fallback to local storage
            loadingSpinner.show();

            if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) && firebaseManager.isUserAuthenticated()) {
                // Save to Firebase
                firebaseManager.saveExpense(selectedCategory, note, date, budgetAmount, new FirebaseManager.FirebaseCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        // Update transaction balance in Firebase
                        String monthYear = date.substring(0, 7);
                        updateTransactionBalanceFirebase(monthYear, budgetAmount);

                        // Create expense notification in Firebase
                        firebaseManager.createExpenseNotification(selectedCategory, budgetAmount, new FirebaseManager.FirebaseCallback<Void>() {
                            @Override
                            public void onSuccess(Void result) {
                                Log.d("ExpensesActivity", "Expense notification created");
                            }

                            @Override
                            public void onFailure(Exception e) {
                                Log.e("ExpensesActivity", "Failed to create expense notification", e);
                            }
                        });

                        // Also save locally for offline access (mark as synced)
                        SQLiteDatabase db = dbHelper.getWritableDatabase();
                        saveDataToDatabaseSynced(db, selectedCategory, date, note, budgetAmount, budgetAmount);

                        loadingSpinner.hide();
                        Toast.makeText(ExpensesActivity.this, "Data saved successfully", Toast.LENGTH_SHORT).show();

                        // Show success notification
                        NotificationHelper.sendNotification(ExpensesActivity.this, "Expense Added",
                            String.format("Added %s: $%d (Synced to cloud)", selectedCategory, budgetAmount));

                        // Navigate back to dashboard to refresh balance
                        Intent intent = new Intent(ExpensesActivity.this, DashboardActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                        startActivity(intent);
                        finish();
                    }

                    @Override
                    public void onFailure(Exception e) {
                        // Fallback to local storage
                        SQLiteDatabase db = dbHelper.getWritableDatabase();
                        saveDataToDatabaseWithNavigation(db, selectedCategory, date, note, budgetAmount, budgetAmount);
                        loadingSpinner.hide();
                        Toast.makeText(ExpensesActivity.this, "Saved locally. Will sync when online.", Toast.LENGTH_SHORT).show();
                    }
                });
            } else {
                // Save locally only
                SQLiteDatabase db = dbHelper.getWritableDatabase();
                saveDataToDatabaseWithNavigation(db, selectedCategory, date, note, budgetAmount, budgetAmount);
                loadingSpinner.hide();

                // Show local notification for offline save
                NotificationHelper.sendNotification(this, "Expense Added",
                    String.format("Added %s: $%d (Saved locally)", selectedCategory, budgetAmount));
            }
            });
    }

    @SuppressLint("UnsafeIntentLaunch")
    private void saveDataToDatabase(SQLiteDatabase db,String name, String date, String note, int amount, int budgetAmount) {
        ContentValues values = new ContentValues();
        values.put("name", name);
        values.put("date", date);
        values.put( "note", note);
        values.put( "amount", amount);
        values.put("budget_amount", budgetAmount);
        values.put("user_id", user_id);
        values.put("synced", 0); // Mark as not synced

        long newRowId = db.insert("expenses", null, values);
        if (newRowId == -1) {
            Toast.makeText(ExpensesActivity.this, "Error saving data", Toast.LENGTH_SHORT).show();
        } else {
            String monthYear =date.substring(0, 7);
            updateTransactionBalance(db, monthYear, amount);
            Toast.makeText(ExpensesActivity.this, "Data saved successfully", Toast.LENGTH_SHORT).show();

            // Show notification for local save
            NotificationHelper.sendNotification(ExpensesActivity.this, "Expense Added",
                String.format("Added %s: $%d", name, amount));

            // Navigate back to dashboard to refresh balance
            Intent intent = new Intent(ExpensesActivity.this, DashboardActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            startActivity(intent);
            finish();
        }
        db.close();
    }

    @SuppressLint("UnsafeIntentLaunch")
    private void saveDataToDatabaseWithNavigation(SQLiteDatabase db,String name, String date, String note, int amount, int budgetAmount) {
        ContentValues values = new ContentValues();
        values.put("name", name);
        values.put("date", date);
        values.put( "note", note);
        values.put( "amount", amount);
        values.put("budget_amount", budgetAmount);
        values.put("user_id", user_id);
        values.put("synced", 0); // Mark as not synced

        long newRowId = db.insert("expenses", null, values);
        if (newRowId == -1) {
            Toast.makeText(ExpensesActivity.this, "Error saving data", Toast.LENGTH_SHORT).show();
        } else {
            String monthYear =date.substring(0, 7);
            updateTransactionBalance(db, monthYear, amount);
            Toast.makeText(ExpensesActivity.this, "Data saved successfully", Toast.LENGTH_SHORT).show();

            // Show notification for local save
            NotificationHelper.sendNotification(ExpensesActivity.this, "Expense Added",
                String.format("Added %s: $%d", name, amount));

            // Navigate back to dashboard to refresh balance
            Intent intent = new Intent(ExpensesActivity.this, DashboardActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            startActivity(intent);
            finish();
        }
        db.close();
    }

    @SuppressLint("UnsafeIntentLaunch")
    private void saveDataToDatabaseSynced(SQLiteDatabase db,String name, String date, String note, int amount, int budgetAmount) {
        ContentValues values = new ContentValues();
        values.put("name", name);
        values.put("date", date);
        values.put( "note", note);
        values.put( "amount", amount);
        values.put("budget_amount", budgetAmount);
        values.put("user_id", user_id);
        values.put("synced", 1); // Mark as synced

        long newRowId = db.insert("expenses", null, values);
        if (newRowId == -1) {
            Toast.makeText(ExpensesActivity.this, "Error saving data", Toast.LENGTH_SHORT).show();
        } else {
            String monthYear =date.substring(0, 7);
            updateTransactionBalance(db, monthYear, amount);
        }
        db.close();
    }

    private void updateTransactionBalance(SQLiteDatabase db, String monthYear, int amount) {
        String balanceQuery = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE  ? AND user_id = ?";
        Cursor cursor = db.rawQuery(balanceQuery, new String[]{monthYear, String.valueOf(user_id)});
        float currentBalance = 0;
        if (cursor.moveToFirst()) {
            currentBalance = cursor.getFloat(0);
            cursor.close();
            float newBalance = currentBalance - amount;
            ContentValues values = new ContentValues();
            values.put("trans_balance", newBalance);
            int rowsUpdated = db.update("transactions", values, "strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?", new String[]{monthYear,String.valueOf(user_id)});
            if (rowsUpdated == 0) {Log.w("DatabaseHelper", "No rows updated in transactions table.");}
        }
    }
    private void updateTransactionBalanceFirebase(String monthYear, int amount) {
        firebaseManager.getOrCreateTransactionSummary(monthYear, new FirebaseManager.FirebaseCallback<Map<String, Object>>() {
            @Override
            public void onSuccess(Map<String, Object> result) {
                double currentBalance = 0.0;
                double target = 0.0;
                double budget = 0.0;

                if (result.containsKey("trans_balance")) {
                    currentBalance = ((Number) result.get("trans_balance")).doubleValue();
                }
                if (result.containsKey("trans_target")) {
                    target = ((Number) result.get("trans_target")).doubleValue();
                }
                if (result.containsKey("trans_budget")) {
                    budget = ((Number) result.get("trans_budget")).doubleValue();
                }

                double newBalance = currentBalance - amount;

                firebaseManager.updateTransactionSummary(monthYear, newBalance, target, budget, new FirebaseManager.FirebaseCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        Log.i("Firebase", "Transaction balance updated successfully");
                    }

                    @Override
                    public void onFailure(Exception e) {
                        Log.e("Firebase", "Failed to update transaction balance", e);
                    }
                });
            }

            @Override
            public void onFailure(Exception e) {
                Log.e("Firebase", "Error getting transaction summary", e);
            }
        });
    }


    private void showDatePicker() {
        final Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        DatePickerDialog datePickerDialog = new DatePickerDialog(this,
                (view, selectedYear, selectedMonth, selectedDay) -> {
                    Calendar selectedDate = Calendar.getInstance();
                    selectedDate.set(selectedYear, selectedMonth, selectedDay);
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
                    String selectedDateString = dateFormat.format(selectedDate.getTime());
                    editText3.setText(selectedDateString);
                }, year, month, day);
        datePickerDialog.show();
    }

    private void setupNavigationButtons() {
        ImageButton homeButton = findViewById(R.id.button_home);
        ImageButton expensesButton = findViewById(R.id.button_expenses);
        ImageButton transactionsButton = findViewById(R.id.button_transactions);
        ImageButton profileButton = findViewById(R.id.button_profile);
        ImageButton notificationButton = findViewById(R.id.button_notification);

        homeButton.setOnClickListener(v -> navigateTo(DashboardActivity.class));
        expensesButton.setOnClickListener(v -> {
            // Already in expenses activity, do nothing or refresh
        });
        transactionsButton.setOnClickListener(v -> navigateTo(TransactionsActivity.class));
        profileButton.setOnClickListener(v -> navigateTo(SettingsActivity.class));
        notificationButton.setOnClickListener(v -> navigateTo(NotificationActivity.class));
    }

    private void navigateTo(Class<?> activityClass) {
        if (!this.getClass().equals(activityClass)) {
            Intent intent = new Intent(this, activityClass);
            startActivity(intent);
        }
    }

    private void requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this,
                    new String[]{android.Manifest.permission.POST_NOTIFICATIONS},
                    1001);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 1001) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d("ExpensesActivity", "Notification permission granted");
            } else {
                Log.w("ExpensesActivity", "Notification permission denied");
                Toast.makeText(this, "Notification permission denied. You won't receive expense notifications.", Toast.LENGTH_LONG).show();
            }
        }
    }

}
