<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Transaction Icon -->
        <LinearLayout
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/transaction_icon_background"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/list_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/attach_money"
                android:tint="?attr/colorOnSecondaryContainer" />

        </LinearLayout>

        <!-- Content Column -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Transaction Name -->
            <TextView
                android:id="@+id/itemName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/name_bug"
                android:textColor="?attr/colorOnSurface"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- Transaction Date -->
            <TextView
                android:id="@+id/itemDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/exp_date"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="2dp"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- Amount Column -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end"
            android:layout_marginStart="12dp">

            <!-- Amount -->
            <TextView
                android:id="@+id/itemAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/bug_amount"
                android:textColor="?attr/colorSecondary"
                android:textStyle="bold"
                android:textSize="16sp"
                android:fontFamily="sans-serif-medium" />

            <!-- Transaction Type Label -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Budget"
                android:textSize="10sp"
                android:textColor="?attr/colorSecondary"
                android:background="@drawable/transaction_type_chip"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>