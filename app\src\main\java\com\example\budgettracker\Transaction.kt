package com.example.budgettracker

class Transaction(
    private var trans_budget: Double,
    private var trans_balance: Double,
    private var trans_target: Double,
    user_id: String,
    private var trans_month: String
) : Record(user_id) {

    // Getters and Setters
    fun getDate(): String {
        return trans_month
    }

    fun setDate(trans_month: String) {
        this.trans_month = trans_month
    }

    fun getTransTarget(): Double {
        return trans_target
    }

    fun setTransTarget(trans_target: Double) {
        this.trans_target = trans_target
    }

    fun getBalance(): Double {
        return trans_balance
    }

    fun setBalance(trans_balance: Double) {
        this.trans_balance = trans_balance
    }

    fun getTotal(): Double {
        return trans_budget
    }

    fun setTotal(trans_budget: Double) {
        this.trans_budget = trans_budget
    }
}
