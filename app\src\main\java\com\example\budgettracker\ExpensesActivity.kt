package com.example.budgettracker

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.ContentValues
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.RelativeLayout
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import java.text.SimpleDateFormat
import java.util.*

class ExpensesActivity : AppCompatActivity() {

    private lateinit var editText2: EditText
    private lateinit var editText4: EditText
    private lateinit var editText3: TextView
    private var selectedCategory = "Select Category"
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var sessionManager: SessionManager
    private lateinit var firebaseManager: FirebaseManager
    lateinit var user_id: String
    private lateinit var loadingSpinner: LoadingSpinner

    @SuppressLint("WrongViewCast")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.addexpense)
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)

        // Initialize Firebase Manager
        firebaseManager = FirebaseManager()
        // Initialize views
        editText3 = findViewById(R.id.editText3)
        editText2 = findViewById(R.id.editText2)
        editText4 = findViewById(R.id.editText4)
        val categorySpinner = findViewById<Spinner>(R.id.category_spinner)
        val saveButton = findViewById<Button>(R.id.save_button)
        val datePickerButton = findViewById<Button>(R.id.date_picker_button)
        dbHelper = DatabaseHelper(this)

        sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid() ?: ""
        }

        // Request notification permission for Android 13+
        requestNotificationPermission()

        //back button
        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@ExpensesActivity, DashboardActivity::class.java)
            startActivity(intent)
        }

        // Setup navigation buttons
        setupNavigationButtons()
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
        val currentDate = dateFormat.format(calendar.time)
        editText3.text = currentDate

        editText4.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick()
                true
            } else {
                false
            }
        }

        editText2.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick()
                true
            } else {
                false
            }
        }

        // Set up Spinner for categories
        val adapter = ArrayAdapter.createFromResource(
            this,
            R.array.categories_array, android.R.layout.simple_spinner_item
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        categorySpinner.adapter = adapter
        categorySpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>, view: View?, position: Int, id: Long) {
                selectedCategory = parent.getItemAtPosition(position).toString()
            }

            override fun onNothingSelected(parent: AdapterView<*>) {
                selectedCategory = "Select Category"
            }
        }
        
        datePickerButton.setOnClickListener { showDatePicker() }
        saveButton.setOnClickListener {
            val note = editText2.text.toString()
            val amountString = editText4.text.toString()
            val date = editText3.text.toString()
            val selectedCategory = categorySpinner.selectedItem.toString()

            if (note.isEmpty() || amountString.isEmpty() || date.isEmpty() || selectedCategory == "Select Category") {
                Toast.makeText(this@ExpensesActivity, "Please fill in all fields", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            val budgetAmount: Int
            try {
                budgetAmount = amountString.toInt()
            } catch (e: NumberFormatException) {
                Toast.makeText(this@ExpensesActivity, "Invalid amount. Please enter a valid number.", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            // Save to Firebase with automatic fallback to local storage
            loadingSpinner.show()

            if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) && firebaseManager.isUserAuthenticated()) {
                // Save to Firebase
                firebaseManager.saveExpense(selectedCategory, note, date, budgetAmount, object : FirebaseManager.FirebaseCallback<Void> {
                    override fun onSuccess(result: Void?) {
                        // Update transaction balance in Firebase
                        val monthYear = date.substring(0, 7)
                        updateTransactionBalanceFirebase(monthYear, budgetAmount)

                        // Create expense notification in Firebase
                        firebaseManager.createExpenseNotification(selectedCategory, budgetAmount.toDouble(), object : FirebaseManager.FirebaseCallback<Void> {
                            override fun onSuccess(result: Void?) {
                                Log.d("ExpensesActivity", "Expense notification created")
                            }

                            override fun onFailure(e: Exception) {
                                Log.e("ExpensesActivity", "Failed to create expense notification", e)
                            }
                        })

                        // Also save locally for offline access (mark as synced)
                        val db = dbHelper.writableDatabase
                        saveDataToDatabaseSynced(db, selectedCategory, date, note, budgetAmount, budgetAmount)

                        loadingSpinner.hide()
                        Toast.makeText(this@ExpensesActivity, "Data saved successfully", Toast.LENGTH_SHORT).show()

                        // Show success notification
                        NotificationHelper.sendNotification(
                            this@ExpensesActivity, "Expense Added",
                            String.format("Added %s: $%d (Synced to cloud)", selectedCategory, budgetAmount)
                        )

                        // Navigate back to dashboard to refresh balance
                        val intent = Intent(this@ExpensesActivity, DashboardActivity::class.java)
                        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                        startActivity(intent)
                        finish()
                    }

                    override fun onFailure(e: Exception) {
                        // Fallback to local storage
                        val db = dbHelper.writableDatabase
                        saveDataToDatabaseWithNavigation(db, selectedCategory, date, note, budgetAmount, budgetAmount)
                        loadingSpinner.hide()
                        Toast.makeText(this@ExpensesActivity, "Saved locally. Will sync when online.", Toast.LENGTH_SHORT).show()
                    }
                })
            } else {
                // Save locally only
                val db = dbHelper.writableDatabase
                saveDataToDatabaseWithNavigation(db, selectedCategory, date, note, budgetAmount, budgetAmount)
                loadingSpinner.hide()

                // Show local notification for offline save
                NotificationHelper.sendNotification(
                    this, "Expense Added",
                    String.format("Added %s: $%d (Saved locally)", selectedCategory, budgetAmount)
                )
            }
        }
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToDatabase(db: SQLiteDatabase, name: String, date: String, note: String, amount: Int, budgetAmount: Int) {
        val values = ContentValues().apply {
            put("name", name)
            put("date", date)
            put("note", note)
            put("amount", amount)
            put("budget_amount", budgetAmount)
            put("user_id", user_id)
            put("synced", 0) // Mark as not synced
        }

        val newRowId = db.insert("expenses", null, values)
        if (newRowId == -1L) {
            Toast.makeText(this@ExpensesActivity, "Error saving data", Toast.LENGTH_SHORT).show()
        } else {
            val monthYear = date.substring(0, 7)
            updateTransactionBalance(db, monthYear, amount)
            Toast.makeText(this@ExpensesActivity, "Data saved successfully", Toast.LENGTH_SHORT).show()

            // Show notification for local save
            NotificationHelper.sendNotification(
                this@ExpensesActivity, "Expense Added",
                String.format("Added %s: $%d", name, amount)
            )

            // Navigate back to dashboard to refresh balance
            val intent = Intent(this@ExpensesActivity, DashboardActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(intent)
            finish()
        }
        db.close()
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToDatabaseWithNavigation(db: SQLiteDatabase, name: String, date: String, note: String, amount: Int, budgetAmount: Int) {
        val values = ContentValues().apply {
            put("name", name)
            put("date", date)
            put("note", note)
            put("amount", amount)
            put("budget_amount", budgetAmount)
            put("user_id", user_id)
            put("synced", 0) // Mark as not synced
        }

        val newRowId = db.insert("expenses", null, values)
        if (newRowId == -1L) {
            Toast.makeText(this@ExpensesActivity, "Error saving data", Toast.LENGTH_SHORT).show()
        } else {
            val monthYear = date.substring(0, 7)
            updateTransactionBalance(db, monthYear, amount)
            Toast.makeText(this@ExpensesActivity, "Data saved successfully", Toast.LENGTH_SHORT).show()

            // Show notification for local save
            NotificationHelper.sendNotification(
                this@ExpensesActivity, "Expense Added",
                String.format("Added %s: $%d", name, amount)
            )

            // Navigate back to dashboard to refresh balance
            val intent = Intent(this@ExpensesActivity, DashboardActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(intent)
            finish()
        }
        db.close()
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToDatabaseSynced(db: SQLiteDatabase, name: String, date: String, note: String, amount: Int, budgetAmount: Int) {
        val values = ContentValues().apply {
            put("name", name)
            put("date", date)
            put("note", note)
            put("amount", amount)
            put("budget_amount", budgetAmount)
            put("user_id", user_id)
            put("synced", 1) // Mark as synced
        }

        val newRowId = db.insert("expenses", null, values)
        if (newRowId == -1L) {
            Toast.makeText(this@ExpensesActivity, "Error saving data", Toast.LENGTH_SHORT).show()
        } else {
            val monthYear = date.substring(0, 7)
            updateTransactionBalance(db, monthYear, amount)
        }
        db.close()
    }

    private fun updateTransactionBalance(db: SQLiteDatabase, monthYear: String, amount: Int) {
        val balanceQuery = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(balanceQuery, arrayOf(monthYear, user_id))
        var currentBalance = 0f
        if (cursor.moveToFirst()) {
            currentBalance = cursor.getFloat(0)
            cursor.close()
            val newBalance = currentBalance - amount
            val values = ContentValues()
            values.put("trans_balance", newBalance)
            val rowsUpdated = db.update("transactions", values, "strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?", arrayOf(monthYear, user_id))
            if (rowsUpdated == 0) {
                Log.w("DatabaseHelper", "No rows updated in transactions table.")
            }
        }
    }

    private fun updateTransactionBalanceFirebase(monthYear: String, amount: Int) {
        firebaseManager.getOrCreateTransactionSummary(monthYear, object : FirebaseManager.FirebaseCallback<Map<String, Any>> {
            override fun onSuccess(result: Map<String, Any>?) {
                if (result == null) {
                    Log.e("Firebase", "Transaction summary result is null")
                    return
                }

                var currentBalance = 0.0
                var target = 0.0
                var budget = 0.0

                if (result.containsKey("trans_balance")) {
                    currentBalance = (result["trans_balance"] as Number).toDouble()
                }
                if (result.containsKey("trans_target")) {
                    target = (result["trans_target"] as Number).toDouble()
                }
                if (result.containsKey("trans_budget")) {
                    budget = (result["trans_budget"] as Number).toDouble()
                }

                val newBalance = currentBalance - amount

                firebaseManager.updateTransactionSummary(monthYear, newBalance, target, budget, object : FirebaseManager.FirebaseCallback<Void> {
                    override fun onSuccess(result: Void?) {
                        Log.i("Firebase", "Transaction balance updated successfully")
                    }

                    override fun onFailure(e: Exception) {
                        Log.e("Firebase", "Failed to update transaction balance", e)
                    }
                })
            }

            override fun onFailure(e: Exception) {
                Log.e("Firebase", "Error getting transaction summary", e)
            }
        })
    }

    private fun showDatePicker() {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(
            this,
            { _, selectedYear, selectedMonth, selectedDay ->
                val selectedDate = Calendar.getInstance()
                selectedDate.set(selectedYear, selectedMonth, selectedDay)
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
                val selectedDateString = dateFormat.format(selectedDate.time)
                editText3.text = selectedDateString
            }, year, month, day
        )
        datePickerDialog.show()
    }

    private fun setupNavigationButtons() {
        val homeButton = findViewById<ImageButton>(R.id.button_home)
        val expensesButton = findViewById<ImageButton>(R.id.button_expenses)
        val transactionsButton = findViewById<ImageButton>(R.id.button_transactions)
        val profileButton = findViewById<ImageButton>(R.id.button_profile)
        val notificationButton = findViewById<ImageButton>(R.id.button_notification)

        homeButton.setOnClickListener { navigateTo(DashboardActivity::class.java) }
        expensesButton.setOnClickListener {
            // Already in expenses activity, do nothing or refresh
        }
        transactionsButton.setOnClickListener { navigateTo(TransactionsActivity::class.java) }
        profileButton.setOnClickListener { navigateTo(SettingsActivity::class.java) }
        notificationButton.setOnClickListener { navigateTo(NotificationActivity::class.java) }
    }

    private fun navigateTo(activityClass: Class<*>) {
        if (this.javaClass != activityClass) {
            val intent = Intent(this, activityClass)
            startActivity(intent)
        }
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(android.Manifest.permission.POST_NOTIFICATIONS),
                    1001
                )
            }
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 1001) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d("ExpensesActivity", "Notification permission granted")
            } else {
                Log.w("ExpensesActivity", "Notification permission denied")
                Toast.makeText(this, "Notification permission denied. You won't receive expense notifications.", Toast.LENGTH_LONG).show()
            }
        }
    }
}
