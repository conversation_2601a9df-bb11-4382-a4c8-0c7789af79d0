<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface">

    <!-- Modern Header -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:scaleType="fitCenter"
            app:tint="?attr/colorOnSurface" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="@string/notifications"
            android:textColor="?attr/colorOnSurface"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

        <Button
            android:id="@+id/clear_all_button"
            style="@style/SmallButton"
            android:text="@string/clear_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="?attr/colorPrimary"
            android:textColor="?attr/colorOnPrimary" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_layout"
        android:layout_above="@id/footer"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Notifications RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/notifications_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="80dp" />

            <!-- Empty State (when no notifications) -->
            <LinearLayout
                android:id="@+id/empty_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="48dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/notifications"
                    android:alpha="0.3"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No notifications yet"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="You'll see important updates about your budget here"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="14sp"
                    android:alpha="0.7"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>
    <include
        android:id="@+id/footer"
        layout="@layout/base"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

</RelativeLayout>
