package com.example.budgettracker

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*

class TransactionsActivity : AppCompatActivity() {

    private lateinit var apiService: ApiService
    private lateinit var recyclerView: RecyclerView
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var budgetInfoTextView: TextView
    private lateinit var emptyTransactionsMessage: TextView
    private var totalBudgetForCurrentMonth = 0
    lateinit var user_id: String
    private lateinit var loadingSpinner: LoadingSpinner

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.addtransaction)
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)

        loadingSpinner = LoadingSpinner(this, rootLayout)

        val sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid() ?: ""
        }

        dbHelper = DatabaseHelper(this)
        recyclerView = findViewById(R.id.recyclerView)
        emptyTransactionsMessage = findViewById(R.id.empty_transactions_message)
        recyclerView.layoutManager = LinearLayoutManager(this)
        
        if (NetworkUtils.isInternetAvailable(this)) {
            fetchAndSetOnline()
            getTotalBudgetForCurrentOnline()
        } else {
            fetchAndSetData()
        }
        budgetInfoTextView = findViewById(R.id.budget_info)

        val saveButton = findViewById<Button>(R.id.save_button)
        val editText5 = findViewById<EditText>(R.id.editText5)
        val editText4 = findViewById<EditText>(R.id.editText4)

        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@TransactionsActivity, DashboardActivity::class.java)
            startActivity(intent)
        }

        // Setup navigation buttons
        setupNavigationButtons()
        
        editText4.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick()
                true
            } else {
                false
            }
        }

        editText5.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick()
                true
            } else {
                false
            }
        }
        
        saveButton.setOnClickListener {
            val note = editText5.text.toString()
            val amountString = editText4.text.toString()
            if (note.isEmpty() || amountString.isEmpty()) {
                Toast.makeText(this@TransactionsActivity, "Please fill in all fields", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            val budgetAmount: Int
            try {
                budgetAmount = amountString.toInt()
            } catch (e: NumberFormatException) {
                Toast.makeText(this@TransactionsActivity, "Invalid amount. Please enter a valid number.", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (NetworkUtils.isInternetAvailable(this)) {
                saveDataToOnline(note, budgetAmount)
            } else {
                saveDataToDatabase(note, budgetAmount)
            }
        }
    }

    private fun parseItems(json: String): List<Budgets> {
        val gson = Gson()
        val budgetsListType = object : TypeToken<List<Budgets>>() {}.type
        return gson.fromJson(json, budgetsListType)
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToDatabase(name: String, budgetAmount: Int) {
        val db = dbHelper.writableDatabase
        val values = ContentValues()
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)
        val query = "SELECT trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id))
        
        if (cursor.moveToFirst()) {
            @SuppressLint("Range") 
            val transTarget = cursor.getString(cursor.getColumnIndex("trans_target"))
            if (transTarget.isNullOrBlank()) {
                Toast.makeText(this@TransactionsActivity, "Monthly Target not Set", Toast.LENGTH_SHORT).show()
                cursor.close()
                db.close()
                return
            }
        } else {
            Toast.makeText(this@TransactionsActivity, "Monthly Target not Set", Toast.LENGTH_SHORT).show()
            cursor.close()
            db.close()
            return
        }

        values.apply {
            put("bud_name", name)
            put("bud_date", currentDate)
            put("bud_amount", budgetAmount)
            put("user_id", user_id)
            put("synced", 0) // Mark as not synced
        }

        val newRowId = db.insert("budget", null, values)
        if (newRowId == -1L) {
            Toast.makeText(this@TransactionsActivity, "Error saving amount", Toast.LENGTH_SHORT).show()
        } else {
            if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
                updateTransactionOnline(monthYear, budgetAmount)
            } else {
                updateTransactionAmounts(db, monthYear, budgetAmount)
            }
            Toast.makeText(this@TransactionsActivity, "Amount Added to your Budget", Toast.LENGTH_SHORT).show()
            val intent = intent
            finish()
            startActivity(intent)
        }
        db.close()
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToOnline(name: String, budgetAmount: Int) {
        loadingSpinner.show()
        val db = dbHelper.writableDatabase
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)
        val targetCall = apiService.getRecords("transactions", monthYear, user_id)
        
        targetCall.enqueue(object : Callback<List<Record>> {
            override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var targetSet = false
                    for (record in records) {
                        if (record is Transaction) {
                            val transTarget = record.getTransTarget().toString()
                            if (transTarget.trim().isEmpty()) {
                                Toast.makeText(this@TransactionsActivity, "Monthly Target not Set", Toast.LENGTH_SHORT).show()
                                loadingSpinner.hide()
                                return
                            } else {
                                targetSet = true
                            }
                        }
                    }
                    if (targetSet) {
                        val budgetData = hashMapOf<String, Any>(
                            "bud_name" to name,
                            "bud_date" to currentDate,
                            "bud_amount" to budgetAmount,
                            "user_id" to user_id
                        )
                        val insertCall = apiService.createRecord("budget", budgetData)
                        insertCall.enqueue(object : Callback<Void> {
                            override fun onResponse(call: Call<Void>, response: Response<Void>) {
                                Log.e("Transaction", response.toString())
                                if (response.isSuccessful) {
                                    updateTransactionOnline(monthYear, budgetAmount)
                                    Toast.makeText(this@TransactionsActivity, "Amount Added to your Budget", Toast.LENGTH_SHORT).show()
                                    val intent = intent
                                    finish()
                                    startActivity(intent)
                                    loadingSpinner.hide()
                                } else {
                                    Toast.makeText(this@TransactionsActivity, "Error saving amount", Toast.LENGTH_SHORT).show()
                                    loadingSpinner.hide()
                                }
                            }

                            override fun onFailure(call: Call<Void>, t: Throwable) {
                                Toast.makeText(this@TransactionsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
                                loadingSpinner.hide()
                            }
                        })
                    }
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(this@TransactionsActivity, "Failed to fetch transaction data", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(this@TransactionsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun updateTransactionAmounts(db: SQLiteDatabase, monthYear: String, budgetAmount: Int) {
        val query = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id))
        var currentBalance = 0f
        if (cursor.moveToFirst()) {
            currentBalance = cursor.getFloat(0)
        }
        cursor.close()

        // Only update balance, not budget (budget stays the same)
        val newBalance = currentBalance + budgetAmount
        val values = ContentValues()
        values.put("trans_balance", newBalance)
        val rowsUpdated = db.update("transactions", values, "strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?", arrayOf(monthYear, user_id))
        if (rowsUpdated == 0) {
            Log.w("DatabaseHelper", "No rows updated in transactions table.")
        }

        Log.d("TransactionsActivity", "Transaction added: +$budgetAmount, New balance: $newBalance")
    }

    private fun updateBudgetInfo(budgetAmount: Int) {
        val budgetInfoTextView = findViewById<TextView>(R.id.budget_info)
        val budgetInfo = getString(R.string.budget_info, budgetAmount)
        budgetInfoTextView.text = budgetInfo
    }

    private fun updateTransactionOnline(monthYear: String, budgetAmount: Int) {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)

        val fetchCall = apiService.getRecords("transactions", monthYear, user_id)
        fetchCall.enqueue(object : Callback<List<Record>> {
            override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var updated = false

                    for (record in records) {
                        if (record is Transaction) {
                            val currentBalance = record.getBalance().toFloat()
                            val currentBudget = record.getTotal().toFloat() // Keep budget unchanged

                            // Step 2: Update only balance, not budget
                            val newBalance = currentBalance + budgetAmount
                            // Budget stays the same - don't add transaction amount to budget

                            // Prepare data for update
                            val updateData = hashMapOf<String, Any>(
                                "trans_balance" to newBalance,
                                "trans_budget" to currentBudget // Keep original budget
                            )

                            // Step 3: Send updated values back to the server
                            val updateCall = apiService.updateRecord("transactions", user_id, monthYear, updateData)
                            updateCall.enqueue(object : Callback<Void> {
                                override fun onResponse(call: Call<Void>, response: Response<Void>) {
                                    if (response.isSuccessful) {
                                        Log.d("UpdateTransaction", "Transaction updated successfully.")
                                    } else {
                                        Log.e("UpdateTransaction", "Error updating transaction: $response")
                                    }
                                    loadingSpinner.hide()
                                }

                                override fun onFailure(call: Call<Void>, t: Throwable) {
                                    Log.e("UpdateTransaction", "Network error updating transaction: ${t.message}")
                                    loadingSpinner.hide()
                                }
                            })
                            updated = true
                            break
                        }
                    }
                    if (!updated) {
                        Log.e("UpdateTransaction", "No transaction found to update.")
                    }
                } else {
                    Log.e("UpdateTransaction", "Error fetching transaction: ${response.message()}")
                    loadingSpinner.hide()
                }
            }

            override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                Log.e("UpdateTransaction", "Network error fetching transaction: ${t.message}")
                loadingSpinner.hide()
            }
        })
    }

    private fun getTotalBudgetForCurrentMonth(db: SQLiteDatabase): Int {
        val sdf = SimpleDateFormat("yyyy-MM", Locale.getDefault())
        val currentMonth = sdf.format(Date())
        val query = "SELECT SUM(bud_amount) FROM budget WHERE strftime('%Y-%m',bud_date) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf("$currentMonth%", user_id))
        var totalAmount = 0
        if (cursor.moveToFirst()) {
            totalAmount = cursor.getInt(0)
        }
        cursor.close()
        return totalAmount
    }

    private fun getTotalBudgetForCurrentOnline() {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val sdf = SimpleDateFormat("yyyy-MM", Locale.getDefault())
        val currentMonth = sdf.format(Date())
        val call = apiService.getRecords("budget", currentMonth, user_id)
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var totalAmount = 0
                    for (record in records) {
                        if (record is Budgets) {
                            totalAmount += record.getAmount()
                        }
                    }
                    updateBudgetInfo(totalAmount)
                    totalBudgetForCurrentMonth = totalAmount
                    loadingSpinner.hide()
                } else {
                    Toast.makeText(applicationContext, "Failed to fetch budget data", Toast.LENGTH_SHORT).show()
                    loadingSpinner.hide()
                }
            }

            override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                Toast.makeText(applicationContext, "Error: ${t.message}", Toast.LENGTH_SHORT).show()
                loadingSpinner.hide()
            }
        })
    }

    private fun fetchAndSetData() {
        val db = dbHelper.readableDatabase

        totalBudgetForCurrentMonth = getTotalBudgetForCurrentMonth(db)
        updateBudgetInfo(totalBudgetForCurrentMonth)
        val projection = arrayOf("bud_name", "bud_date", "bud_amount")
        val selection = "user_id = ?"
        val selectionArgs = arrayOf(user_id)
        val cursor = db.query(
            "budget",
            projection,
            selection,
            selectionArgs,
            null,
            null,
            null
        )
        val budgetsList = mutableListOf<Budgets>()
        while (cursor.moveToNext()) {
            val name = cursor.getString(cursor.getColumnIndexOrThrow("bud_name"))
            val date = cursor.getString(cursor.getColumnIndexOrThrow("bud_date"))
            val amount = cursor.getInt(cursor.getColumnIndexOrThrow("bud_amount"))
            // Process data
            val expense = Budgets(name, date, amount, user_id)
            budgetsList.add(expense)
        }
        cursor.close()
        val gson = Gson()
        val jsonItems = gson.toJson(budgetsList)
        val listType = object : TypeToken<List<Budgets>>() {}.type
        val items: List<Budgets> = gson.fromJson(jsonItems, listType)

        // Handle empty state
        if (items.isEmpty()) {
            emptyTransactionsMessage.visibility = View.VISIBLE
            recyclerView.visibility = View.GONE
        } else {
            emptyTransactionsMessage.visibility = View.GONE
            recyclerView.visibility = View.VISIBLE
            val item2Adapter = Item2Adapter(items)
            recyclerView.adapter = item2Adapter
        }
    }

    private fun trimDate(dateTime: String): String {
        return try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
            val outputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            outputFormat.format(inputFormat.parse(dateTime) ?: return dateTime)
        } catch (e: Exception) {
            e.printStackTrace()
            dateTime // Return the original if parsing fails
        }
    }

    private fun fetchAndSetOnline() {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("budget", null, user_id ?: "")
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(call: Call<List<Record>>, response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val items = getBudgets(response)
                    items?.let { budgetList ->
                        for (budget in budgetList) {
                            budget.setDate(trimDate(budget.getDate()))
                        }

                        // Handle empty state
                        if (budgetList.isEmpty()) {
                            emptyTransactionsMessage.visibility = View.VISIBLE
                            recyclerView.visibility = View.GONE
                        } else {
                            emptyTransactionsMessage.visibility = View.GONE
                            recyclerView.visibility = View.VISIBLE
                            val item2Adapter = Item2Adapter(budgetList)
                            recyclerView.adapter = item2Adapter
                        }
                    }
                    loadingSpinner.hide()
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(applicationContext, "Failed to fetch data", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<List<Record>>, t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(applicationContext, "Error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun getBudgets(response: Response<List<Record>>): List<Budgets>? {
        val records = response.body()
        val budgetsList = mutableListOf<Budgets>()
        records?.let {
            for (record in it) {
                if (record is Budgets) {
                    budgetsList.add(record)
                }
            }
        }
        val gson = Gson()
        val jsonItems = gson.toJson(budgetsList)
        val listType = object : TypeToken<List<Budgets>>() {}.type
        return gson.fromJson(jsonItems, listType)
    }

    private fun setupNavigationButtons() {
        val homeButton = findViewById<ImageButton>(R.id.button_home)
        val expensesButton = findViewById<ImageButton>(R.id.button_expenses)
        val transactionsButton = findViewById<ImageButton>(R.id.button_transactions)
        val profileButton = findViewById<ImageButton>(R.id.button_profile)
        val notificationButton = findViewById<ImageButton>(R.id.button_notification)

        homeButton.setOnClickListener { navigateTo(DashboardActivity::class.java) }
        expensesButton.setOnClickListener { navigateTo(ExpensesActivity::class.java) }
        transactionsButton.setOnClickListener {
            // Already in transactions activity, do nothing or refresh
        }
        profileButton.setOnClickListener { navigateTo(SettingsActivity::class.java) }
        notificationButton.setOnClickListener { navigateTo(NotificationActivity::class.java) }
    }

    private fun navigateTo(activityClass: Class<*>) {
        if (this.javaClass != activityClass) {
            val intent = Intent(this, activityClass)
            startActivity(intent)
        }
    }
}
