<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:id="@+id/root_layout">

    <!-- Modern Header -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:scaleType="fitCenter"
            android:tint="?attr/colorOnSurface" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="@string/add_a_new_expense"
            android:textColor="?attr/colorOnSurface"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_layout"
        android:layout_above="@id/footer"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Expense Form Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Expense Details"
                        android:textColor="?attr/colorOnSurface"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginBottom="24dp" />

                    <!-- Category Selection -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Category"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <Spinner
                            android:id="@+id/category_spinner"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:background="@drawable/spinner_background"
                            android:padding="16dp" />

                    </LinearLayout>

                    <!-- Note Input -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Description"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp">

                            <EditText
                                android:id="@+id/editText2"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="Enter expense description"
                                android:background="@android:color/transparent"
                                android:inputType="textMultiLine"
                                android:minLines="2"
                                android:maxLines="4"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Amount Input -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Amount"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="$"
                                android:textColor="?attr/colorOnSurface"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:layout_marginEnd="8dp" />

                            <EditText
                                android:id="@+id/editText4"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="0.00"
                                android:background="@android:color/transparent"
                                android:inputType="numberDecimal"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Date Selection -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="24dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Date"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/editText3"
                                android:layout_width="0dp"
                                android:layout_height="56dp"
                                android:layout_weight="1"
                                android:background="@drawable/edittext_background"
                                android:padding="16dp"
                                android:text="Select Date"
                                android:textColor="?attr/colorOnSurface"
                                android:textSize="16sp"
                                android:gravity="center_vertical" />

                            <Button
                                android:id="@+id/date_picker_button"
                                style="@style/SmallButton"
                                android:layout_marginStart="12dp"
                                android:text="@string/pick_date" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Save Button -->
                    <Button
                        android:id="@+id/save_button"
                        style="@style/MediumButton"
                        android:text="@string/add_expense" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- Bottom Navigation -->
    <include
        android:id="@+id/footer"
        layout="@layout/base"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

    <!-- Loading Spinner Overlay -->
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="@color/scrim_overlay">

        <include layout="@layout/spinner" />

    </RelativeLayout>

</RelativeLayout>
