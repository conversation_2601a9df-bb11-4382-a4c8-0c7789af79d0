package com.example.budgettracker

import androidx.annotation.NonNull

class Expenses(
    private var name: String,
    private var date: String,
    private var note: String, // Added field for 'note'
    private var amount: Int,
    user_id: String
) : Record(user_id) {

    // Getters and Setters
    fun getName(): String {
        return name
    }

    fun setName(name: String) {
        this.name = name
    }

    fun getDate(): String {
        return date
    }

    fun setDate(date: String) {
        this.date = date
    }

    fun getNote(): String {
        return note
    }

    fun setNote(note: String) {
        this.note = note
    }

    fun getAmount(): Int {
        return amount
    }

    fun setAmount(amount: Int) {
        this.amount = amount
    }

//    @NonNull
//    override fun toString(): String {
//        return "$name - $amount"
//    }
}
