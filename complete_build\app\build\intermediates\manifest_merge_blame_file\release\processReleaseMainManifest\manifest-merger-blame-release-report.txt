1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.budgettracker"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10    <!-- Permission to use biometric authentication -->
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:5:5-79
11-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
13-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:7:5-72
13-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:8:5-67
14-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:8:22-64
15
16    <!-- Feature declaration for fingerprint hardware -->
17    <uses-feature
17-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:5-90
18        android:name="android.hardware.fingerprint"
18-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:19-62
19        android:required="false" />
19-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:63-87
20
21    <!-- suppress DeprecatedClassUsageInspection -->
22    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
22-->[androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:5-74
22-->[androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:22-71
23    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
23-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:5-68
23-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:22-65
24    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
24-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
24-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
25    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
25-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:5-81
25-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:22-78
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:5-77
26-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:22-74
27    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
28
29    <permission
29-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:13:5-53:19
36        android:allowBackup="true"
36-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:14:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:15:9-65
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:16:9-54
41        android:icon="@mipmap/logo"
41-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:17:9-36
42        android:label="@string/app_name"
42-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:18:9-41
43        android:roundIcon="@mipmap/logo"
43-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:19:9-41
44        android:supportsRtl="true"
44-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:20:9-35
45        android:theme="@style/Base.Theme.BudgetTracker" >
45-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:21:9-56
46        <activity
46-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:23:9-32:20
47            android:name="com.example.budgettracker.MainActivity"
47-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:24:13-41
48            android:exported="true"
48-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:25:13-36
49            android:theme="@style/Base.Theme.BudgetTracker" >
49-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:26:13-60
50            <intent-filter>
50-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:27:13-31:29
51                <action android:name="android.intent.action.MAIN" />
51-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:28:17-69
51-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:28:25-66
52
53                <category android:name="android.intent.category.LAUNCHER" />
53-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:30:17-77
53-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:30:27-74
54            </intent-filter>
55        </activity>
56        <activity
56-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:33:9-35:39
57            android:name="com.example.budgettracker.DashboardActivity"
57-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:34:13-46
58            android:exported="true" />
58-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:35:13-36
59        <activity
59-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:9-78
60            android:name="com.example.budgettracker.ExpensesActivity"
60-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:19-51
61            android:exported="true" />
61-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:53-76
62        <activity
62-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:9-82
63            android:name="com.example.budgettracker.TransactionsActivity"
63-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:19-55
64            android:exported="true" />
64-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:57-80
65        <activity
65-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:9-77
66            android:name="com.example.budgettracker.ProfileActivity"
66-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:19-50
67            android:exported="true" />
67-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:52-75
68        <activity
68-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:9-78
69            android:name="com.example.budgettracker.SettingsActivity"
69-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:19-51
70            android:exported="true" />
70-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:53-76
71        <activity
71-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:9-78
72            android:name="com.example.budgettracker.LanguageActivity"
72-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:19-51
73            android:exported="true" />
73-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:53-76
74        <activity
74-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:9-82
75            android:name="com.example.budgettracker.NotificationActivity"
75-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:19-55
76            android:exported="true" />
76-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:57-80
77        <activity
77-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:9-78
78            android:name="com.example.budgettracker.RegisterActivity"
78-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:19-51
79            android:exported="true" />
79-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:53-76
80
81        <!-- Firebase Cloud Messaging Service -->
82        <service
82-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:46:9-52:19
83            android:name="com.example.budgettracker.MyFirebaseMessagingService"
83-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:47:13-55
84            android:exported="false" >
84-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:48:13-37
85            <intent-filter>
85-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:49:13-51:29
86                <action android:name="com.google.firebase.MESSAGING_EVENT" />
86-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:17-78
86-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:25-75
87            </intent-filter>
88        </service>
89
90        <activity
90-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
91            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
91-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
92            android:excludeFromRecents="true"
92-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
93            android:exported="true"
93-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
94            android:launchMode="singleTask"
94-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
95            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
95-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
96            <intent-filter>
96-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
97                <action android:name="android.intent.action.VIEW" />
97-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
97-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
99-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
100                <category android:name="android.intent.category.BROWSABLE" />
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
101
102                <data
102-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
103                    android:host="firebase.auth"
103-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
104                    android:path="/"
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
105                    android:scheme="genericidp" />
105-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
106            </intent-filter>
107        </activity>
108        <activity
108-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
109            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
110            android:excludeFromRecents="true"
110-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
111            android:exported="true"
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
112            android:launchMode="singleTask"
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
113            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
113-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
114            <intent-filter>
114-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
115                <action android:name="android.intent.action.VIEW" />
115-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
115-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
116
117                <category android:name="android.intent.category.DEFAULT" />
117-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
117-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
118                <category android:name="android.intent.category.BROWSABLE" />
118-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
118-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
119
120                <data
120-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
121                    android:host="firebase.auth"
121-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
122                    android:path="/"
122-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
123                    android:scheme="recaptcha" />
123-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
124            </intent-filter>
125        </activity>
126
127        <service
127-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
128            android:name="com.google.firebase.components.ComponentDiscoveryService"
128-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:67:13-84
129            android:directBootAware="true"
129-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
130            android:exported="false" >
130-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:68:13-37
131            <meta-data
131-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
132                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
132-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
134            <meta-data
134-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
135                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
135-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
137            <meta-data
137-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
138                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
138-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
140            <meta-data
140-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
141                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
141-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
143            <meta-data
143-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
144                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
144-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
146            <meta-data
146-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
147                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
147-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
149            <meta-data
149-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
150                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
150-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
152            <meta-data
152-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
153                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
153-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
155            <meta-data
155-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
156                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
156-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
158            <meta-data
158-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
159                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
159-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
161        </service>
162
163        <receiver
163-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
164            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
164-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
165            android:exported="true"
165-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
166            android:permission="com.google.android.c2dm.permission.SEND" >
166-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
167            <intent-filter>
167-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:33:13-35:29
168                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
168-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:17-81
168-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:25-78
169            </intent-filter>
170
171            <meta-data
171-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
172                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
172-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
173                android:value="true" />
173-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
174        </receiver>
175        <!--
176             FirebaseMessagingService performs security checks at runtime,
177             but set to not exported to explicitly avoid allowing another app to call it.
178        -->
179        <service
179-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
180            android:name="com.google.firebase.messaging.FirebaseMessagingService"
180-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
181            android:directBootAware="true"
181-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
182            android:exported="false" >
182-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
183            <intent-filter android:priority="-500" >
183-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:49:13-51:29
184                <action android:name="com.google.firebase.MESSAGING_EVENT" />
184-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:17-78
184-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:25-75
185            </intent-filter>
186        </service>
187
188        <provider
188-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
189            android:name="com.google.firebase.provider.FirebaseInitProvider"
189-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
190            android:authorities="com.example.budgettracker.firebaseinitprovider"
190-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
191            android:directBootAware="true"
191-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
192            android:exported="false"
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
193            android:initOrder="100" />
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
194        <provider
194-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:29:9-37:20
195            android:name="androidx.startup.InitializationProvider"
195-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:30:13-67
196            android:authorities="com.example.budgettracker.androidx-startup"
196-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:31:13-68
197            android:exported="false" >
197-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:32:13-37
198            <meta-data
198-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:34:13-36:52
199                android:name="androidx.work.WorkManagerInitializer"
199-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:35:17-68
200                android:value="androidx.startup" />
200-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:36:17-49
201            <meta-data
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
202                android:name="androidx.emoji2.text.EmojiCompatInitializer"
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
203                android:value="androidx.startup" />
203-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
204            <meta-data
204-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
205                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
205-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
206                android:value="androidx.startup" />
206-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
207            <meta-data
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
208                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
209                android:value="androidx.startup" />
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
210        </provider>
211
212        <service
212-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:39:9-45:35
213            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
213-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:40:13-88
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:41:13-44
215            android:enabled="@bool/enable_system_alarm_service_default"
215-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:42:13-72
216            android:exported="false" />
216-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:43:13-37
217        <service
217-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:46:9-52:35
218            android:name="androidx.work.impl.background.systemjob.SystemJobService"
218-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:47:13-84
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:48:13-44
220            android:enabled="@bool/enable_system_job_service_default"
220-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:49:13-70
221            android:exported="true"
221-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:50:13-36
222            android:permission="android.permission.BIND_JOB_SERVICE" />
222-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:51:13-69
223        <service
223-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:53:9-59:35
224            android:name="androidx.work.impl.foreground.SystemForegroundService"
224-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:54:13-81
225            android:directBootAware="false"
225-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:55:13-44
226            android:enabled="@bool/enable_system_foreground_service_default"
226-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:56:13-77
227            android:exported="false" />
227-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:57:13-37
228
229        <receiver
229-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:61:9-66:35
230            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
230-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:62:13-88
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:63:13-44
232            android:enabled="true"
232-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:64:13-35
233            android:exported="false" />
233-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:65:13-37
234        <receiver
234-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:67:9-77:20
235            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
235-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:68:13-106
236            android:directBootAware="false"
236-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:69:13-44
237            android:enabled="false"
237-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:70:13-36
238            android:exported="false" >
238-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:71:13-37
239            <intent-filter>
239-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:73:13-76:29
240                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
240-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:17-87
240-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:25-84
241                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
241-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:17-90
241-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:25-87
242            </intent-filter>
243        </receiver>
244        <receiver
244-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:78:9-88:20
245            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
245-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:79:13-104
246            android:directBootAware="false"
246-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:80:13-44
247            android:enabled="false"
247-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:81:13-36
248            android:exported="false" >
248-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:82:13-37
249            <intent-filter>
249-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:84:13-87:29
250                <action android:name="android.intent.action.BATTERY_OKAY" />
250-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:17-77
250-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:25-74
251                <action android:name="android.intent.action.BATTERY_LOW" />
251-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:17-76
251-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:25-73
252            </intent-filter>
253        </receiver>
254        <receiver
254-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:89:9-99:20
255            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
255-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:90:13-104
256            android:directBootAware="false"
256-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:91:13-44
257            android:enabled="false"
257-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:92:13-36
258            android:exported="false" >
258-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:93:13-37
259            <intent-filter>
259-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:95:13-98:29
260                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
260-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:17-83
260-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:25-80
261                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
261-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:17-82
261-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:25-79
262            </intent-filter>
263        </receiver>
264        <receiver
264-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:100:9-109:20
265            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
265-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:101:13-103
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:102:13-44
267            android:enabled="false"
267-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:103:13-36
268            android:exported="false" >
268-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:104:13-37
269            <intent-filter>
269-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:106:13-108:29
270                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
270-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:17-79
270-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:25-76
271            </intent-filter>
272        </receiver>
273        <receiver
273-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:110:9-121:20
274            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
274-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:111:13-88
275            android:directBootAware="false"
275-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:112:13-44
276            android:enabled="false"
276-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:113:13-36
277            android:exported="false" >
277-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:114:13-37
278            <intent-filter>
278-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:116:13-120:29
279                <action android:name="android.intent.action.BOOT_COMPLETED" />
279-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
279-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
280                <action android:name="android.intent.action.TIME_SET" />
280-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:17-73
280-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:25-70
281                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
281-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:17-81
281-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:25-78
282            </intent-filter>
283        </receiver>
284        <receiver
284-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:122:9-131:20
285            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
285-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:123:13-99
286            android:directBootAware="false"
286-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:124:13-44
287            android:enabled="@bool/enable_system_alarm_service_default"
287-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:125:13-72
288            android:exported="false" >
288-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:126:13-37
289            <intent-filter>
289-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:128:13-130:29
290                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
290-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:17-98
290-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:25-95
291            </intent-filter>
292        </receiver>
293        <receiver
293-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:132:9-142:20
294            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
294-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:133:13-78
295            android:directBootAware="false"
295-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:134:13-44
296            android:enabled="true"
296-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:135:13-35
297            android:exported="true"
297-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:136:13-36
298            android:permission="android.permission.DUMP" >
298-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:137:13-57
299            <intent-filter>
299-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:139:13-141:29
300                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
300-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:17-88
300-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:25-85
301            </intent-filter>
302        </receiver>
303
304        <service
304-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
305            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
305-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
306            android:enabled="true"
306-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
307            android:exported="false" >
307-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
308            <meta-data
308-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
309                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
309-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
310                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
310-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
311        </service>
312
313        <activity
313-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
314            android:name="androidx.credentials.playservices.HiddenActivity"
314-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
315            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
315-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
316            android:enabled="true"
316-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
317            android:exported="false"
317-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
318            android:fitsSystemWindows="true"
318-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
319            android:theme="@style/Theme.Hidden" >
319-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
320        </activity>
321        <activity
321-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
322            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
322-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
323            android:excludeFromRecents="true"
323-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
324            android:exported="false"
324-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
325            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
325-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
326        <!--
327            Service handling Google Sign-In user revocation. For apps that do not integrate with
328            Google Sign-In, this service will never be started.
329        -->
330        <service
330-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
331            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
331-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
332            android:exported="true"
332-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
333            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
333-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
334            android:visibleToInstantApps="true" />
334-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
335
336        <activity
336-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
337            android:name="com.google.android.gms.common.api.GoogleApiActivity"
337-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
338            android:exported="false"
338-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
339            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
339-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
340
341        <uses-library
341-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
342            android:name="androidx.window.extensions"
342-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
343            android:required="false" />
343-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
344        <uses-library
344-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
345            android:name="androidx.window.sidecar"
345-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
346            android:required="false" />
346-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
347
348        <meta-data
348-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
349            android:name="com.google.android.gms.version"
349-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
350            android:value="@integer/google_play_services_version" />
350-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
351
352        <receiver
352-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
353            android:name="androidx.profileinstaller.ProfileInstallReceiver"
353-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
354            android:directBootAware="false"
354-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
355            android:enabled="true"
355-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
356            android:exported="true"
356-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
357            android:permission="android.permission.DUMP" >
357-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
358            <intent-filter>
358-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
359                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
359-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
359-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
360            </intent-filter>
361            <intent-filter>
361-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
362                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
362-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
362-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
363            </intent-filter>
364            <intent-filter>
364-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
365                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
365-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
365-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
366            </intent-filter>
367            <intent-filter>
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
368                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
368-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
368-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
369            </intent-filter>
370        </receiver>
371
372        <service
372-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
373            android:name="androidx.room.MultiInstanceInvalidationService"
373-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
374            android:directBootAware="true"
374-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
375            android:exported="false" />
375-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
376        <service
376-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
377            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
377-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
378            android:exported="false" >
378-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
379            <meta-data
379-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
380                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
380-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
381                android:value="cct" />
381-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
382        </service>
383        <service
383-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
384            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
384-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
385            android:exported="false"
385-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
386            android:permission="android.permission.BIND_JOB_SERVICE" >
386-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
387        </service>
388
389        <receiver
389-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
390            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
390-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
391            android:exported="false" />
391-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
392    </application>
393
394</manifest>
