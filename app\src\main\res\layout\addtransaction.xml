<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:id="@+id/root_layout">

    <!-- Modern Header -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:scaleType="fitCenter"
            android:tint="?attr/colorOnSurface" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Add Transaction"
            android:textColor="?attr/colorOnSurface"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_layout"
        android:layout_above="@id/footer"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Budget Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:background="@drawable/gradient_primary_background">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Current Budget"
                        android:textColor="?attr/colorOnPrimary"
                        android:textSize="14sp"
                        android:fontFamily="sans-serif-medium"
                        android:alpha="0.87"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/budget_info"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="$0.00"
                        android:textColor="?attr/colorOnPrimary"
                        android:textSize="28sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Transaction Form Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Transaction Details"
                        android:textColor="?attr/colorOnSurface"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginBottom="24dp" />

                    <!-- Description Input -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Description"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp">

                            <EditText
                                android:id="@+id/editText5"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="Enter transaction description"
                                android:background="@android:color/transparent"
                                android:inputType="textMultiLine"
                                android:minLines="2"
                                android:maxLines="4"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Amount Input -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="24dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Amount"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="$"
                                android:textColor="?attr/colorOnSurface"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:layout_marginEnd="8dp" />

                            <EditText
                                android:id="@+id/editText4"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="0.00"
                                android:background="@android:color/transparent"
                                android:inputType="numberDecimal"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Save Button -->
                    <Button
                        android:id="@+id/save_button"
                        style="@style/MediumButton"
                        android:text="@string/add" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Recent Transactions Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="80dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header with icon and title -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/attach_money"
                            android:tint="?attr/colorPrimary"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Recent Transactions"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="View All"
                            android:textColor="?attr/colorPrimary"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:padding="8dp"
                            android:clickable="true"
                            android:focusable="true" />

                    </LinearLayout>

                    <!-- Empty state message -->
                    <TextView
                        android:id="@+id/empty_transactions_message"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="No recent transactions found.\nAdd your first transaction above!"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:textSize="14sp"
                        android:textAlignment="center"
                        android:padding="32dp"
                        android:visibility="gone"
                        android:drawableTop="@drawable/attach_money"
                        android:drawableTint="?attr/colorOnSurfaceVariant"
                        android:drawablePadding="16dp"
                        android:fontFamily="sans-serif" />

                    <!-- Transactions RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:clipToPadding="false"
                        android:paddingBottom="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>
    <include
        android:id="@+id/footer"
        layout="@layout/base"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

    <!-- Loading Spinner Overlay -->
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="@color/scrim_overlay">

        <include layout="@layout/spinner" />

    </RelativeLayout>

</RelativeLayout>
