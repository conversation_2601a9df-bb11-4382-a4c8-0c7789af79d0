# BudgetTracker Assets

This directory contains the final image assets and branding materials for the BudgetTracker application.

## 📁 Directory Structure

### `/images/`
Contains all the primary visual assets used in the application:

#### App Icons
- `ic_launcher.webp` - Main adaptive app icon (108x108dp)
- `ic_launcher_round.webp` - Round variant of the app icon

#### Logos & Branding
- `logo.png` - Primary BudgetTracker logo
- `logos.png` - Logo variations and branding elements
- `logo_hdpi.png` - High-density version of the primary logo
- `logos_hdpi.png` - High-density logo variations

## 🎨 Asset Specifications

### App Icons
- **Format**: WebP for optimal compression
- **Size**: Multiple densities (mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi)
- **Style**: Material Design adaptive icons
- **Background**: Transparent with proper safe zones

### Logos
- **Format**: PNG with transparency support
- **Usage**: Splash screens, about pages, branding
- **Variants**: Multiple sizes and orientations available

## 📱 Implementation

These assets are referenced throughout the Android application:

```xml
<!-- App icon in AndroidManifest.xml -->
<application android:icon="@mipmap/ic_launcher">

<!-- Logo in layouts -->
<ImageView android:src="@drawable/logo" />
```

## 🔧 Asset Guidelines

### For Developers
1. **Don't modify** original assets without design approval
2. **Use appropriate density** folders for different screen sizes
3. **Maintain aspect ratios** when scaling
4. **Test on multiple devices** to ensure proper display

### For Designers
1. **Follow Material Design** guidelines for icons
2. **Maintain brand consistency** across all assets
3. **Optimize file sizes** without compromising quality
4. **Provide multiple formats** when necessary

## 📋 Asset Checklist

- [x] App icon (adaptive)
- [x] App icon (round variant)
- [x] Primary logo
- [x] Logo variations
- [x] High-density versions
- [x] Proper file formats
- [x] Optimized file sizes
- [x] Multiple density support

## 🚀 Usage in App

These assets are actively used in:
- **App launcher** (ic_launcher.webp)
- **Login screen** (logo.png)
- **About page** (logos.png)
- **Splash screen** (logo variations)
- **Settings** (branding elements)

---

*Last updated: [Current Date]*
*Asset version: 1.0*
