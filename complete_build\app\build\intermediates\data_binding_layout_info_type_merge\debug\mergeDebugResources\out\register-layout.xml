<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="register" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\register.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/register_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="319" endOffset="16"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="307" startOffset="4" endLine="317" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="315" startOffset="8" endLine="315" endOffset="43"/></Target><Target id="@+id/header_layout" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="41" endOffset="18"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="49"/></Target><Target id="@+id/register_logo" view="ImageView"><Expressions/><location startLine="64" startOffset="16" endLine="70" endOffset="56"/></Target><Target id="@+id/username_container" view="LinearLayout"><Expressions/><location startLine="108" startOffset="20" endLine="143" endOffset="34"/></Target><Target id="@+id/username_input" view="EditText"><Expressions/><location startLine="130" startOffset="28" endLine="139" endOffset="57"/></Target><Target id="@+id/email_container" view="LinearLayout"><Expressions/><location startLine="146" startOffset="20" endLine="181" endOffset="34"/></Target><Target id="@+id/email_input" view="EditText"><Expressions/><location startLine="168" startOffset="28" endLine="177" endOffset="57"/></Target><Target id="@+id/password_container" view="LinearLayout"><Expressions/><location startLine="184" startOffset="20" endLine="230" endOffset="34"/></Target><Target id="@+id/password_input" view="EditText"><Expressions/><location startLine="207" startOffset="28" endLine="217" endOffset="57"/></Target><Target id="@+id/password_eye" view="ImageView"><Expressions/><location startLine="219" startOffset="28" endLine="226" endOffset="69"/></Target><Target id="@+id/password_container_confrim" view="LinearLayout"><Expressions/><location startLine="233" startOffset="20" endLine="279" endOffset="34"/></Target><Target id="@+id/password_input_confrim" view="EditText"><Expressions/><location startLine="256" startOffset="28" endLine="266" endOffset="57"/></Target><Target id="@+id/password_eye_confrim" view="ImageView"><Expressions/><location startLine="268" startOffset="28" endLine="275" endOffset="69"/></Target><Target id="@+id/signup_button" view="Button"><Expressions/><location startLine="282" startOffset="20" endLine="285" endOffset="55"/></Target></Targets></Layout>