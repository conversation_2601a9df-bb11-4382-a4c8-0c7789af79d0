package com.example.budgettracker

class ChartData(
    private var date: String,
    private var amount: Int
) {
    // Getters and Setters
    fun getDate(): String {
        return date
    }

    fun setDate(date: String) {
        this.date = date
    }

    fun getAmount(): Int {
        return amount
    }

    fun setAmount(amount: Int) {
        this.amount = amount
    }

    fun getXValue(): Int {
        return date.replace("/", "").toInt()
    }

    fun getYValue(): Double {
        return amount.toDouble()
    }
}
