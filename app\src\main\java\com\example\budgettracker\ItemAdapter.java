package com.example.budgettracker;

import android.animation.ObjectAnimator;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ItemAdapter extends RecyclerView.Adapter<ItemAdapter.ViewHolder> {

    private List<Expenses> expensesList;
    private Set<Integer> expandedItems = new HashSet<>();

    public ItemAdapter(List<Expenses> expensesList) {
        this.expensesList = expensesList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Expenses expense = expensesList.get(position);

        // Set basic data
        String name = expense.getName();
        holder.nameTextView.setText(name);
        holder.dateTextView.setText(expense.getDate());
        holder.amountTextView.setText(String.format("$%d", expense.getAmount()));

        // Set full description for expandable section
        String fullDescription = expense.getNote();
        if (fullDescription == null || fullDescription.trim().isEmpty()) {
            fullDescription = name; // Use name as fallback
        }
        holder.fullDescriptionTextView.setText(fullDescription);

        // Show expand button only if description is longer than name or has additional info
        boolean hasExpandableContent = !fullDescription.equals(name) || name.length() > 20;
        holder.expandButton.setVisibility(hasExpandableContent ? View.VISIBLE : View.GONE);

        // Handle expand/collapse state
        boolean isExpanded = expandedItems.contains(position);
        holder.expandableDetails.setVisibility(isExpanded ? View.VISIBLE : View.GONE);

        // Rotate expand button based on state
        float rotation = isExpanded ? 180f : 0f;
        holder.expandButton.setRotation(rotation);

        // Set click listeners
        setupClickListeners(holder, position, expense);

        // Add tooltip for truncated text
        if (name.length() > 20) {
            holder.nameTextView.setOnLongClickListener(v -> {
                Toast.makeText(v.getContext(), name, Toast.LENGTH_LONG).show();
                return true;
            });
        }
    }

    private void setupClickListeners(ViewHolder holder, int position, Expenses expense) {
        // Expand button click
        holder.expandButton.setOnClickListener(v -> {
            toggleExpansion(position, holder);
        });

        // Card click for expansion
        holder.itemView.setOnClickListener(v -> {
            if (holder.expandButton.getVisibility() == View.VISIBLE) {
                toggleExpansion(position, holder);
            }
        });

        // Long click for full details
        holder.itemView.setOnLongClickListener(v -> {
            String details = String.format("Item: %s\nAmount: $%d\nDate: %s\nNote: %s",
                expense.getName(),
                expense.getAmount(),
                expense.getDate(),
                expense.getNote() != null ? expense.getNote() : "No additional notes");
            Toast.makeText(v.getContext(), details, Toast.LENGTH_LONG).show();
            return true;
        });
    }

    private void toggleExpansion(int position, ViewHolder holder) {
        boolean isExpanded = expandedItems.contains(position);

        if (isExpanded) {
            expandedItems.remove(position);
            holder.expandableDetails.setVisibility(View.GONE);
            animateExpandButton(holder.expandButton, 0f);
        } else {
            expandedItems.add(position);
            holder.expandableDetails.setVisibility(View.VISIBLE);
            animateExpandButton(holder.expandButton, 180f);
        }
    }

    private void animateExpandButton(ImageButton button, float targetRotation) {
        ObjectAnimator animator = ObjectAnimator.ofFloat(button, "rotation", targetRotation);
        animator.setDuration(200);
        animator.start();
    }

    @Override
    public int getItemCount() {
        return expensesList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView nameTextView;
        public TextView dateTextView;
        public TextView amountTextView;
        public TextView fullDescriptionTextView;
        public TextView categoryTextView;
        public ImageButton expandButton;
        public View expandableDetails;

        public ViewHolder(View itemView) {
            super(itemView);
            nameTextView = itemView.findViewById(R.id.itemName);
            dateTextView = itemView.findViewById(R.id.itemDate);
            amountTextView = itemView.findViewById(R.id.itemAmount);
            fullDescriptionTextView = itemView.findViewById(R.id.itemFullDescription);
            categoryTextView = itemView.findViewById(R.id.itemCategory);
            expandButton = itemView.findViewById(R.id.expand_button);
            expandableDetails = itemView.findViewById(R.id.expandable_details);
        }
    }
}
