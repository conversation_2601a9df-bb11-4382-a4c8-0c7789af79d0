<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="incidents">

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (999 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_close.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="1024"
            endOffset="1314"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1875 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprint.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="1900"
            endOffset="2190"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/person.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="947"
            endOffset="1237"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1388 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="1413"
            endOffset="1703"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="29"
            column="13"
            startOffset="1146"
            endLine="29"
            endColumn="48"
            endOffset="1181"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Expense Details&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="76"
            column="25"
            startOffset="2918"
            endLine="76"
            endColumn="55"
            endOffset="2948"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Category&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="93"
            column="29"
            startOffset="3764"
            endLine="93"
            endColumn="52"
            endOffset="3787"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="118"
            column="29"
            startOffset="4956"
            endLine="118"
            endColumn="55"
            endOffset="4982"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="130"
            column="30"
            startOffset="5577"
            endLine="130"
            endColumn="38"
            endOffset="5585"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter expense description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="134"
            column="33"
            startOffset="5819"
            endLine="134"
            endColumn="73"
            endOffset="5859"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="157"
            column="29"
            startOffset="6927"
            endLine="157"
            endColumn="50"
            endOffset="6948"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;$&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="173"
            column="33"
            startOffset="7787"
            endLine="173"
            endColumn="49"
            endOffset="7803"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="179"
            column="30"
            startOffset="8092"
            endLine="179"
            endColumn="38"
            endOffset="8100"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0.00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="183"
            column="33"
            startOffset="8334"
            endLine="183"
            endColumn="52"
            endOffset="8353"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="205"
            column="29"
            startOffset="9373"
            endLine="205"
            endColumn="48"
            endOffset="9392"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Select Date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="224"
            column="33"
            startOffset="10404"
            endLine="224"
            endColumn="59"
            endOffset="10430"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="29"
            column="13"
            startOffset="1146"
            endLine="29"
            endColumn="48"
            endOffset="1181"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Transaction&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="36"
            column="13"
            startOffset="1395"
            endLine="36"
            endColumn="43"
            endOffset="1425"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Current Budget&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="76"
            column="25"
            startOffset="2947"
            endLine="76"
            endColumn="54"
            endOffset="2976"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;$0.00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="87"
            column="25"
            startOffset="3500"
            endLine="87"
            endColumn="45"
            endOffset="3520"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Transaction Details&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="114"
            column="25"
            startOffset="4599"
            endLine="114"
            endColumn="59"
            endOffset="4633"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="131"
            column="29"
            startOffset="5448"
            endLine="131"
            endColumn="55"
            endOffset="5474"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="143"
            column="30"
            startOffset="6069"
            endLine="143"
            endColumn="38"
            endOffset="6077"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter transaction description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="147"
            column="33"
            startOffset="6311"
            endLine="147"
            endColumn="77"
            endOffset="6355"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="170"
            column="29"
            startOffset="7423"
            endLine="170"
            endColumn="50"
            endOffset="7444"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;$&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="186"
            column="33"
            startOffset="8283"
            endLine="186"
            endColumn="49"
            endOffset="8299"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="192"
            column="30"
            startOffset="8588"
            endLine="192"
            endColumn="38"
            endOffset="8596"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0.00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="196"
            column="33"
            startOffset="8830"
            endLine="196"
            endColumn="52"
            endOffset="8849"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="240"
            column="26"
            startOffset="10684"
            endLine="240"
            endColumn="35"
            endOffset="10693"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorPrimary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorPrimary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorPrimary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="244"
            column="29"
            startOffset="10904"
            endLine="244"
            endColumn="62"
            endOffset="10937"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Recent Transactions&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="251"
            column="29"
            startOffset="11244"
            endLine="251"
            endColumn="63"
            endOffset="11278"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;View All&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="260"
            column="29"
            startOffset="11724"
            endLine="260"
            endColumn="52"
            endOffset="11747"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableTopCompat` instead of `android:drawableTop`">
        <fix-composite
            description="Set drawableTopCompat=&quot;@drawable/attach_money&quot; and Delete drawableTop">
            <fix-attribute
                description="Set drawableTopCompat=&quot;@drawable/attach_money&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableTopCompat"
                value="@drawable/attach_money"/>
            <fix-attribute
                description="Delete drawableTop"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableTop"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="282"
            column="25"
            startOffset="12898"
            endLine="282"
            endColumn="69"
            endOffset="12942"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableTint` instead of `android:drawableTint`">
        <fix-composite
            description="Set drawableTint=&quot;?attr/colorOnSurfaceVariant&quot; and Delete drawableTint">
            <fix-attribute
                description="Set drawableTint=&quot;?attr/colorOnSurfaceVariant&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableTint"
                value="?attr/colorOnSurfaceVariant"/>
            <fix-attribute
                description="Delete drawableTint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableTint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="283"
            column="25"
            startOffset="12968"
            endLine="283"
            endColumn="75"
            endOffset="13018"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No recent transactions found.\nAdd your first transaction above!&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="276"
            column="25"
            startOffset="12516"
            endLine="276"
            endColumn="104"
            endOffset="12595"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="21"
            column="9"
            startOffset="810"
            endLine="21"
            endColumn="44"
            endOffset="845"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="34"
            column="9"
            startOffset="1305"
            endLine="34"
            endColumn="44"
            endOffset="1340"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="47"
            column="9"
            startOffset="1807"
            endLine="47"
            endColumn="44"
            endOffset="1842"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="60"
            column="9"
            startOffset="2301"
            endLine="60"
            endColumn="44"
            endOffset="2336"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="73"
            column="9"
            startOffset="2789"
            endLine="73"
            endColumn="44"
            endOffset="2824"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="27"
            column="14"
            startOffset="1011"
            endLine="27"
            endColumn="23"
            endOffset="1020"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Spending vs Target:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="140"
            column="29"
            startOffset="5780"
            endLine="140"
            endColumn="63"
            endOffset="5814"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0% of target spent&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="150"
            column="29"
            startOffset="6314"
            endLine="150"
            endColumn="62"
            endOffset="6347"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Balance vs Budget:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="169"
            column="29"
            startOffset="7211"
            endLine="169"
            endColumn="62"
            endOffset="7244"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0% remaining&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="179"
            column="29"
            startOffset="7743"
            endLine="179"
            endColumn="56"
            endOffset="7770"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Total Spent:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="198"
            column="29"
            startOffset="8634"
            endLine="198"
            endColumn="56"
            endOffset="8661"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;$0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="208"
            column="29"
            startOffset="9160"
            endLine="208"
            endColumn="46"
            endOffset="9177"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Spending Overview&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="237"
            column="25"
            startOffset="10304"
            endLine="237"
            endColumn="57"
            endOffset="10336"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="10985"
                    endOffset="15033"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="11258"
                    endOffset="11981"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12009"
                    endOffset="12732"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12760"
                    endOffset="13492"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="13520"
                    endOffset="14243"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="14271"
                    endOffset="14994"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="257"
            column="26"
            startOffset="11259"
            endLine="257"
            endColumn="32"
            endOffset="11265"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="268"
            column="29"
            startOffset="11902"
            endLine="268"
            endColumn="51"
            endOffset="11924"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="10985"
                    endOffset="15033"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="11258"
                    endOffset="11981"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12009"
                    endOffset="12732"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12760"
                    endOffset="13492"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="13520"
                    endOffset="14243"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="14271"
                    endOffset="14994"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="271"
            column="26"
            startOffset="12010"
            endLine="271"
            endColumn="32"
            endOffset="12016"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="278"
            column="29"
            startOffset="12389"
            endLine="278"
            endColumn="51"
            endOffset="12411"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="10985"
                    endOffset="15033"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="11258"
                    endOffset="11981"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12009"
                    endOffset="12732"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12760"
                    endOffset="13492"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="13520"
                    endOffset="14243"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="14271"
                    endOffset="14994"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="285"
            column="26"
            startOffset="12761"
            endLine="285"
            endColumn="32"
            endOffset="12767"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="292"
            column="29"
            startOffset="13140"
            endLine="292"
            endColumn="51"
            endOffset="13162"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="10985"
                    endOffset="15033"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="11258"
                    endOffset="11981"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12009"
                    endOffset="12732"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12760"
                    endOffset="13492"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="13520"
                    endOffset="14243"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="14271"
                    endOffset="14994"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="299"
            column="26"
            startOffset="13521"
            endLine="299"
            endColumn="32"
            endOffset="13527"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="306"
            column="29"
            startOffset="13900"
            endLine="306"
            endColumn="51"
            endOffset="13922"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="10985"
                    endOffset="15033"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="11258"
                    endOffset="11981"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12009"
                    endOffset="12732"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="12760"
                    endOffset="13492"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="13520"
                    endOffset="14243"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
                    startOffset="14271"
                    endOffset="14994"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="313"
            column="26"
            startOffset="14272"
            endLine="313"
            endColumn="32"
            endOffset="14278"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="320"
            column="29"
            startOffset="14651"
            endLine="320"
            endColumn="51"
            endOffset="14673"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="375"
            column="29"
            startOffset="17202"
            endLine="375"
            endColumn="51"
            endOffset="17224"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Expense&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="419"
            column="9"
            startOffset="18708"
            endLine="419"
            endColumn="35"
            endOffset="18734"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Expense&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="440"
            column="9"
            startOffset="19561"
            endLine="440"
            endColumn="49"
            endOffset="19601"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Transaction&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="458"
            column="9"
            startOffset="20271"
            endLine="458"
            endColumn="39"
            endOffset="20301"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Budget&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="479"
            column="9"
            startOffset="21131"
            endLine="479"
            endColumn="48"
            endOffset="21170"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add New Entry&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="496"
            column="9"
            startOffset="21840"
            endLine="496"
            endColumn="51"
            endOffset="21882"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="10"
            column="6"
            startOffset="385"
            endLine="10"
            endColumn="18"
            endOffset="397"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;@color/white&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;@color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="31"
            column="17"
            startOffset="1238"
            endLine="31"
            endColumn="44"
            endOffset="1265"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Notification icon&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="29"
            column="17"
            startOffset="1119"
            endLine="29"
            endColumn="63"
            endOffset="1165"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Notification Title&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="45"
            column="17"
            startOffset="1698"
            endLine="45"
            endColumn="50"
            endOffset="1731"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Notification message content&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="56"
            column="17"
            startOffset="2188"
            endLine="56"
            endColumn="60"
            endOffset="2231"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Just now&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="67"
            column="17"
            startOffset="2657"
            endLine="67"
            endColumn="40"
            endOffset="2680"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_bug.xml"
            line="14"
            column="6"
            startOffset="546"
            endLine="14"
            endColumn="18"
            endOffset="558"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_bug.xml"
            line="29"
            column="14"
            startOffset="1083"
            endLine="29"
            endColumn="23"
            endOffset="1092"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSecondaryContainer&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSecondaryContainer&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSecondaryContainer"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_bug.xml"
            line="34"
            column="17"
            startOffset="1300"
            endLine="34"
            endColumn="63"
            endOffset="1346"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Budget&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_bug.xml"
            line="94"
            column="17"
            startOffset="3584"
            endLine="94"
            endColumn="38"
            endOffset="3605"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_bug.xml"
            line="95"
            column="17"
            startOffset="3623"
            endLine="95"
            endColumn="40"
            endOffset="3646"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item.xml"
            line="28"
            column="14"
            startOffset="1018"
            endLine="28"
            endColumn="23"
            endOffset="1027"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorPrimary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorPrimary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorPrimary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item.xml"
            line="36"
            column="17"
            startOffset="1385"
            endLine="36"
            endColumn="50"
            endOffset="1418"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;View more details&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item.xml"
            line="90"
            column="17"
            startOffset="3666"
            endLine="90"
            endColumn="63"
            endOffset="3712"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Full description will appear here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item.xml"
            line="115"
            column="17"
            startOffset="4615"
            endLine="115"
            endColumn="65"
            endOffset="4663"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Category&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item.xml"
            line="125"
            column="17"
            startOffset="5069"
            endLine="125"
            endColumn="40"
            endOffset="5092"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/login.xml"
            line="47"
            column="10"
            startOffset="1750"
            endLine="47"
            endColumn="18"
            endOffset="1758"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/login.xml"
            line="71"
            column="10"
            startOffset="2666"
            endLine="71"
            endColumn="18"
            endOffset="2674"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/login.xml"
            line="90"
            column="13"
            startOffset="3477"
            endLine="90"
            endColumn="48"
            endOffset="3512"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorPrimary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorPrimary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorPrimary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/login.xml"
            line="135"
            column="13"
            startOffset="5115"
            endLine="135"
            endColumn="46"
            endOffset="5148"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notifications.xml"
            line="83"
            column="18"
            startOffset="3206"
            endLine="83"
            endColumn="27"
            endOffset="3215"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No notifications yet&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notifications.xml"
            line="93"
            column="21"
            startOffset="3639"
            endLine="93"
            endColumn="56"
            endOffset="3674"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;You&apos;ll see important updates about your budget here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notifications.xml"
            line="102"
            column="21"
            startOffset="4051"
            endLine="102"
            endColumn="87"
            endOffset="4117"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="29"
            column="13"
            startOffset="1146"
            endLine="29"
            endColumn="48"
            endOffset="1181"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Create Account&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="36"
            column="13"
            startOffset="1395"
            endLine="36"
            endColumn="42"
            endOffset="1424"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="65"
            column="18"
            startOffset="2399"
            endLine="65"
            endColumn="27"
            endOffset="2408"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Join Budget Tracker&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="76"
            column="21"
            startOffset="2886"
            endLine="76"
            endColumn="55"
            endOffset="2920"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Create your account to start managing your finances&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="86"
            column="21"
            startOffset="3357"
            endLine="86"
            endColumn="87"
            endOffset="3423"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Username&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="119"
            column="29"
            startOffset="4819"
            endLine="119"
            endColumn="52"
            endOffset="4842"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="131"
            column="30"
            startOffset="5437"
            endLine="131"
            endColumn="38"
            endOffset="5445"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your username&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="135"
            column="33"
            startOffset="5684"
            endLine="135"
            endColumn="67"
            endOffset="5718"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email Address&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="157"
            column="29"
            startOffset="6727"
            endLine="157"
            endColumn="57"
            endOffset="6755"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="169"
            column="30"
            startOffset="7350"
            endLine="169"
            endColumn="38"
            endOffset="7358"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your email&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="173"
            column="33"
            startOffset="7594"
            endLine="173"
            endColumn="64"
            endOffset="7625"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Password&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="195"
            column="29"
            startOffset="8652"
            endLine="195"
            endColumn="52"
            endOffset="8675"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="208"
            column="30"
            startOffset="9332"
            endLine="208"
            endColumn="38"
            endOffset="9340"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter your password&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="213"
            column="33"
            startOffset="9629"
            endLine="213"
            endColumn="67"
            endOffset="9663"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="227"
            column="33"
            startOffset="10514"
            endLine="227"
            endColumn="68"
            endOffset="10549"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Toggle password visibility&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="225"
            column="33"
            startOffset="10359"
            endLine="225"
            endColumn="88"
            endOffset="10414"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Confirm Password&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="244"
            column="29"
            startOffset="11232"
            endLine="244"
            endColumn="60"
            endOffset="11263"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="257"
            column="30"
            startOffset="11920"
            endLine="257"
            endColumn="38"
            endOffset="11928"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Confirm your password&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="262"
            column="33"
            startOffset="12225"
            endLine="262"
            endColumn="69"
            endOffset="12261"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="276"
            column="33"
            startOffset="13120"
            endLine="276"
            endColumn="68"
            endOffset="13155"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Toggle password visibility&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="274"
            column="33"
            startOffset="12965"
            endLine="274"
            endColumn="88"
            endOffset="13020"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Create Account&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="286"
            column="25"
            startOffset="13452"
            endLine="286"
            endColumn="54"
            endOffset="13481"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;By creating an account, you agree to our Terms of Service and Privacy Policy&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="296"
            column="17"
            startOffset="13761"
            endLine="296"
            endColumn="108"
            endOffset="13852"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;?attr/colorOnSurface&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;?attr/colorOnSurface&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="?attr/colorOnSurface"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="29"
            column="13"
            startOffset="1146"
            endLine="29"
            endColumn="48"
            endOffset="1181"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Budget Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="76"
            column="25"
            startOffset="2909"
            endLine="76"
            endColumn="55"
            endOffset="2939"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Monthly Budget&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="93"
            column="29"
            startOffset="3750"
            endLine="93"
            endColumn="58"
            endOffset="3779"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="105"
            column="30"
            startOffset="4368"
            endLine="105"
            endColumn="38"
            endOffset="4376"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter budget amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="110"
            column="33"
            startOffset="4660"
            endLine="110"
            endColumn="67"
            endOffset="4694"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Target Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="131"
            column="29"
            startOffset="5655"
            endLine="131"
            endColumn="57"
            endOffset="5683"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="143"
            column="30"
            startOffset="6272"
            endLine="143"
            endColumn="38"
            endOffset="6280"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter target amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="148"
            column="33"
            startOffset="6564"
            endLine="148"
            endColumn="67"
            endOffset="6598"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Budget Period&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="176"
            column="33"
            startOffset="7913"
            endLine="176"
            endColumn="61"
            endOffset="7941"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Select Date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="187"
            column="33"
            startOffset="8558"
            endLine="187"
            endColumn="59"
            endOffset="8584"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pick Date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="197"
            column="29"
            startOffset="9011"
            endLine="197"
            endColumn="53"
            endOffset="9035"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Save Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="205"
            column="25"
            startOffset="9284"
            endLine="205"
            endColumn="53"
            endOffset="9312"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;App Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="228"
            column="25"
            startOffset="10165"
            endLine="228"
            endColumn="52"
            endOffset="10192"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Current Budget Summary&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="268"
            column="25"
            startOffset="11910"
            endLine="268"
            endColumn="62"
            endOffset="11947"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Balance:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="285"
            column="29"
            startOffset="12761"
            endLine="285"
            endColumn="52"
            endOffset="12784"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;$0.00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="293"
            column="29"
            startOffset="13169"
            endLine="293"
            endColumn="49"
            endOffset="13189"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Target:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="310"
            column="29"
            startOffset="13928"
            endLine="310"
            endColumn="51"
            endOffset="13950"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;$0.00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="318"
            column="29"
            startOffset="14334"
            endLine="318"
            endColumn="49"
            endOffset="14354"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Budget:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="334"
            column="29"
            startOffset="15036"
            endLine="334"
            endColumn="51"
            endOffset="15058"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;$0.00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="342"
            column="29"
            startOffset="15442"
            endLine="342"
            endColumn="49"
            endOffset="15462"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/adapters/NotificationAdapter.kt"
            line="84"
            column="9"
            startOffset="3447"
            endLine="84"
            endColumn="31"
            endOffset="3469"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/DashboardActivity.kt"
            line="526"
            column="21"
            startOffset="21679"
            endLine="526"
            endColumn="50"
            endOffset="21708"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/DashboardActivity.kt"
            line="526"
            column="21"
            startOffset="21679"
            endLine="526"
            endColumn="50"
            endOffset="21708"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/ExpensesActivity.kt"
            line="170"
            column="29"
            startOffset="7399"
            endLine="170"
            endColumn="109"
            endOffset="7479"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/ExpensesActivity.kt"
            line="197"
            column="21"
            startOffset="8827"
            endLine="197"
            endColumn="99"
            endOffset="8905"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/ExpensesActivity.kt"
            line="226"
            column="17"
            startOffset="10028"
            endLine="226"
            endColumn="61"
            endOffset="10072"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/ExpensesActivity.kt"
            line="261"
            column="17"
            startOffset="11506"
            endLine="261"
            endColumn="61"
            endOffset="11550"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/FirebaseManager.kt"
            line="469"
            column="27"
            startOffset="17218"
            endLine="469"
            endColumn="115"
            endOffset="17306"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/FirebaseManager.kt"
            line="473"
            column="27"
            startOffset="17427"
            endLine="473"
            endColumn="110"
            endOffset="17510"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/FirebaseManager.kt"
            line="498"
            column="23"
            startOffset="18363"
            endLine="498"
            endColumn="94"
            endOffset="18434"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/FirebaseManager.kt"
            line="507"
            column="23"
            startOffset="18739"
            endLine="507"
            endColumn="86"
            endOffset="18802"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/Item2Adapter.kt"
            line="35"
            column="38"
            startOffset="1165"
            endLine="35"
            endColumn="79"
            endOffset="1206"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/Item2Adapter.kt"
            line="35"
            column="38"
            startOffset="1165"
            endLine="35"
            endColumn="79"
            endOffset="1206"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/Item2Adapter.kt"
            line="39"
            column="27"
            startOffset="1335"
            endLine="42"
            endColumn="14"
            endOffset="1477"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/Item2Adapter.kt"
            line="48"
            column="32"
            startOffset="1697"
            endLine="51"
            endColumn="14"
            endOffset="1885"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/ItemAdapter.kt"
            line="29"
            column="38"
            startOffset="1012"
            endLine="29"
            endColumn="79"
            endOffset="1053"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/ItemAdapter.kt"
            line="29"
            column="38"
            startOffset="1012"
            endLine="29"
            endColumn="79"
            endOffset="1053"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/ItemAdapter.kt"
            line="77"
            column="27"
            startOffset="2861"
            endLine="83"
            endColumn="14"
            endOffset="3116"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="196"
            column="21"
            startOffset="7750"
            endLine="196"
            endColumn="105"
            endOffset="7834"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="221"
            column="25"
            startOffset="8811"
            endLine="221"
            endColumn="107"
            endOffset="8893"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="228"
            column="25"
            startOffset="9194"
            endLine="228"
            endColumn="103"
            endOffset="9272"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="264"
            column="17"
            startOffset="10740"
            endLine="264"
            endColumn="45"
            endOffset="10768"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="287"
            column="17"
            startOffset="11668"
            endLine="287"
            endColumn="45"
            endOffset="11696"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="356"
            column="29"
            startOffset="14033"
            endLine="356"
            endColumn="111"
            endOffset="14115"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="363"
            column="29"
            startOffset="14444"
            endLine="363"
            endColumn="107"
            endOffset="14522"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="383"
            column="29"
            startOffset="15382"
            endLine="383"
            endColumn="113"
            endOffset="15466"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="420"
            column="29"
            startOffset="17064"
            endLine="420"
            endColumn="73"
            endOffset="17108"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="459"
            column="29"
            startOffset="18731"
            endLine="459"
            endColumn="73"
            endOffset="18775"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationActivity.kt"
            line="590"
            column="17"
            startOffset="24334"
            endLine="590"
            endColumn="108"
            endOffset="24425"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationHelper.kt"
            line="59"
            column="17"
            startOffset="2549"
            endLine="59"
            endColumn="63"
            endOffset="2595"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationWorker.kt"
            line="101"
            column="23"
            startOffset="3668"
            endLine="101"
            endColumn="97"
            endOffset="3742"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationWorker.kt"
            line="118"
            column="23"
            startOffset="4443"
            endLine="118"
            endColumn="103"
            endOffset="4523"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/NotificationWorker.kt"
            line="134"
            column="23"
            startOffset="5169"
            endLine="134"
            endColumn="107"
            endOffset="5253"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/SettingsActivity.kt"
            line="160"
            column="40"
            startOffset="6749"
            endLine="160"
            endColumn="46"
            endOffset="6755"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/SettingsActivity.kt"
            line="160"
            column="41"
            startOffset="6750"
            endLine="160"
            endColumn="45"
            endOffset="6754"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/SettingsActivity.kt"
            line="161"
            column="39"
            startOffset="6794"
            endLine="161"
            endColumn="45"
            endOffset="6800"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/SettingsActivity.kt"
            line="161"
            column="40"
            startOffset="6795"
            endLine="161"
            endColumn="44"
            endOffset="6799"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/SettingsActivity.kt"
            line="162"
            column="39"
            startOffset="6839"
            endLine="162"
            endColumn="45"
            endOffset="6845"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/budgettracker/SettingsActivity.kt"
            line="162"
            column="40"
            startOffset="6840"
            endLine="162"
            endColumn="44"
            endOffset="6844"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="14"
            column="9"
            startOffset="338"
            endLine="14"
            endColumn="23"
            endOffset="352"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for kotlin-stdlib"
            robot="true">
            <fix-replace
                description="Replace with kotlinStdlib = &quot;1.9.10&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinStdlib = &quot;1.9.10&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="162"
                    endOffset="162"/>
            </fix-replace>
            <fix-replace
                description="Replace with kotlin-stdlib = { module = &quot;org.jetbrains.kotlin:kotlin-stdlib&quot;, version.ref = &quot;kotlinStdlib&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlin-stdlib = { module = &quot;org.jetbrains.kotlin:kotlin-stdlib&quot;, version.ref = &quot;kotlinStdlib&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="1057"
                    endOffset="1057"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.kotlin.stdlib"
                robot="true"
                replacement="libs.kotlin.stdlib"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1084"
                    endOffset="1127"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="47"
            column="20"
            startOffset="1084"
            endLine="47"
            endColumn="63"
            endOffset="1127"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.google.firebase:firebase-firestore is already available as `firebase-firestore`, but using version 25.1.4 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for google-firebase-firestore"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with google-firebase-firestore = { module = &quot;com.google.firebase:firebase-firestore&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="google-firebase-firestore = { module = &quot;com.google.firebase:firebase-firestore&quot; }&#xA;"
                    priority="0">
                    <range
                        file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                        startOffset="697"
                        endOffset="697"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.google.firebase.firestore"
                    robot="true"
                    independent="true"
                    replacement="libs.google.firebase.firestore"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="1471"
                        endOffset="1511"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `firebase-firestore` (version 25.1.4)"
                replacement="libs.firebase.firestore"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1471"
                    endOffset="1511"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="56"
            column="20"
            startOffset="1471"
            endLine="56"
            endColumn="60"
            endOffset="1511"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.google.firebase:firebase-messaging is already available as `firebase-messaging`, but using version 24.1.1 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for google-firebase-messaging"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with google-firebase-messaging = { module = &quot;com.google.firebase:firebase-messaging&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="google-firebase-messaging = { module = &quot;com.google.firebase:firebase-messaging&quot; }&#xA;"
                    priority="0">
                    <range
                        file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                        startOffset="697"
                        endOffset="697"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.google.firebase.messaging"
                    robot="true"
                    independent="true"
                    replacement="libs.google.firebase.messaging"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="1532"
                        endOffset="1572"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `firebase-messaging` (version 24.1.1)"
                replacement="libs.firebase.messaging"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1532"
                    endOffset="1572"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="57"
            column="20"
            startOffset="1532"
            endLine="57"
            endColumn="60"
            endOffset="1572"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofit = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="451"
                    endOffset="451"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofit&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofit&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="2330"
                    endOffset="2330"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.retrofit"
                robot="true"
                replacement="libs.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1797"
                    endOffset="1836"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="63"
            column="20"
            startOffset="1797"
            endLine="63"
            endColumn="59"
            endOffset="1836"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-converter-gson"
            robot="true">
            <fix-replace
                description="Replace with converterGson = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converterGson = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="27"
                    endOffset="27"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGson&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGson&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="2330"
                    endOffset="2330"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.converter.gson"
                robot="true"
                replacement="libs.retrofit2.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1857"
                    endOffset="1902"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="64"
            column="20"
            startOffset="1857"
            endLine="64"
            endColumn="65"
            endOffset="1902"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for graphview"
            robot="true">
            <fix-replace
                description="Replace with graphview = &quot;4.2.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="graphview = &quot;4.2.2&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="27"
                    endOffset="27"/>
            </fix-replace>
            <fix-replace
                description="Replace with graphview = { module = &quot;com.jjoe64:graphview&quot;, version.ref = &quot;graphview&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="graphview = { module = &quot;com.jjoe64:graphview&quot;, version.ref = &quot;graphview&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="697"
                    endOffset="697"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.graphview"
                robot="true"
                replacement="libs.graphview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1923"
                    endOffset="1951"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="65"
            column="20"
            startOffset="1923"
            endLine="65"
            endColumn="48"
            endOffset="1951"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for gson"
            robot="true">
            <fix-replace
                description="Replace with gson = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gson = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="27"
                    endOffset="27"/>
            </fix-replace>
            <fix-replace
                description="Replace with gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gson&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gson&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="697"
                    endOffset="697"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.gson"
                robot="true"
                replacement="libs.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1973"
                    endOffset="2007"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="66"
            column="21"
            startOffset="1973"
            endLine="66"
            endColumn="55"
            endOffset="2007"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.google.firebase:firebase-bom is already available as `firebase-bom`, but using version 32.1.2 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for firebase-bom-v3312"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with firebaseBomVersion = &quot;33.1.2&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="firebaseBomVersion = &quot;33.1.2&quot;&#xA;"
                    priority="0">
                    <range
                        file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                        startOffset="27"
                        endOffset="27"/>
                </fix-replace>
                <fix-replace
                    description="Replace with firebase-bom-v3312 = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBomVersion&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="firebase-bom-v3312 = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBomVersion&quot; }&#xA;"
                    priority="0">
                    <range
                        file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                        startOffset="697"
                        endOffset="697"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.firebase.bom.v3312"
                    robot="true"
                    independent="true"
                    replacement="libs.firebase.bom.v3312"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="2028"
                        endOffset="2069"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `firebase-bom` (version 32.1.2)"
                replacement="libs.firebase.bom"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2028"
                    endOffset="2069"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="67"
            column="20"
            startOffset="2028"
            endLine="67"
            endColumn="61"
            endOffset="2069"/>
    </incident>

    <incident
        id="BomWithoutPlatform"
        severity="warning"
        message="BOM should be added with a call to platform()">
        <fix-replace
            description="Add platform() to BOM declaration"
            family="Add platform() to BOM declaration"
            oldString="&quot;com.google.firebase:firebase-bom:33.1.2&quot;"
            replacement="platform(&quot;com.google.firebase:firebase-bom:33.1.2&quot;)"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="67"
            column="20"
            startOffset="2028"
            endLine="67"
            endColumn="61"
            endOffset="2069"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for work-runtime"
            robot="true">
            <fix-replace
                description="Replace with workRuntime = &quot;2.8.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="workRuntime = &quot;2.8.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="451"
                    endOffset="451"/>
            </fix-replace>
            <fix-replace
                description="Replace with work-runtime = { module = &quot;androidx.work:work-runtime&quot;, version.ref = &quot;workRuntime&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="work-runtime = { module = &quot;androidx.work:work-runtime&quot;, version.ref = &quot;workRuntime&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="2330"
                    endOffset="2330"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.work.runtime"
                robot="true"
                replacement="libs.work.runtime"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2091"
                    endOffset="2125"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="68"
            column="21"
            startOffset="2091"
            endLine="68"
            endColumn="55"
            endOffset="2125"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for guava"
            robot="true">
            <fix-replace
                description="Replace with guava = &quot;32.0.1-android&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="guava = &quot;32.0.1-android&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="27"
                    endOffset="27"/>
            </fix-replace>
            <fix-replace
                description="Replace with guava = { module = &quot;com.google.guava:guava&quot;, version.ref = &quot;guava&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="guava = { module = &quot;com.google.guava:guava&quot;, version.ref = &quot;guava&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="697"
                    endOffset="697"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.guava"
                robot="true"
                replacement="libs.guava"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2146"
                    endOffset="2185"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="69"
            column="20"
            startOffset="2146"
            endLine="69"
            endColumn="59"
            endOffset="2185"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="108"
            endLine="6"
            endColumn="23"
            endOffset="115"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="132"
            endLine="7"
            endColumn="23"
            endOffset="139"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="8"
            column="13"
            startOffset="153"
            endLine="8"
            endColumn="20"
            endOffset="160"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.10.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="173"
            endLine="9"
            endColumn="20"
            endOffset="181"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="10"
            column="20"
            startOffset="202"
            endLine="10"
            endColumn="27"
            endOffset="209"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="12"
            column="22"
            startOffset="258"
            endLine="12"
            endColumn="29"
            endOffset="265"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="13"
            column="16"
            startOffset="282"
            endLine="13"
            endColumn="23"
            endOffset="289"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0">
        <fix-replace
            description="Change to 33.15.0"
            family="Update versions"
            oldString="32.1.2"
            replacement="33.15.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="14"
            column="15"
            startOffset="305"
            endLine="14"
            endColumn="23"
            endOffset="313"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1">
        <fix-replace
            description="Change to 23.2.1"
            family="Update versions"
            oldString="23.0.0"
            replacement="23.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="16"
            column="16"
            startOffset="357"
            endLine="16"
            endColumn="24"
            endOffset="365"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.1"
                family="Update versions"
                oldString="8.5.1"
                replacement="8.10.1"
                priority="0"/>
            <fix-replace
                description="Change to 8.5.2"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.5.1"
                replacement="8.5.2"
                priority="0"/>
        </fix-alternatives>
        <location
            file="$HOME/Desktop/New folder/BudgetTracker/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="18"
            endLine="2"
            endColumn="14"
            endOffset="25"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin:kotlin-stdlib than 1.9.10 is available: 1.9.22">
        <fix-replace
            description="Change to 1.9.22"
            family="Update versions"
            oldString="1.9.10"
            replacement="1.9.22"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="47"
            column="20"
            startOffset="1084"
            endLine="47"
            endColumn="63"
            endOffset="1127"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 33.1.2 is available: 33.15.0">
        <fix-replace
            description="Change to 33.15.0"
            family="Update versions"
            oldString="33.1.2"
            replacement="33.15.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="67"
            column="20"
            startOffset="2028"
            endLine="67"
            endColumn="61"
            endOffset="2069"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime than 2.8.0 is available: 2.10.1">
        <fix-replace
            description="Change to 2.10.1"
            family="Update versions"
            oldString="2.8.0"
            replacement="2.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="68"
            column="21"
            startOffset="2091"
            endLine="68"
            endColumn="55"
            endOffset="2125"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logos.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logos.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/logo.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/logo.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/logo.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/logo.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/logo.png"/>
    </incident>

    <incident
        id="IconDipSize"
        severity="warning"
        message="The image `logo.png` varies significantly in its density-independent (dip) size across the various density versions: mipmap-hdpi/logo.png: 397x279 dp (595x419 px), mipmap-mdpi/logo.png: 595x419 dp (595x419 px), mipmap-xhdpi/logo.png: 298x210 dp (595x419 px), mipmap-xxhdpi/logo.png: 198x140 dp (595x419 px), mipmap-xxxhdpi/logo.png: 149x105 dp (595x419 px)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/logo.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: logo.png, logos.png, logo.png, logo.png, logo.png, logo.png, logo.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/logo.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logos.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.png"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="44"
            endOffset="351"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="44"
            endOffset="351"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="7"
            column="5"
            startOffset="296"
            endLine="7"
            endColumn="44"
            endOffset="335"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="44"
            endOffset="351"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/language.xml"
            line="5"
            column="5"
            startOffset="203"
            endLine="5"
            endColumn="38"
            endOffset="236"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base_Theme_BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/login.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="44"
            endOffset="351"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notifications.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="44"
            endOffset="351"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="44"
            endOffset="351"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/colorSurface` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetHero`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="8"
            column="5"
            startOffset="347"
            endLine="8"
            endColumn="44"
            endOffset="386"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;budget_info&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2913"
            endLine="55"
            endColumn="31"
            endOffset="2931"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;zulu&quot; is not translated in &quot;af&quot; (Afrikaans) or &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="66"
            column="13"
            startOffset="3500"
            endLine="66"
            endColumn="24"
            endOffset="3511"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;name_bug&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="67"
            column="13"
            startOffset="3539"
            endLine="67"
            endColumn="28"
            endOffset="3554"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;bug_amount&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="68"
            column="13"
            startOffset="3582"
            endLine="68"
            endColumn="30"
            endOffset="3599"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;exp_amount&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="69"
            column="13"
            startOffset="3629"
            endLine="69"
            endColumn="30"
            endOffset="3646"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;ext_name&quot; is not translated in &quot;af&quot; (Afrikaans) or &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="13"
            startOffset="3676"
            endLine="70"
            endColumn="28"
            endOffset="3691"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;exp_date&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="71"
            column="13"
            startOffset="3719"
            endLine="71"
            endColumn="28"
            endOffset="3734"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;date_x&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="3766"
            endLine="72"
            endColumn="26"
            endOffset="3779"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;main_cash&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="73"
            column="13"
            startOffset="3807"
            endLine="73"
            endColumn="29"
            endOffset="3823"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;_0&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="74"
            column="13"
            startOffset="3859"
            endLine="74"
            endColumn="22"
            endOffset="3868"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;month&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="75"
            column="13"
            startOffset="3894"
            endLine="75"
            endColumn="25"
            endOffset="3906"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;pick_date&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="76"
            column="13"
            startOffset="3940"
            endLine="76"
            endColumn="29"
            endOffset="3956"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;budget&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="77"
            column="13"
            startOffset="3989"
            endLine="77"
            endColumn="26"
            endOffset="4002"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;target&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="4032"
            endLine="78"
            endColumn="26"
            endOffset="4045"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;balance&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="79"
            column="13"
            startOffset="4075"
            endLine="79"
            endColumn="27"
            endOffset="4089"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;set_new_target&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="4120"
            endLine="80"
            endColumn="34"
            endOffset="4141"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;set&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="81"
            column="13"
            startOffset="4177"
            endLine="81"
            endColumn="23"
            endOffset="4187"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;sign_up&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="82"
            column="13"
            startOffset="4214"
            endLine="82"
            endColumn="27"
            endOffset="4228"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;add_expense&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="83"
            column="13"
            startOffset="4259"
            endLine="83"
            endColumn="31"
            endOffset="4277"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;view_all&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="84"
            column="13"
            startOffset="4312"
            endLine="84"
            endColumn="28"
            endOffset="4327"/>
    </incident>

    <incident
        id="DuplicateIncludedIds"
        severity="warning"
        message="Duplicate id @+id/button_notification, defined or included multiple times in layout/dashboard.xml: [layout/dashboard.xml defines @+id/button_notification, layout/dashboard.xml => layout/base.xml defines @+id/button_notification]">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="402"
            column="5"
            startOffset="18058"
            endLine="407"
            endColumn="51"
            endOffset="18274"
            message="Duplicate id @+id/button_notification, defined or included multiple times in layout/dashboard.xml: [layout/dashboard.xml defines @+id/button_notification, layout/dashboard.xml => layout/base.xml defines @+id/button_notification]"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="48"
            column="13"
            startOffset="1794"
            endLine="48"
            endColumn="50"
            endOffset="1831"
            message="Defined here"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="52"
            column="9"
            startOffset="1937"
            endLine="52"
            endColumn="46"
            endOffset="1974"
            message="Defined here, included via layout/dashboard.xml => layout/base.xml defines @+id/button_notification"/>
    </incident>

</incidents>
