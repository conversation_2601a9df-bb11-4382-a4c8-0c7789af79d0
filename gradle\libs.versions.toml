[versions]
agp = "8.5.1"
kotlin = "1.9.10"
biometric = "1.2.0-alpha05"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"
appcompat = "1.6.1"
material = "1.10.0"
constraintlayout = "2.1.4"
mpandroidchart = "3.1.0"
navigationFragment = "2.6.0"
navigationUi = "2.6.0"
firebaseBom = "32.1.2"
firebaseCommon = "21.0.0"
firebaseAuth = "23.0.0"
contentpager = "1.0.0"
firebaseFirestore = "25.1.4"
firebaseMessaging = "24.1.1"

[libraries]
biometric = { module = "androidx.biometric:biometric", version.ref = "biometric" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
mpandroidchart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "mpandroidchart" }
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment", version.ref = "navigationFragment" }
navigation-ui = { group = "androidx.navigation", name = "navigation-ui", version.ref = "navigationUi" }


firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
retrofit = { module = "com.squareup.retrofit2:retrofit" }
firebase-common = { group = "com.google.firebase", name = "firebase-common", version.ref = "firebaseCommon" }
firebase-auth = { group = "com.google.firebase", name = "firebase-auth", version.ref = "firebaseAuth" }
contentpager = { group = "androidx.contentpager", name = "contentpager", version.ref = "contentpager" }
firebase-firestore = { group = "com.google.firebase", name = "firebase-firestore", version.ref = "firebaseFirestore" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging", version.ref = "firebaseMessaging" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

