<libraries>
  <library
      name="androidx.databinding:viewbinding:8.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\87daeb89e7006dad3b2d70792f7dc363\transformed\jetified-viewbinding-8.5.1\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\87daeb89e7006dad3b2d70792f7dc363\transformed\jetified-viewbinding-8.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8cbcc578d44f3eb74db94fab2c6b909f\transformed\navigation-common-2.6.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8cbcc578d44f3eb74db94fab2c6b909f\transformed\navigation-common-2.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\34a0dba344bab218dae460ae09545d83\transformed\navigation-runtime-2.6.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\34a0dba344bab218dae460ae09545d83\transformed\navigation-runtime-2.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui:2.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\transformed\navigation-ui-2.6.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui:2.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\transformed\navigation-ui-2.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment:2.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8faebcb1c3a5e3e4cbf4eebb68087685\transformed\navigation-fragment-2.6.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment:2.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8faebcb1c3a5e3e4cbf4eebb68087685\transformed\navigation-fragment-2.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:23.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:23.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c377da76ffd722c11f7e2a995dabf1f9\transformed\material-1.10.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.10.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c377da76ffd722c11f7e2a995dabf1f9\transformed\material-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8fe6d8e6447df98388c2372dd5db4cda\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8fe6d8e6447df98388c2372dd5db4cda\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.contentpager:contentpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4f71eadc1b8e90646e5ad874b5903c79\transformed\contentpager-1.0.0\jars\classes.jar"
      resolved="androidx.contentpager:contentpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4f71eadc1b8e90646e5ad874b5903c79\transformed\contentpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.2.0-alpha05@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.2.0-alpha05"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ecc0e1a6fb1bf17d44fb5bd4b082ceae\transformed\jetified-viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ecc0e1a6fb1bf17d44fb5bd4b082ceae\transformed\jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4909f730a9e7806222bd84df864833cf\transformed\jetified-fragment-ktx-1.6.0\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4909f730a9e7806222bd84df864833cf\transformed\jetified-fragment-ktx-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6f8046b9f6ebb729ea86cd38f19e22c2\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6f8046b9f6ebb729ea86cd38f19e22c2\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore:25.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:25.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c31e469962acb98785db8c0d2a661168\transformed\jetified-firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c31e469962acb98785db8c0d2a661168\transformed\jetified-firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\505f45660c762064795deae2c2e196b4\transformed\jetified-appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\505f45660c762064795deae2c2e196b4\transformed\jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.jjoe64:graphview:4.2.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9772ea6e98fb513fde374d6102af9be0\transformed\jetified-graphview-4.2.2\jars\classes.jar"
      resolved="com.jjoe64:graphview:4.2.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9772ea6e98fb513fde374d6102af9be0\transformed\jetified-graphview-4.2.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b1e820dea617353e0956c65165b54c8b\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b1e820dea617353e0956c65165b54c8b\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ea784ed1012bd601aa8faf8751b2d9b8\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ea784ed1012bd601aa8faf8751b2d9b8\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8ef09979244db1040cf88eeabe9be1cb\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8ef09979244db1040cf88eeabe9be1cb\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\51674a575b03a3f252817171cb48cb1e\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\51674a575b03a3f252817171cb48cb1e\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2a3b35c4940c1c2d53fd392389e37fd7\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2a3b35c4940c1c2d53fd392389e37fd7\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f45219147905cc69ca8d23ceb240d1c0\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f45219147905cc69ca8d23ceb240d1c0\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\baa02ee2b336dd28d751a84382ff3f88\transformed\transition-1.4.1\jars\classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\baa02ee2b336dd28d751a84382ff3f88\transformed\transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c390d111e0296cb277f7457d019cd712\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c390d111e0296cb277f7457d019cd712\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\db4986a1ffdc5cf11da22a86fc7d5e8c\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\db4986a1ffdc5cf11da22a86fc7d5e8c\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ba365e17b0c92088e8b876778529c00e\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ba365e17b0c92088e8b876778529c00e\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ac93c7f5e634f2bb5a4c72004cb6aa8f\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ac93c7f5e634f2bb5a4c72004cb6aa8f\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\048055b1c844a0d4657ea5603727f4a7\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\048055b1c844a0d4657ea5603727f4a7\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7ac808904cd7e03e33d69da647d3102c\transformed\browser-1.4.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7ac808904cd7e03e33d69da647d3102c\transformed\browser-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5b487c08a660904bdeeea450e14c67b6\transformed\jetified-activity-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5b487c08a660904bdeeea450e14c67b6\transformed\jetified-activity-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\73d2850eb1f9692bf7f675809cde6554\transformed\jetified-core-ktx-1.9.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\73d2850eb1f9692bf7f675809cde6554\transformed\jetified-core-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0d81c1d2dadc3ad1df6d9d5ca28e1dad\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0d81c1d2dadc3ad1df6d9d5ca28e1dad\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b54f3439f91d18bcb99cb17e6de28ef4\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b54f3439f91d18bcb99cb17e6de28ef4\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9d6040b0339e72d27b1a9f3cbc100b37\transformed\jetified-firebase-appcheck-interop-17.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9d6040b0339e72d27b1a9f3cbc100b37\transformed\jetified-firebase-appcheck-interop-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\78f8bd9f21c9c9e350b0ae9463cb6254\transformed\jetified-firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\78f8bd9f21c9c9e350b0ae9463cb6254\transformed\jetified-firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\414810306ab42cdbd7aeac81ddbd0d14\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\414810306ab42cdbd7aeac81ddbd0d14\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\180ea994d7df7386e51a57698ad3d488\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\180ea994d7df7386e51a57698ad3d488\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ba1416b4e6ca07f65a70639c28a97d1c\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ba1416b4e6ca07f65a70639c28a97d1c\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\jars\classes.jar"
      resolved="androidx.core:core:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9a0131a85ed38d09f0f70b41b154b388\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9a0131a85ed38d09f0f70b41b154b388\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e60b9f8b65ff5d0d3690c967a8fc459b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e60b9f8b65ff5d0d3690c967a8fc459b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1e4d68544ef3b568b5636125eb743808\transformed\jetified-lifecycle-runtime-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1e4d68544ef3b568b5636125eb743808\transformed\jetified-lifecycle-runtime-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.1\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.1"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\433a447b623505e7bfb53d51b85c55a2\transformed\lifecycle-livedata-core-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\433a447b623505e7bfb53d51b85c55a2\transformed\lifecycle-livedata-core-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c1617a116917964ea202899a142ae129\transformed\lifecycle-viewmodel-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c1617a116917964ea202899a142ae129\transformed\lifecycle-viewmodel-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9bae62f918c79a50a2afe899642c7b83\transformed\lifecycle-livedata-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9bae62f918c79a50a2afe899642c7b83\transformed\lifecycle-livedata-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4c23147b0932287a7a28448a50f1785d\transformed\lifecycle-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4c23147b0932287a7a28448a50f1785d\transformed\lifecycle-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\58db176b82f12f7c23b8b67cf9baa82c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\58db176b82f12f7c23b8b67cf9baa82c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.android.play:integrity:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f7fbf9f8dcec6439dc9000f6ca3bc4a6\transformed\jetified-integrity-1.2.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f7fbf9f8dcec6439dc9000f6ca3bc4a6\transformed\jetified-integrity-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\743dfef3b5f1b030098eeeb08afd7f3a\transformed\jetified-firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\743dfef3b5f1b030098eeeb08afd7f3a\transformed\jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1b07acaf295c6d81572c63d8d6b44936\transformed\jetified-firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1b07acaf295c6d81572c63d8d6b44936\transformed\jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8eb7245843a35542f1c46cc4bcc038ae\transformed\jetified-play-services-tasks-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8eb7245843a35542f1c46cc4bcc038ae\transformed\jetified-play-services-tasks-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9faddc12b4375d81ca9618be2077d4eb\transformed\jetified-firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9faddc12b4375d81ca9618be2077d4eb\transformed\jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a5c7cd714341ca1665b7498b28d2d66b\transformed\fragment-1.6.0\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a5c7cd714341ca1665b7498b28d2d66b\transformed\fragment-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a2e564b0fdf56bef138528d62a64ff42\transformed\jetified-activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a2e564b0fdf56bef138528d62a64ff42\transformed\jetified-activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\76241a0f367aa123de6300cf3b364aa4\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\76241a0f367aa123de6300cf3b364aa4\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9b29633514210382aa5f3cfd66de980e\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9b29633514210382aa5f3cfd66de980e\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7e720598323011a970a0e06f1d41965f\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7e720598323011a970a0e06f1d41965f\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\3cd0824ea124b81ca8f547ab361f990c\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\3cd0824ea124b81ca8f547ab361f990c\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6f8cdd8f36058e9a21e710b4a674385d\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6f8cdd8f36058e9a21e710b4a674385d\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1c641e390a7be6a0bccc310f791f99a5\transformed\jetified-credentials-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1c641e390a7be6a0bccc310f791f99a5\transformed\jetified-credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\820e46c87cf5b0c2078bc5e577f87474\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\820e46c87cf5b0c2078bc5e577f87474\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\3d0a96145352cfe522dbaa8a6897ab93\transformed\jetified-firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\3d0a96145352cfe522dbaa8a6897ab93\transformed\jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4656a9a6a353c65596a09f3aa7c3e6b8\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4656a9a6a353c65596a09f3aa7c3e6b8\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c87ccc46a42e4b91a08431e11d609b1f\transformed\jetified-transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c87ccc46a42e4b91a08431e11d609b1f\transformed\jetified-transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\3944c43952836de719334faa2ffab8dc\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\3944c43952836de719334faa2ffab8dc\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\6b63ab947e57e165706b4eb22843c474\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\6b63ab947e57e165706b4eb22843c474\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\45393922257e8062269c4be3f99a3548\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\45393922257e8062269c4be3f99a3548\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation\1.5.0\857678d6b4ca7b28571ef7935c668bdb57e15027\annotation-1.5.0.jar"
      resolved="androidx.annotation:annotation:1.5.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a94674afbe5a03fba673e15c1a34395c\transformed\jetified-annotation-experimental-1.3.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a94674afbe5a03fba673e15c1a34395c\transformed\jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.10\72812e8a368917ab5c0a5081b56915ffdfec93b7\kotlin-stdlib-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.10"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4fbad6584b625f6cbde5b424d0ce76e0\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4fbad6584b625f6cbde5b424d0ce76e0\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="com.google.guava:guava:32.0.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\32.0.1-android\d3fe54a75aeaca33fc562a2a9c1dc181e54cc5e9\guava-32.0.1-android.jar"
      resolved="com.google.guava:guava:32.0.1-android"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.9.10\dafaf2c27f27c09220cee312df10917d9a5d97ce\kotlin-stdlib-common-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\972fbbbbddcb6b5d4c6e1f7bf21bdacf\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\972fbbbbddcb6b5d4c6e1f7bf21bdacf\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\658f655ad56fdec7e516531202060d4b\transformed\jetified-protolite-well-known-types-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\658f655ad56fdec7e516531202060d4b\transformed\jetified-protolite-well-known-types-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.22.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.22.3\7df7200bce0ff77dc66dd063df79ff74c6114bfc\protobuf-javalite-3.22.3.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.22.3"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="org.checkerframework:checker-qual:3.33.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.33.0\de2b60b62da487644fc11f734e73c8b0b431238f\checker-qual-3.33.0.jar"
      resolved="org.checkerframework:checker-qual:3.33.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:2.8@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\2.8\c85270e307e7b822f1086b93689124b89768e273\j2objc-annotations-2.8.jar"
      resolved="com.google.j2objc:j2objc-annotations:2.8"/>
  <library
      name="com.squareup.okhttp3:okhttp:3.14.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\3.14.9\3e6d101343c7ea687cd593e4990f73b25c878383\okhttp-3.14.9.jar"
      resolved="com.squareup.okhttp3:okhttp:3.14.9"/>
  <library
      name="com.squareup.okio:okio:1.17.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\1.17.5\34336f82f14dde1c0752fd5f0546dbf3c3225aba\okio-1.17.5.jar"
      resolved="com.squareup.okio:okio:1.17.5"/>
  <library
      name="com.google.android.gms:play-services-auth:20.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:20.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\da59337531a415863d35241726a5e12a\transformed\jetified-play-services-fido-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\da59337531a415863d35241726a5e12a\transformed\jetified-play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4eee9fd69195cf7da3f5523c2ce9c927\transformed\jetified-play-services-auth-base-18.0.4\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4eee9fd69195cf7da3f5523c2ce9c927\transformed\jetified-play-services-auth-base-18.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4d3d90c764242f30fac5fefd7ddbf3ef\transformed\jetified-emoji2-views-helper-1.2.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4d3d90c764242f30fac5fefd7ddbf3ef\transformed\jetified-emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a5b690fe5611e1aebf99a0fd20c574b4\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a5b690fe5611e1aebf99a0fd20c574b4\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\jars\classes.jar"
      resolved="androidx.window:window:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\89e596ebf43b1dbbbb42cb44a2aa0f1e\transformed\jetified-lifecycle-service-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\89e596ebf43b1dbbbb42cb44a2aa0f1e\transformed\jetified-lifecycle-service-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.6.1\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.6.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.6.1"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1a1227758cacd92d107d7b31106cf4cf\transformed\sqlite-framework-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1a1227758cacd92d107d7b31106cf4cf\transformed\sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4dcfd1e981820e4c55bf21c2bfd2941a\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4dcfd1e981820e4c55bf21c2bfd2941a\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.0\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\55f326db8455ef50bf388165c0a22c79\transformed\sqlite-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\55f326db8455ef50bf388165c0a22c79\transformed\sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4784fee769f25faf6fa32e7878ad8538\transformed\jetified-googleid-1.1.0\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4784fee769f25faf6fa32e7878ad8538\transformed\jetified-googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-android:1.57.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4420005cd9cc8ccbfa28427f44f2a9c2\transformed\jetified-grpc-android-1.57.2\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.57.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4420005cd9cc8ccbfa28427f44f2a9c2\transformed\jetified-grpc-android-1.57.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.57.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.57.2\6dbccffc5531af73b739205f8a24d7e77a0e51fc\grpc-okhttp-1.57.2.jar"
      resolved="io.grpc:grpc-okhttp:1.57.2"/>
  <library
      name="io.grpc:grpc-core:1.57.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-core\1.57.2\2e04aac0fe659243f4ed5dbe4c2839e88408dffc\grpc-core-1.57.2.jar"
      resolved="io.grpc:grpc-core:1.57.2"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.57.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.57.2\14d8e0cab40bf2f661e07b8d0dcc4192d4e9e85a\grpc-protobuf-lite-1.57.2.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.57.2"/>
  <library
      name="io.grpc:grpc-stub:1.57.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.57.2\df47b4eb69fb36940689649400c8e9cbf7c3dc2c\grpc-stub-1.57.2.jar"
      resolved="io.grpc:grpc-stub:1.57.2"/>
  <library
      name="io.grpc:grpc-context:1.57.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-context\1.57.2\d2aa34eebfec2ce045873f410a7dd76437465509\grpc-context-1.57.2.jar"
      resolved="io.grpc:grpc-context:1.57.2"/>
  <library
      name="io.grpc:grpc-api:1.57.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-api\1.57.2\c71a006b81ddae7bc4b7cb1d2da78c1b173761f4\grpc-api-1.57.2.jar"
      resolved="io.grpc:grpc-api:1.57.2"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.0.4\29cdbe03ded6b0980f63fa5da2579a430e911c40\constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="io.perfmark:perfmark-api:0.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.26.0\ef65452adaf20bf7d12ef55913aba24037b82738\perfmark-api-0.26.0.jar"
      resolved="io.perfmark:perfmark-api:0.26.0"/>
  <library
      name="com.google.android:annotations:4.1.1.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.android\annotations\4.1.1.4\a1678ba907bf92691d879fef34e1a187038f9259\annotations-4.1.1.4.jar"
      resolved="com.google.android:annotations:4.1.1.4"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.23\3c0daebd5f0e1ce72cc50c818321ac957aeb5d70\animal-sniffer-annotations-1.23.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.23"/>
</libraries>
