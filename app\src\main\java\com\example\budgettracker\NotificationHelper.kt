package com.example.budgettracker

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat

object NotificationHelper {

    private const val CHANNEL_ID = "budget_notifications_channel"

    fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name: CharSequence = "Budget Notifications"
            val description = "Channel for budget notifications"
            val importance = NotificationManager.IMPORTANCE_HIGH  // High importance for pop-ups
            val channel = NotificationChannel(CHANNEL_ID, name, importance)
            channel.description = description
            channel.enableLights(true)  // Enable LED light
            channel.enableVibration(true)  // Enable vibration
            channel.setShowBadge(true)  // Show badge on app icon
            channel.lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC  // Show on lock screen

            val notificationManager = context.getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    fun sendNotification(context: Context, title: String, content: String) {
        // Check for notification permission on Android 13+ (API 33+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ActivityCompat.checkSelfPermission(context, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                // Permission not granted, log and return
                android.util.Log.w("NotificationHelper", "Notification permission not granted")
                return
            }
        }

        // Create notification channel if needed
        createNotificationChannel(context)

        // Create intent for when notification is tapped
        val intent = Intent(context, DashboardActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK

        @SuppressLint("UnspecifiedImmutableFlag")
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            else
                PendingIntent.FLAG_UPDATE_CURRENT
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.notifications)
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_HIGH)  // High priority for pop-up
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setDefaults(NotificationCompat.DEFAULT_ALL)  // Sound, vibration, lights
            .setStyle(NotificationCompat.BigTextStyle().bigText(content))
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)  // Show on lock screen
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)  // Message category for pop-up
            .setFullScreenIntent(pendingIntent, false)  // Helps with pop-up display

        val notificationManager = NotificationManagerCompat.from(context)

        try {
            notificationManager.notify(System.currentTimeMillis().toInt(), builder.build())
            android.util.Log.d("NotificationHelper", "Notification sent: $title")
        } catch (e: SecurityException) {
            android.util.Log.e("NotificationHelper", "Failed to send notification: ${e.message}")
        }
    }

    // Test method to send a test notification
    fun sendTestNotification(context: Context) {
        sendNotification(context, "🎉 Test Notification", "If you can see this, notifications are working perfectly!")
    }
}
