package com.example.budgettracker

import android.content.Context
import android.content.SharedPreferences
import android.preference.PreferenceManager
import android.util.Log

class SessionManager(context: Context) {
    companion object {
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_USER_EMAIL = "user_email"
        private const val KEY_USER_NAME = "user_name"
        private const val KEY_USER_UID = "user_uid" // Add UID key
    }

    private val sharedPreferences: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(context)
    private val editor: SharedPreferences.Editor = sharedPreferences.edit()

    fun createLoginSession(email: String, userName: String, uid: String) {
        editor.putBoolean(KEY_IS_LOGGED_IN, true)
        editor.putString(KEY_USER_EMAIL, email)
        editor.putString(KEY_USER_NAME, userName)
        editor.putString(KEY_USER_UID, uid)
        editor.apply()
    }

    fun isLoggedIn(): <PERSON><PERSON><PERSON> {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    fun logoutUser() {
        editor.clear()
        editor.apply()
    }

    fun getUserEmail(): String? {
        return sharedPreferences.getString(KEY_USER_EMAIL, null)
    }

    fun getUserName(): String? {
        return sharedPreferences.getString(KEY_USER_NAME, null)
    }

    fun getUserUid(): String? {
        return sharedPreferences.getString(KEY_USER_UID, null)
    } // Retrieve UID from session

    fun getAllSessionDetails(): Map<String, String?> {
        val sessionDetails = HashMap<String, String?>()
        sessionDetails["isLoggedIn"] = isLoggedIn().toString()
        sessionDetails["email"] = getUserEmail()
        sessionDetails["userName"] = getUserName()
        sessionDetails["uid"] = getUserUid()
        return sessionDetails
    }
}
