1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.budgettracker"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10    <!-- Permission to use biometric authentication -->
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:5:5-79
11-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
13-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:7:5-72
13-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:8:5-67
14-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:8:22-64
15
16    <!-- Feature declaration for fingerprint hardware -->
17    <uses-feature
17-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:5-90
18        android:name="android.hardware.fingerprint"
18-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:19-62
19        android:required="false" />
19-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:63-87
20
21    <!-- suppress DeprecatedClassUsageInspection -->
22    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
22-->[androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:5-74
22-->[androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:22-71
23    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
23-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:5-68
23-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:22-65
24    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
24-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
24-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
25    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
25-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:5-81
25-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:22-78
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:5-77
26-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:22-74
27    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
28
29    <permission
29-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:13:5-53:19
36        android:allowBackup="true"
36-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:14:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:15:9-65
39        android:debuggable="true"
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:16:9-54
42        android:icon="@mipmap/logo"
42-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:17:9-36
43        android:label="@string/app_name"
43-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:18:9-41
44        android:roundIcon="@mipmap/logo"
44-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:19:9-41
45        android:supportsRtl="true"
45-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:20:9-35
46        android:theme="@style/Base.Theme.BudgetHero" >
46-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:21:9-53
47        <activity
47-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:23:9-32:20
48            android:name="com.example.budgettracker.MainActivity"
48-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:24:13-41
49            android:exported="true"
49-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:25:13-36
50            android:theme="@style/Base.Theme.BudgetHero" >
50-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:26:13-57
51            <intent-filter>
51-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:27:13-31:29
52                <action android:name="android.intent.action.MAIN" />
52-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:28:17-69
52-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:28:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:30:17-77
54-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:30:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:33:9-35:39
58            android:name="com.example.budgettracker.DashboardActivity"
58-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:34:13-46
59            android:exported="true" />
59-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:35:13-36
60        <activity
60-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:9-78
61            android:name="com.example.budgettracker.ExpensesActivity"
61-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:19-51
62            android:exported="true" />
62-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:53-76
63        <activity
63-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:9-82
64            android:name="com.example.budgettracker.TransactionsActivity"
64-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:19-55
65            android:exported="true" />
65-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:57-80
66        <activity
66-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:9-77
67            android:name="com.example.budgettracker.ProfileActivity"
67-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:19-50
68            android:exported="true" />
68-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:52-75
69        <activity
69-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:9-78
70            android:name="com.example.budgettracker.SettingsActivity"
70-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:19-51
71            android:exported="true" />
71-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:53-76
72        <activity
72-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:9-78
73            android:name="com.example.budgettracker.LanguageActivity"
73-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:19-51
74            android:exported="true" />
74-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:53-76
75        <activity
75-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:9-82
76            android:name="com.example.budgettracker.NotificationActivity"
76-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:19-55
77            android:exported="true" />
77-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:57-80
78        <activity
78-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:9-78
79            android:name="com.example.budgettracker.RegisterActivity"
79-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:19-51
80            android:exported="true" />
80-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:53-76
81
82        <!-- Firebase Cloud Messaging Service -->
83        <service
83-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:46:9-52:19
84            android:name="com.example.budgettracker.MyFirebaseMessagingService"
84-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:47:13-55
85            android:exported="false" >
85-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:48:13-37
86            <intent-filter>
86-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:49:13-51:29
87                <action android:name="com.google.firebase.MESSAGING_EVENT" />
87-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:17-78
87-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:25-75
88            </intent-filter>
89        </service>
90
91        <activity
91-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
92            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
92-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
93            android:excludeFromRecents="true"
93-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
94            android:exported="true"
94-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
95            android:launchMode="singleTask"
95-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
96            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
96-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
97            <intent-filter>
97-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
98                <action android:name="android.intent.action.VIEW" />
98-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
98-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
99
100                <category android:name="android.intent.category.DEFAULT" />
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
101                <category android:name="android.intent.category.BROWSABLE" />
101-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
101-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
102
103                <data
103-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
104                    android:host="firebase.auth"
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
105                    android:path="/"
105-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
106                    android:scheme="genericidp" />
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
107            </intent-filter>
108        </activity>
109        <activity
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
110            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
110-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
111            android:excludeFromRecents="true"
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
112            android:exported="true"
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
113            android:launchMode="singleTask"
113-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
114            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
114-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
115            <intent-filter>
115-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
116                <action android:name="android.intent.action.VIEW" />
116-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
116-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
117
118                <category android:name="android.intent.category.DEFAULT" />
118-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
118-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
119                <category android:name="android.intent.category.BROWSABLE" />
119-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
119-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
120
121                <data
121-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
122                    android:host="firebase.auth"
122-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
123                    android:path="/"
123-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
124                    android:scheme="recaptcha" />
124-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
125            </intent-filter>
126        </activity>
127
128        <service
128-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
129            android:name="com.google.firebase.components.ComponentDiscoveryService"
129-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:67:13-84
130            android:directBootAware="true"
130-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
131            android:exported="false" >
131-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:68:13-37
132            <meta-data
132-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
133                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
133-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
135            <meta-data
135-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
136                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
136-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
138            <meta-data
138-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
139                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
139-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
141            <meta-data
141-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
142                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
142-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
144            <meta-data
144-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
145                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
145-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
147            <meta-data
147-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
148                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
148-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
150            <meta-data
150-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
151                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
153            <meta-data
153-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
154                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
154-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
156            <meta-data
156-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
157                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
157-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
159            <meta-data
159-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
160                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
160-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
162        </service>
163
164        <receiver
164-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
165            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
165-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
166            android:exported="true"
166-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
167            android:permission="com.google.android.c2dm.permission.SEND" >
167-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
168            <intent-filter>
168-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:33:13-35:29
169                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
169-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:17-81
169-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:25-78
170            </intent-filter>
171
172            <meta-data
172-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
173                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
173-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
174                android:value="true" />
174-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
175        </receiver>
176        <!--
177             FirebaseMessagingService performs security checks at runtime,
178             but set to not exported to explicitly avoid allowing another app to call it.
179        -->
180        <service
180-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
181            android:name="com.google.firebase.messaging.FirebaseMessagingService"
181-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
182            android:directBootAware="true"
182-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
183            android:exported="false" >
183-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
184            <intent-filter android:priority="-500" >
184-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:49:13-51:29
185                <action android:name="com.google.firebase.MESSAGING_EVENT" />
185-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:17-78
185-->C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:25-75
186            </intent-filter>
187        </service>
188
189        <provider
189-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
190            android:name="com.google.firebase.provider.FirebaseInitProvider"
190-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
191            android:authorities="com.example.budgettracker.firebaseinitprovider"
191-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
192            android:directBootAware="true"
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
193            android:exported="false"
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
194            android:initOrder="100" />
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
195        <provider
195-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:29:9-37:20
196            android:name="androidx.startup.InitializationProvider"
196-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:30:13-67
197            android:authorities="com.example.budgettracker.androidx-startup"
197-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:31:13-68
198            android:exported="false" >
198-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:32:13-37
199            <meta-data
199-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:34:13-36:52
200                android:name="androidx.work.WorkManagerInitializer"
200-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:35:17-68
201                android:value="androidx.startup" />
201-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:36:17-49
202            <meta-data
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
203                android:name="androidx.emoji2.text.EmojiCompatInitializer"
203-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
204                android:value="androidx.startup" />
204-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
205            <meta-data
205-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
206                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
206-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
207                android:value="androidx.startup" />
207-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
208            <meta-data
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
209                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
210                android:value="androidx.startup" />
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
211        </provider>
212
213        <service
213-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:39:9-45:35
214            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
214-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:40:13-88
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:41:13-44
216            android:enabled="@bool/enable_system_alarm_service_default"
216-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:42:13-72
217            android:exported="false" />
217-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:43:13-37
218        <service
218-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:46:9-52:35
219            android:name="androidx.work.impl.background.systemjob.SystemJobService"
219-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:47:13-84
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:48:13-44
221            android:enabled="@bool/enable_system_job_service_default"
221-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:49:13-70
222            android:exported="true"
222-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:50:13-36
223            android:permission="android.permission.BIND_JOB_SERVICE" />
223-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:51:13-69
224        <service
224-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:53:9-59:35
225            android:name="androidx.work.impl.foreground.SystemForegroundService"
225-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:54:13-81
226            android:directBootAware="false"
226-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:55:13-44
227            android:enabled="@bool/enable_system_foreground_service_default"
227-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:56:13-77
228            android:exported="false" />
228-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:57:13-37
229
230        <receiver
230-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:61:9-66:35
231            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
231-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:62:13-88
232            android:directBootAware="false"
232-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:63:13-44
233            android:enabled="true"
233-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:64:13-35
234            android:exported="false" />
234-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:65:13-37
235        <receiver
235-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:67:9-77:20
236            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
236-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:68:13-106
237            android:directBootAware="false"
237-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:69:13-44
238            android:enabled="false"
238-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:70:13-36
239            android:exported="false" >
239-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:71:13-37
240            <intent-filter>
240-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:73:13-76:29
241                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
241-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:17-87
241-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:25-84
242                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
242-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:17-90
242-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:25-87
243            </intent-filter>
244        </receiver>
245        <receiver
245-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:78:9-88:20
246            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
246-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:79:13-104
247            android:directBootAware="false"
247-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:80:13-44
248            android:enabled="false"
248-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:81:13-36
249            android:exported="false" >
249-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:82:13-37
250            <intent-filter>
250-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:84:13-87:29
251                <action android:name="android.intent.action.BATTERY_OKAY" />
251-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:17-77
251-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:25-74
252                <action android:name="android.intent.action.BATTERY_LOW" />
252-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:17-76
252-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:25-73
253            </intent-filter>
254        </receiver>
255        <receiver
255-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:89:9-99:20
256            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
256-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:90:13-104
257            android:directBootAware="false"
257-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:91:13-44
258            android:enabled="false"
258-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:92:13-36
259            android:exported="false" >
259-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:93:13-37
260            <intent-filter>
260-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:95:13-98:29
261                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
261-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:17-83
261-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:25-80
262                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
262-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:17-82
262-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:25-79
263            </intent-filter>
264        </receiver>
265        <receiver
265-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:100:9-109:20
266            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
266-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:101:13-103
267            android:directBootAware="false"
267-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:102:13-44
268            android:enabled="false"
268-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:103:13-36
269            android:exported="false" >
269-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:104:13-37
270            <intent-filter>
270-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:106:13-108:29
271                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
271-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:17-79
271-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:25-76
272            </intent-filter>
273        </receiver>
274        <receiver
274-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:110:9-121:20
275            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
275-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:111:13-88
276            android:directBootAware="false"
276-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:112:13-44
277            android:enabled="false"
277-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:113:13-36
278            android:exported="false" >
278-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:114:13-37
279            <intent-filter>
279-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:116:13-120:29
280                <action android:name="android.intent.action.BOOT_COMPLETED" />
280-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
280-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
281                <action android:name="android.intent.action.TIME_SET" />
281-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:17-73
281-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:25-70
282                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
282-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:17-81
282-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:25-78
283            </intent-filter>
284        </receiver>
285        <receiver
285-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:122:9-131:20
286            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
286-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:123:13-99
287            android:directBootAware="false"
287-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:124:13-44
288            android:enabled="@bool/enable_system_alarm_service_default"
288-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:125:13-72
289            android:exported="false" >
289-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:126:13-37
290            <intent-filter>
290-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:128:13-130:29
291                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
291-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:17-98
291-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:25-95
292            </intent-filter>
293        </receiver>
294        <receiver
294-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:132:9-142:20
295            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
295-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:133:13-78
296            android:directBootAware="false"
296-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:134:13-44
297            android:enabled="true"
297-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:135:13-35
298            android:exported="true"
298-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:136:13-36
299            android:permission="android.permission.DUMP" >
299-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:137:13-57
300            <intent-filter>
300-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:139:13-141:29
301                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
301-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:17-88
301-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:25-85
302            </intent-filter>
303        </receiver>
304
305        <service
305-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
306            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
306-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
307            android:enabled="true"
307-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
308            android:exported="false" >
308-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
309            <meta-data
309-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
310                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
310-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
311                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
311-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
312        </service>
313
314        <activity
314-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
315            android:name="androidx.credentials.playservices.HiddenActivity"
315-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
316            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
316-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
317            android:enabled="true"
317-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
318            android:exported="false"
318-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
319            android:fitsSystemWindows="true"
319-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
320            android:theme="@style/Theme.Hidden" >
320-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
321        </activity>
322        <activity
322-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
323            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
323-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
324            android:excludeFromRecents="true"
324-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
325            android:exported="false"
325-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
326            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
326-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
327        <!--
328            Service handling Google Sign-In user revocation. For apps that do not integrate with
329            Google Sign-In, this service will never be started.
330        -->
331        <service
331-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
332            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
332-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
333            android:exported="true"
333-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
334            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
334-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
335            android:visibleToInstantApps="true" />
335-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
336
337        <activity
337-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
338            android:name="com.google.android.gms.common.api.GoogleApiActivity"
338-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
339            android:exported="false"
339-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
340            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
340-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
341
342        <uses-library
342-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
343            android:name="androidx.window.extensions"
343-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
344            android:required="false" />
344-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
345        <uses-library
345-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
346            android:name="androidx.window.sidecar"
346-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
347            android:required="false" />
347-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
348
349        <meta-data
349-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
350            android:name="com.google.android.gms.version"
350-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
351            android:value="@integer/google_play_services_version" />
351-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
352
353        <receiver
353-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
354            android:name="androidx.profileinstaller.ProfileInstallReceiver"
354-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
355            android:directBootAware="false"
355-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
356            android:enabled="true"
356-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
357            android:exported="true"
357-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
358            android:permission="android.permission.DUMP" >
358-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
359            <intent-filter>
359-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
360                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
360-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
360-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
361            </intent-filter>
362            <intent-filter>
362-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
363                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
363-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
363-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
364            </intent-filter>
365            <intent-filter>
365-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
366                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
366-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
366-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
367            </intent-filter>
368            <intent-filter>
368-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
369                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
369-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
369-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
370            </intent-filter>
371        </receiver>
372
373        <service
373-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
374            android:name="androidx.room.MultiInstanceInvalidationService"
374-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
375            android:directBootAware="true"
375-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
376            android:exported="false" />
376-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
377        <service
377-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
378            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
378-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
379            android:exported="false" >
379-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
380            <meta-data
380-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
381                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
381-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
382                android:value="cct" />
382-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
383        </service>
384        <service
384-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
385            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
385-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
386            android:exported="false"
386-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
387            android:permission="android.permission.BIND_JOB_SERVICE" >
387-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
388        </service>
389
390        <receiver
390-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
391            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
391-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
392            android:exported="false" />
392-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
393    </application>
394
395</manifest>
