package com.example.budgettracker;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.budgettracker.adapters.NotificationAdapter;
import com.example.budgettracker.DatabaseHelper;
import com.example.budgettracker.models.NotificationItem;
import com.example.budgettracker.NetworkUtils;
import com.example.budgettracker.NotificationHelper;
import com.example.budgettracker.SessionManager;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import android.util.Log;

public class NotificationActivity extends AppCompatActivity {
    private DatabaseHelper dbHelper;
    private RecyclerView notificationsRecyclerView;
    private NotificationAdapter notificationAdapter;
    private LinearLayout emptyState;
    private List<NotificationItem> notifications;
    private FirebaseManager firebaseManager;
    public String user_id;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SessionManager sessionManager = new SessionManager(this);
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid();
        }
        setContentView(R.layout.notifications);

        initializeViews();
        setupRecyclerView();
        loadNotifications();

        // Setup navigation buttons
        setupNavigationButtons();

        // Schedule periodic notification checks (only if Firebase is enabled)
        if (AppConfig.shouldUseFirebase()) {
            NotificationScheduler.schedulePeriodicNotificationChecks(this);
        }

        ImageButton backButton = findViewById(R.id.back_button);
        backButton.setOnClickListener(v -> {
            Intent intent = new Intent(NotificationActivity.this, DashboardActivity.class);
            startActivity(intent);
        });

        Button clearAllButton = findViewById(R.id.clear_all_button);
        clearAllButton.setOnClickListener(v -> clearAllNotifications());
    }

    private void initializeViews() {
        dbHelper = new DatabaseHelper(this);
        firebaseManager = new FirebaseManager();
        notificationsRecyclerView = findViewById(R.id.notifications_recycler_view);
        emptyState = findViewById(R.id.empty_state);
        notifications = new ArrayList<>();
        NotificationHelper.createNotificationChannel(this);
    }

    private void setupRecyclerView() {
        notificationAdapter = new NotificationAdapter(notifications, this::onNotificationClick);
        notificationsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        notificationsRecyclerView.setAdapter(notificationAdapter);
    }

    private void onNotificationClick(NotificationItem notification, int position) {
        if (!notification.isRead()) {
            notification.setRead(true);
            notificationAdapter.notifyItemChanged(position);

            if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) && firebaseManager != null && firebaseManager.isUserAuthenticated() && notification.getId() != null) {
                firebaseManager.markNotificationAsRead(notification.getId(), new FirebaseManager.FirebaseCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        Log.d("NotificationActivity", "Notification marked as read in Firebase");
                    }

                    @Override
                    public void onFailure(Exception e) {
                        Log.e("NotificationActivity", "Failed to mark notification as read in Firebase", e);
                    }
                });
            } else {
                Log.d("NotificationActivity", "Notification marked as read locally");
            }
        }
    }

    private void loadNotifications() {
        notifications.clear();

        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) && firebaseManager != null && firebaseManager.isUserAuthenticated()) {
            loadNotificationsFromFirebase();
        } else {
            loadNotificationsFromLocal();
        }
    }

    private void loadNotificationsFromFirebase() {
        firebaseManager.getNotifications(new FirebaseManager.FirebaseCallback<List<Map<String, Object>>>() {
            @Override
            public void onSuccess(List<Map<String, Object>> result) {
                notifications.clear();

                for (Map<String, Object> notificationData : result) {
                    String id = (String) notificationData.get("id");
                    String title = (String) notificationData.get("title");
                    String message = (String) notificationData.get("message");
                    String type = (String) notificationData.get("type");
                    String timestamp = (String) notificationData.get("timestamp");
                    boolean isRead = Boolean.TRUE.equals(notificationData.get("is_read"));

                    NotificationItem.NotificationType notificationType = getNotificationTypeFromString(type);
                    NotificationItem notification = new NotificationItem(title, message, getRelativeTime(timestamp), notificationType);
                    notification.setId(id);
                    notification.setRead(isRead);
                    notifications.add(notification);
                }

                // Also generate real-time notifications based on current data
                generateRealTimeNotifications();

                updateUI();
            }

            @Override
            public void onFailure(Exception e) {
                Log.e("NotificationActivity", "Failed to load Firebase notifications", e);
                // Fallback to local notifications
                loadNotificationsFromLocal();
            }
        });
    }

    private void loadNotificationsFromLocal() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        String monthYear = currentDate.substring(0, 7);

        if (AppConfig.shouldUseFirebase() && firebaseManager != null && firebaseManager.isUserAuthenticated()) {
            generateNotificationsFromDataOnline(monthYear);
        } else {
            generateNotificationsFromData(monthYear);
        }
    }

    private void generateNotificationsFromData(String monthYear) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();

        // Check budget status
        checkBudgetStatus(db, monthYear);

        // Check recent transactions
        checkRecentTransactions(db);

        // Check recent expenses
        checkRecentExpenses(db);

        // Check low balance alerts
        checkLowBalanceLocal(db, monthYear);

        updateUI();
    }

    private void checkLowBalanceLocal(SQLiteDatabase db, String monthYear) {
        String query = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?";
        Cursor cursor = db.rawQuery(query, new String[]{monthYear, String.valueOf(user_id)});

        if (cursor.moveToFirst()) {
            @SuppressLint("Range") double balance = cursor.getDouble(cursor.getColumnIndex("trans_balance"));

            // Define low balance threshold
            double lowBalanceThreshold = 100.0;

            if (balance <= lowBalanceThreshold && balance > 0) {
                notifications.add(new NotificationItem(
                    "Low Balance Alert",
                    String.format("Your current balance is low: $%.2f. Consider adding funds.", balance),
                    "Today",
                    NotificationItem.NotificationType.LOW_BALANCE
                ));
            }
        }
        cursor.close();
    }

    private void checkBudgetStatus(SQLiteDatabase db, String monthYear) {
        String query = "SELECT trans_balance, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?";
        Cursor cursor = db.rawQuery(query, new String[]{monthYear, String.valueOf(user_id)});

        if (cursor.moveToFirst()) {
            @SuppressLint("Range") double balance = cursor.getDouble(cursor.getColumnIndex("trans_balance"));
            @SuppressLint("Range") double target = cursor.getDouble(cursor.getColumnIndex("trans_target"));

            if (target > 0) {
                double percentage = (balance / target) * 100;

                if (percentage >= 100) {
                    notifications.add(new NotificationItem(
                        "Budget Exceeded!",
                        String.format("You've exceeded your budget by $%.2f this month", balance - target),
                        "Today",
                        NotificationItem.NotificationType.BUDGET_EXCEEDED
                    ));
                } else if (percentage >= 80) {
                    notifications.add(new NotificationItem(
                        "Budget Warning",
                        String.format("You've spent %.0f%% of your budget for this month", percentage),
                        "Today",
                        NotificationItem.NotificationType.BUDGET_WARNING
                    ));
                }

                if (balance >= target) {
                    notifications.add(new NotificationItem(
                        "Goal Achieved!",
                        "Congratulations! You've reached your savings goal for this month.",
                        "Today",
                        NotificationItem.NotificationType.GOAL_ACHIEVED
                    ));
                }
            }
        }
        cursor.close();
    }

    private void checkRecentTransactions(SQLiteDatabase db) {
        String query = "SELECT bud_name, bud_amount, bud_date FROM budget WHERE user_id = ? ORDER BY bud_date DESC LIMIT 3";
        Cursor cursor = db.rawQuery(query, new String[]{String.valueOf(user_id)});

        while (cursor.moveToNext()) {
            @SuppressLint("Range") String name = cursor.getString(cursor.getColumnIndex("bud_name"));
            @SuppressLint("Range") int amount = cursor.getInt(cursor.getColumnIndex("bud_amount"));
            @SuppressLint("Range") String date = cursor.getString(cursor.getColumnIndex("bud_date"));

            notifications.add(new NotificationItem(
                "Transaction Added",
                String.format("Added transaction: %s", name),
                getRelativeTime(date),
                NotificationItem.NotificationType.TRANSACTION_ADDED,
                String.format("$%d", amount)
            ));
        }
        cursor.close();
    }

    private void checkRecentExpenses(SQLiteDatabase db) {
        String query = "SELECT name, amount, date FROM expenses WHERE user_id = ? ORDER BY date DESC LIMIT 3";
        Cursor cursor = db.rawQuery(query, new String[]{String.valueOf(user_id)});

        while (cursor.moveToNext()) {
            @SuppressLint("Range") String name = cursor.getString(cursor.getColumnIndex("name"));
            @SuppressLint("Range") int amount = cursor.getInt(cursor.getColumnIndex("amount"));
            @SuppressLint("Range") String date = cursor.getString(cursor.getColumnIndex("date"));

            notifications.add(new NotificationItem(
                "Expense Added",
                String.format("Added expense: %s", name),
                getRelativeTime(date),
                NotificationItem.NotificationType.EXPENSE_ADDED,
                String.format("$%d", amount)
            ));
        }
        cursor.close();
    }

    private String getRelativeTime(String dateString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            Date date = sdf.parse(dateString);
            Date now = new Date();

            long diffInMillis = now.getTime() - date.getTime();
            long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);

            if (diffInDays == 0) {
                return "Today";
            } else if (diffInDays == 1) {
                return "Yesterday";
            } else if (diffInDays < 7) {
                return diffInDays + " days ago";
            } else {
                return "1 week ago";
            }
        } catch (Exception e) {
            return "Recently";
        }
    }

    private void generateNotificationsFromDataOnline(String monthYear) {
        if (!firebaseManager.isUserAuthenticated()) {
            generateNotificationsFromData(monthYear);
            return;
        }

        // Check budget status from Firebase
        checkBudgetStatusOnline(monthYear);

        // Check recent transactions from Firebase
        checkRecentTransactionsOnline();

        // Check recent expenses from Firebase
        checkRecentExpensesOnline();

        // Generate real-time notifications
        generateRealTimeNotifications();
    }

    private void checkBudgetStatusOnline(String monthYear) {
        firebaseManager.getOrCreateTransactionSummary(monthYear, new FirebaseManager.FirebaseCallback<Map<String, Object>>() {
            @Override
            public void onSuccess(Map<String, Object> result) {
                double balance = 0.0;
                double target = 0.0;

                if (result.containsKey("trans_balance")) {
                    balance = ((Number) result.get("trans_balance")).doubleValue();
                }
                if (result.containsKey("trans_target")) {
                    target = ((Number) result.get("trans_target")).doubleValue();
                }

                if (target > 0) {
                    double percentage = (balance / target) * 100;

                    if (percentage >= 100) {
                        notifications.add(new NotificationItem(
                            "Budget Exceeded!",
                            String.format("You've exceeded your budget by $%.2f this month", balance - target),
                            "Today",
                            NotificationItem.NotificationType.BUDGET_EXCEEDED
                        ));
                    } else if (percentage >= 80) {
                        notifications.add(new NotificationItem(
                            "Budget Warning",
                            String.format("You've spent %.0f%% of your budget for this month", percentage),
                            "Today",
                            NotificationItem.NotificationType.BUDGET_WARNING
                        ));
                    }

                    // Check goal achievement
                    if (balance >= target) {
                        notifications.add(new NotificationItem(
                            "Goal Achieved!",
                            "Congratulations! You've reached your savings goal for this month.",
                            "Today",
                            NotificationItem.NotificationType.GOAL_ACHIEVED
                        ));
                    }

                    // Check low balance
                    if (balance <= 100.0 && balance > 0) {
                        notifications.add(new NotificationItem(
                            "Low Balance Alert",
                            String.format("Your current balance is low: $%.2f. Consider adding funds.", balance),
                            "Today",
                            NotificationItem.NotificationType.LOW_BALANCE
                        ));
                    }
                }
                updateUI();
            }

            @Override
            public void onFailure(Exception e) {
                Log.e("NotificationActivity", "Failed to check budget status online", e);
            }
        });
    }

    private void checkRecentTransactionsOnline() {
        firebaseManager.getTransactions(new FirebaseManager.FirebaseCallback<List<Map<String, Object>>>() {
            @Override
            public void onSuccess(List<Map<String, Object>> result) {
                // Get the 3 most recent transactions
                int count = Math.min(3, result.size());
                for (int i = 0; i < count; i++) {
                    Map<String, Object> transaction = result.get(i);
                    String name = (String) transaction.get("bud_name");
                    Number amountNum = (Number) transaction.get("bud_amount");
                    String date = (String) transaction.get("bud_date");

                    if (name != null && amountNum != null) {
                        notifications.add(new NotificationItem(
                            "Transaction Added",
                            String.format("Added transaction: %s", name),
                            getRelativeTime(date),
                            NotificationItem.NotificationType.TRANSACTION_ADDED,
                            String.format("$%.2f", amountNum.doubleValue())
                        ));
                    }
                }
                updateUI();
            }

            @Override
            public void onFailure(Exception e) {
                Log.e("NotificationActivity", "Failed to check recent transactions online", e);
            }
        });
    }

    private void checkRecentExpensesOnline() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        String monthYear = currentDate.substring(0, 7);

        firebaseManager.getExpensesForMonth(monthYear, new FirebaseManager.FirebaseCallback<List<Map<String, Object>>>() {
            @Override
            public void onSuccess(List<Map<String, Object>> result) {
                // Get the 3 most recent expenses
                int count = Math.min(3, result.size());
                for (int i = 0; i < count; i++) {
                    Map<String, Object> expense = result.get(i);
                    String name = (String) expense.get("name");
                    Number amountNum = (Number) expense.get("amount");
                    String date = (String) expense.get("date");

                    if (name != null && amountNum != null) {
                        notifications.add(new NotificationItem(
                            "Expense Added",
                            String.format("Added expense: %s", name),
                            getRelativeTime(date),
                            NotificationItem.NotificationType.EXPENSE_ADDED,
                            String.format("$%.2f", amountNum.doubleValue())
                        ));
                    }
                }
                updateUI();
            }

            @Override
            public void onFailure(Exception e) {
                Log.e("NotificationActivity", "Failed to check recent expenses online", e);
            }
        });
    }

    private void updateUI() {
        if (notifications.isEmpty()) {
            notificationsRecyclerView.setVisibility(View.GONE);
            emptyState.setVisibility(View.VISIBLE);
        } else {
            notificationsRecyclerView.setVisibility(View.VISIBLE);
            emptyState.setVisibility(View.GONE);
            notificationAdapter.updateNotifications(notifications);
        }
    }

    private void clearAllNotifications() {
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this)) && firebaseManager != null && firebaseManager.isUserAuthenticated()) {
            firebaseManager.clearAllNotifications(new FirebaseManager.FirebaseCallback<Void>() {
                @Override
                public void onSuccess(Void result) {
                    notifications.clear();
                    updateUI();
                }

                @Override
                public void onFailure(Exception e) {
                    Log.e("NotificationActivity", "Failed to clear Firebase notifications", e);
                    notifications.clear();
                    updateUI();
                }
            });
        } else {
            notifications.clear();
            updateUI();
        }
    }

    private NotificationItem.NotificationType getNotificationTypeFromString(String type) {
        if (type == null) return NotificationItem.NotificationType.TRANSACTION_ADDED;

        switch (type) {
            case "BUDGET_WARNING":
                return NotificationItem.NotificationType.BUDGET_WARNING;
            case "BUDGET_EXCEEDED":
                return NotificationItem.NotificationType.BUDGET_EXCEEDED;
            case "GOAL_ACHIEVED":
                return NotificationItem.NotificationType.GOAL_ACHIEVED;
            case "TRANSACTION_ADDED":
                return NotificationItem.NotificationType.TRANSACTION_ADDED;
            case "EXPENSE_ADDED":
                return NotificationItem.NotificationType.EXPENSE_ADDED;
            case "LOW_BALANCE":
                return NotificationItem.NotificationType.LOW_BALANCE;
            default:
                return NotificationItem.NotificationType.TRANSACTION_ADDED;
        }
    }

    private void generateRealTimeNotifications() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        String monthYear = currentDate.substring(0, 7);

        // Check budget status and create Firebase notifications if needed
        if (firebaseManager.isUserAuthenticated()) {
            firebaseManager.getOrCreateTransactionSummary(monthYear, new FirebaseManager.FirebaseCallback<Map<String, Object>>() {
                @Override
                public void onSuccess(Map<String, Object> result) {
                    double balance = 0.0;
                    double target = 0.0;
                    double budget = 0.0;

                    if (result.containsKey("trans_balance")) {
                        balance = ((Number) result.get("trans_balance")).doubleValue();
                    }
                    if (result.containsKey("trans_target")) {
                        target = ((Number) result.get("trans_target")).doubleValue();
                    }
                    if (result.containsKey("trans_budget")) {
                        budget = ((Number) result.get("trans_budget")).doubleValue();
                    }

                    // Check budget alerts
                    if (target > 0) {
                        double percentage = (balance / target) * 100;

                        if (percentage >= 80) {
                            firebaseManager.createBudgetAlert(monthYear, percentage, new FirebaseManager.FirebaseCallback<Void>() {
                                @Override
                                public void onSuccess(Void result) {
                                    Log.d("NotificationActivity", "Budget alert notification created");
                                }

                                @Override
                                public void onFailure(Exception e) {
                                    Log.e("NotificationActivity", "Failed to create budget alert", e);
                                }
                            });
                        }
                    }

                    // Check goal achievements
                    if (target > 0 && balance >= target) {
                        firebaseManager.createGoalAchievementNotification("savings", new FirebaseManager.FirebaseCallback<Void>() {
                            @Override
                            public void onSuccess(Void result) {
                                Log.d("NotificationActivity", "Goal achievement notification created");
                            }

                            @Override
                            public void onFailure(Exception e) {
                                Log.e("NotificationActivity", "Failed to create goal achievement notification", e);
                            }
                        });
                    }

                    // Check low balance alerts
                    checkLowBalanceAlert(balance);
                }

                @Override
                public void onFailure(Exception e) {
                    Log.e("NotificationActivity", "Failed to get transaction summary", e);
                }
            });
        }
    }

    private void checkLowBalanceAlert(double currentBalance) {
        // Define low balance threshold (e.g., $100 or less)
        double lowBalanceThreshold = 100.0;

        if (currentBalance <= lowBalanceThreshold && currentBalance > 0) {
            firebaseManager.saveNotification(
                "Low Balance Alert",
                String.format("Your current balance is low: $%.2f. Consider adding funds.", currentBalance),
                "LOW_BALANCE",
                new FirebaseManager.FirebaseCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        Log.d("NotificationActivity", "Low balance notification created");
                    }

                    @Override
                    public void onFailure(Exception e) {
                        Log.e("NotificationActivity", "Failed to create low balance notification", e);
                    }
                }
            );
        }
    }

    private void setupNavigationButtons() {
        ImageButton homeButton = findViewById(R.id.button_home);
        ImageButton expensesButton = findViewById(R.id.button_expenses);
        ImageButton transactionsButton = findViewById(R.id.button_transactions);
        ImageButton profileButton = findViewById(R.id.button_profile);
        ImageButton notificationButton = findViewById(R.id.button_notification);

        homeButton.setOnClickListener(v -> navigateTo(DashboardActivity.class));
        expensesButton.setOnClickListener(v -> navigateTo(ExpensesActivity.class));
        transactionsButton.setOnClickListener(v -> navigateTo(TransactionsActivity.class));
        profileButton.setOnClickListener(v -> navigateTo(SettingsActivity.class));
        notificationButton.setOnClickListener(v -> {
            // Already in notifications activity, do nothing or refresh
            loadNotifications();
        });
    }

    private void navigateTo(Class<?> activityClass) {
        if (!this.getClass().equals(activityClass)) {
            Intent intent = new Intent(this, activityClass);
            startActivity(intent);
        }
    }
}
