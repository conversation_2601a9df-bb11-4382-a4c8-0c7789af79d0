{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.example.budgettracker.app-mergeReleaseResources-58:\\values-v27\\values-v27.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-v27\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "151", "endLines": "50", "endColumns": "12", "endOffsets": "2982"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "49", "endColumns": "12", "endOffsets": "2653"}}]}, {"outputFile": "com.example.budgettracker.app-mergeReleaseResources-58:/values-v27/values-v27.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-v27\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "151", "endLines": "50", "endColumns": "12", "endOffsets": "2979"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "49", "endColumns": "12", "endOffsets": "2650"}}]}]}