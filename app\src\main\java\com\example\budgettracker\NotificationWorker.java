package com.example.budgettracker;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

public class NotificationWorker extends Worker {
    private static final String TAG = "NotificationWorker";
    private FirebaseManager firebaseManager;

    public NotificationWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
        firebaseManager = new FirebaseManager();
    }

    @NonNull
    @Override
    public Result doWork() {
        Log.d(TAG, "Starting periodic notification check");
        
        try {
            checkNotificationConditions();
            return Result.success();
        } catch (Exception e) {
            Log.e(TAG, "Error during notification check", e);
            return Result.retry();
        }
    }

    private void checkNotificationConditions() {
        if (!firebaseManager.isUserAuthenticated()) {
            Log.d(TAG, "User not authenticated, skipping notification check");
            return;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        String monthYear = currentDate.substring(0, 7);

        // Check budget conditions
        checkBudgetConditions(monthYear);
    }

    private void checkBudgetConditions(String monthYear) {
        firebaseManager.getOrCreateTransactionSummary(monthYear, new FirebaseManager.FirebaseCallback<Map<String, Object>>() {
            @Override
            public void onSuccess(Map<String, Object> result) {
                double balance = 0.0;
                double target = 0.0;
                double budget = 0.0;

                if (result.containsKey("trans_balance")) {
                    balance = ((Number) result.get("trans_balance")).doubleValue();
                }
                if (result.containsKey("trans_target")) {
                    target = ((Number) result.get("trans_target")).doubleValue();
                }
                if (result.containsKey("trans_budget")) {
                    budget = ((Number) result.get("trans_budget")).doubleValue();
                }

                // Check for budget alerts (approaching 80% or exceeded)
                if (target > 0) {
                    double percentage = (balance / target) * 100;
                    
                    if (percentage >= 90 && percentage < 100) {
                        // Critical budget warning
                        createBudgetWarningNotification(percentage, "critical");
                    } else if (percentage >= 80 && percentage < 90) {
                        // Regular budget warning
                        createBudgetWarningNotification(percentage, "warning");
                    } else if (percentage >= 100) {
                        // Budget exceeded
                        createBudgetExceededNotification(balance - target);
                    }
                }

                // Check for low balance
                if (balance <= 100.0 && balance > 0) {
                    createLowBalanceNotification(balance);
                }

                // Check for goal achievement
                if (target > 0 && balance >= target) {
                    createGoalAchievementNotification();
                }
            }

            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to check budget conditions", e);
            }
        });
    }

    private void createBudgetWarningNotification(double percentage, String severity) {
        String title = severity.equals("critical") ? "Critical Budget Alert!" : "Budget Warning";
        String message = String.format("You've spent %.0f%% of your budget this month", percentage);
        
        firebaseManager.saveNotification(title, message, "BUDGET_WARNING", new FirebaseManager.FirebaseCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                Log.d(TAG, "Budget warning notification created");
                // Also show local notification
                NotificationHelper.sendNotification(getApplicationContext(), title, message);
            }

            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to create budget warning notification", e);
            }
        });
    }

    private void createBudgetExceededNotification(double exceededAmount) {
        String title = "Budget Exceeded!";
        String message = String.format("You've exceeded your budget by $%.2f this month", exceededAmount);
        
        firebaseManager.saveNotification(title, message, "BUDGET_EXCEEDED", new FirebaseManager.FirebaseCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                Log.d(TAG, "Budget exceeded notification created");
                NotificationHelper.sendNotification(getApplicationContext(), title, message);
            }

            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to create budget exceeded notification", e);
            }
        });
    }

    private void createLowBalanceNotification(double balance) {
        String title = "Low Balance Alert";
        String message = String.format("Your current balance is low: $%.2f. Consider adding funds.", balance);
        
        firebaseManager.saveNotification(title, message, "LOW_BALANCE", new FirebaseManager.FirebaseCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                Log.d(TAG, "Low balance notification created");
                NotificationHelper.sendNotification(getApplicationContext(), title, message);
            }

            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to create low balance notification", e);
            }
        });
    }

    private void createGoalAchievementNotification() {
        String title = "Goal Achieved!";
        String message = "Congratulations! You've reached your savings goal for this month.";
        
        firebaseManager.saveNotification(title, message, "GOAL_ACHIEVED", new FirebaseManager.FirebaseCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                Log.d(TAG, "Goal achievement notification created");
                NotificationHelper.sendNotification(getApplicationContext(), title, message);
            }

            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "Failed to create goal achievement notification", e);
            }
        });
    }
}
