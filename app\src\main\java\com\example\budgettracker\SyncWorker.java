package com.example.budgettracker;

import android.annotation.SuppressLint;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SyncWorker extends Worker {

    private final ApiService apiService;
    private final DatabaseHelper dbHelper;

    public SyncWorker(@NonNull Context context, @NonNull WorkerParameters params) {
        super(context, params);
        this.apiService = RetrofitClient.getClient().create(ApiService.class);
        this.dbHelper = new DatabaseHelper(context);
    }

    @NonNull
    @Override
    public Result doWork() {
        Log.d("SyncWorker", "SyncWorker is running");
//        try {
//            syncData();
//            return Result.success();
//        } catch (Exception e) {
//            Log.e("SyncWorker", "Sync failed: " + e);
//            return Result.failure();
//        }
        return null;
    }
    private List<Map<String, Object>> transformTransactions(List<Transaction> transactions) {
        List<Map<String, Object>> transformedList = new ArrayList<>();
        for (Transaction trans : transactions) {
            Map<String, Object> transMap = new HashMap<>();
            transMap.put("trans_balance", trans.getBalance());
            transMap.put("trans_budget", trans.getTotal());
            transMap.put("trans_target", trans.getTransTarget());
            transMap.put("trans_month", trans.getDate());
            transMap.put("user_id", trans.getuser_id());
            transformedList.add(transMap);
        }
        return transformedList;
    }private List<Map<String, Object>> transformUsers(List<User> users) {
        List<Map<String, Object>> transformedList = new ArrayList<>();
        for (User user : users) {
            Map<String, Object> userMap = new HashMap<>();
            userMap.put("user_id", user.getuser_id());
            userMap.put("created_date", user.getCreatedDate());
            userMap.put("user_email", user.getUserEmail());
            userMap.put("user_status", user.getUserStatus());
            userMap.put("user_name", user.getUserName());
            transformedList.add(userMap);
        }
        return transformedList;
    }
    private List<Map<String, Object>> transformBudgets(List<Budgets> budgets) {
        List<Map<String, Object>> transformedList = new ArrayList<>();
        for (Budgets budget : budgets) {
            Map<String, Object> budgetMap = new HashMap<>();
            budgetMap.put("user_id", budget.getuser_id());
            budgetMap.put("bud_date", budget.getDate());
            budgetMap.put("bud_amount", budget.getAmount());
            budgetMap.put("bud_name", budget.getName());
            transformedList.add(budgetMap);
        }
        return transformedList;
    }

    private void syncData() {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        List<Expenses> expenses = fetchExpenses(db);
        List<User> users = fetchUsers(db);
        List<Budgets> budgets = fetchBudgets(db);
        List<Transaction> transactions = fetchTransactions(db);
//        List<Map<String, Object>> transformedTransactions = transformTransactions(transactions);
//        List<Map<String, Object>> transformedUsers = transformUsers(users);
//        List<Map<String, Object>> transformedBudgets = transformBudgets(budgets);

        Map<String, Object> data = new HashMap<>();
        data.put("expenses", expenses);
        data.put("user", users);
        data.put("budget", budgets);
        data.put("transactions", transactions);

        Log.d("SyncWorker", "Sync data: " + new Gson().toJson(data));


        Call<Void> call = apiService.syncAllData(data);
        call.enqueue(new Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull Response<Void> response) {
                if (response.isSuccessful()) {
                    deleteAllData(db);
                } else {
                    Log.e("SyncWorker", "Sync failed: " + response);
                }
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                Log.e("SyncWorker", "Sync error: " + t);
            }
        });

        db.close();
    }

    private List<Expenses> fetchExpenses(SQLiteDatabase db) {
        List<Expenses> expensesList = new ArrayList<>();
        Cursor cursor = db.rawQuery("SELECT * FROM expenses", null);
        while (cursor.moveToNext()) {
            @SuppressLint("Range") Expenses expense = new Expenses(
                    cursor.getString(cursor.getColumnIndex("name")),
                    cursor.getString(cursor.getColumnIndex("date")),
                    cursor.getString(cursor.getColumnIndex("note")),
                    cursor.getInt(cursor.getColumnIndex("amount")),
                    cursor.getString(cursor.getColumnIndex("user_id"))
            );
            expensesList.add(expense);
        }
        cursor.close();
        return expensesList;
    }

    private List<User> fetchUsers(SQLiteDatabase db) {
        List<User> usersList = new ArrayList<>();
        Cursor cursor = db.rawQuery("SELECT * FROM user", null);
        while (cursor.moveToNext()) {
            @SuppressLint("Range") User user = new User(
                    cursor.getString(cursor.getColumnIndex("user_id")),
                    cursor.getString(cursor.getColumnIndex("created_date")),
                    cursor.getString(cursor.getColumnIndex("user_name")),
                    cursor.getInt(cursor.getColumnIndex("user_status")),
                    cursor.getString(cursor.getColumnIndex("user_email"))
            );
            usersList.add(user);
        }
        cursor.close();
        Log.w("Trial fetchUsers", String.valueOf(usersList));
        return usersList;
    }

    private List<Budgets> fetchBudgets(SQLiteDatabase db) {
        List<Budgets> budgetsList = new ArrayList<>();
        Cursor cursor = db.rawQuery("SELECT * FROM budget", null);
        while (cursor.moveToNext()) {
            @SuppressLint("Range") Budgets budget = new Budgets(
                    cursor.getString(cursor.getColumnIndex("bud_name")),
                    cursor.getString(cursor.getColumnIndex("bud_date")),
                    cursor.getInt(cursor.getColumnIndex("bud_amount")),
                    cursor.getString(cursor.getColumnIndex("user_id"))
            );
            budgetsList.add(budget);
        }
        cursor.close();
        Log.w("Trial fetchBudgets", String.valueOf(budgetsList));
        return budgetsList;
    }

    private List<Transaction> fetchTransactions(SQLiteDatabase db) {
        List<Transaction> transactionsList = new ArrayList<>();
        Cursor cursor = db.rawQuery("SELECT * FROM transactions", null);
        while (cursor.moveToNext()) {
            @SuppressLint("Range") Transaction trans = new Transaction(
                    cursor.getFloat(cursor.getColumnIndex("trans_budget")),
                    cursor.getFloat(cursor.getColumnIndex("trans_balance")),
                    cursor.getFloat(cursor.getColumnIndex("trans_target")),
                    cursor.getString(cursor.getColumnIndex("user_id")),
                    cursor.getString(cursor.getColumnIndex("trans_month"))
            );
            transactionsList.add(trans);
        }
        cursor.close();
        Log.w("Trial fetchTransactions", String.valueOf(transactionsList));
        return transactionsList;
    }

    private void deleteAllData(SQLiteDatabase db) {
//        db.execSQL("DELETE FROM expenses");
//        db.execSQL("DELETE FROM user");
//        db.execSQL("DELETE FROM budget");
//        db.execSQL("DELETE FROM transactions");
    }
}
