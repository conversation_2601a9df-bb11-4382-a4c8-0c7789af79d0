package com.example.budgettracker;

public class Transaction extends Record {
    private String trans_month;
    private double trans_target;
    private double trans_balance;
    private double trans_budget;

    // Constructor
    public Transaction( double trans_budget,double trans_balance,double trans_target, String user_id,String trans_month) {
        super(user_id);
        this.trans_month = trans_month;
        this.trans_target = trans_target;
        this.trans_balance = trans_balance;
        this.trans_budget = trans_budget;
    }

    // Getters and Setters
    public String getDate() {
        return trans_month;
    }

    public void setDate(String trans_month) {
        this.trans_month = trans_month;
    }


    public double getTransTarget() {
        return trans_target;
    }

    public void setTransTarget(double trans_target) {
        this.trans_target = trans_target;
    }

    public double getBalance() {
        return trans_balance;
    }

    public void setBalance(double trans_balance) {
        this.trans_balance = trans_balance;
    }

    public double getTotal() {
        return trans_budget;
    }

    public void setTotal(double trans_budget) {
        this.trans_budget = trans_budget;
    }
}