// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class RegisterBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final LinearLayout emailContainer;

  @NonNull
  public final EditText emailInput;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final LinearLayout passwordContainer;

  @NonNull
  public final LinearLayout passwordContainerConfrim;

  @NonNull
  public final ImageView passwordEye;

  @NonNull
  public final ImageView passwordEyeConfrim;

  @NonNull
  public final EditText passwordInput;

  @NonNull
  public final EditText passwordInputConfrim;

  @NonNull
  public final ImageView registerLogo;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final Button signupButton;

  @NonNull
  public final RelativeLayout spinnerContainer;

  @NonNull
  public final LinearLayout usernameContainer;

  @NonNull
  public final EditText usernameInput;

  private RegisterBinding(@NonNull RelativeLayout rootView, @NonNull ImageButton backButton,
      @NonNull LinearLayout emailContainer, @NonNull EditText emailInput,
      @NonNull LinearLayout headerLayout, @NonNull LinearLayout passwordContainer,
      @NonNull LinearLayout passwordContainerConfrim, @NonNull ImageView passwordEye,
      @NonNull ImageView passwordEyeConfrim, @NonNull EditText passwordInput,
      @NonNull EditText passwordInputConfrim, @NonNull ImageView registerLogo,
      @NonNull RelativeLayout rootLayout, @NonNull Button signupButton,
      @NonNull RelativeLayout spinnerContainer, @NonNull LinearLayout usernameContainer,
      @NonNull EditText usernameInput) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.emailContainer = emailContainer;
    this.emailInput = emailInput;
    this.headerLayout = headerLayout;
    this.passwordContainer = passwordContainer;
    this.passwordContainerConfrim = passwordContainerConfrim;
    this.passwordEye = passwordEye;
    this.passwordEyeConfrim = passwordEyeConfrim;
    this.passwordInput = passwordInput;
    this.passwordInputConfrim = passwordInputConfrim;
    this.registerLogo = registerLogo;
    this.rootLayout = rootLayout;
    this.signupButton = signupButton;
    this.spinnerContainer = spinnerContainer;
    this.usernameContainer = usernameContainer;
    this.usernameInput = usernameInput;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static RegisterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static RegisterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.register, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static RegisterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.email_container;
      LinearLayout emailContainer = ViewBindings.findChildViewById(rootView, id);
      if (emailContainer == null) {
        break missingId;
      }

      id = R.id.email_input;
      EditText emailInput = ViewBindings.findChildViewById(rootView, id);
      if (emailInput == null) {
        break missingId;
      }

      id = R.id.header_layout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.password_container;
      LinearLayout passwordContainer = ViewBindings.findChildViewById(rootView, id);
      if (passwordContainer == null) {
        break missingId;
      }

      id = R.id.password_container_confrim;
      LinearLayout passwordContainerConfrim = ViewBindings.findChildViewById(rootView, id);
      if (passwordContainerConfrim == null) {
        break missingId;
      }

      id = R.id.password_eye;
      ImageView passwordEye = ViewBindings.findChildViewById(rootView, id);
      if (passwordEye == null) {
        break missingId;
      }

      id = R.id.password_eye_confrim;
      ImageView passwordEyeConfrim = ViewBindings.findChildViewById(rootView, id);
      if (passwordEyeConfrim == null) {
        break missingId;
      }

      id = R.id.password_input;
      EditText passwordInput = ViewBindings.findChildViewById(rootView, id);
      if (passwordInput == null) {
        break missingId;
      }

      id = R.id.password_input_confrim;
      EditText passwordInputConfrim = ViewBindings.findChildViewById(rootView, id);
      if (passwordInputConfrim == null) {
        break missingId;
      }

      id = R.id.register_logo;
      ImageView registerLogo = ViewBindings.findChildViewById(rootView, id);
      if (registerLogo == null) {
        break missingId;
      }

      RelativeLayout rootLayout = (RelativeLayout) rootView;

      id = R.id.signup_button;
      Button signupButton = ViewBindings.findChildViewById(rootView, id);
      if (signupButton == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      id = R.id.username_container;
      LinearLayout usernameContainer = ViewBindings.findChildViewById(rootView, id);
      if (usernameContainer == null) {
        break missingId;
      }

      id = R.id.username_input;
      EditText usernameInput = ViewBindings.findChildViewById(rootView, id);
      if (usernameInput == null) {
        break missingId;
      }

      return new RegisterBinding((RelativeLayout) rootView, backButton, emailContainer, emailInput,
          headerLayout, passwordContainer, passwordContainerConfrim, passwordEye,
          passwordEyeConfrim, passwordInput, passwordInputConfrim, registerLogo, rootLayout,
          signupButton, spinnerContainer, usernameContainer, usernameInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
