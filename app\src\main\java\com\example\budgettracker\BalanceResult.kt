package com.example.budgettracker

class BalanceResult(
    private var totalBudget: Float,
    private var balance: Float,
    private var transTarget: Float // Note the consistent naming convention for the field
) {
    // Getter for totalBudget
    fun getTotalBudget(): Float {
        return totalBudget
    }

    // Setter for totalBudget
    fun setTotalBudget(totalBudget: Float) {
        this.totalBudget = totalBudget
    }

    // Getter for balance
    fun getBalance(): Float {
        return balance
    }

    // Setter for balance
    fun setBalance(balance: Float) {
        this.balance = balance
    }

    // Getter for transTarget
    fun getTransTarget(): Float {
        return transTarget
    }

    // Setter for transTarget
    fun setTransTarget(transTarget: Float) {
        this.transTarget = transTarget
    }
}
