-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-55:12
INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-55:12
INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-55:12
INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-55:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\87daeb89e7006dad3b2d70792f7dc363\transformed\jetified-viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8cbcc578d44f3eb74db94fab2c6b909f\transformed\navigation-common-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\34a0dba344bab218dae460ae09545d83\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8faebcb1c3a5e3e4cbf4eebb68087685\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\transformed\navigation-ui-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c377da76ffd722c11f7e2a995dabf1f9\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\4fbad6584b625f6cbde5b424d0ce76e0\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\505f45660c762064795deae2c2e196b4\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8fe6d8e6447df98388c2372dd5db4cda\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c31e469962acb98785db8c0d2a661168\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ecc0e1a6fb1bf17d44fb5bd4b082ceae\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\4909f730a9e7806222bd84df864833cf\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\1c641e390a7be6a0bccc310f791f99a5\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6f8046b9f6ebb729ea86cd38f19e22c2\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7fbf9f8dcec6439dc9000f6ca3bc4a6\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d6040b0339e72d27b1a9f3cbc100b37\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\78f8bd9f21c9c9e350b0ae9463cb6254\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da59337531a415863d35241726a5e12a\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\4eee9fd69195cf7da3f5523c2ce9c927\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\743dfef3b5f1b030098eeeb08afd7f3a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.contentpager:contentpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f71eadc1b8e90646e5ad874b5903c79\transformed\contentpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d3d90c764242f30fac5fefd7ddbf3ef\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\9772ea6e98fb513fde374d6102af9be0\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1e820dea617353e0956c65165b54c8b\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea784ed1012bd601aa8faf8751b2d9b8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ef09979244db1040cf88eeabe9be1cb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\51674a575b03a3f252817171cb48cb1e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2a3b35c4940c1c2d53fd392389e37fd7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c390d111e0296cb277f7457d019cd712\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\db4986a1ffdc5cf11da22a86fc7d5e8c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba365e17b0c92088e8b876778529c00e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f45219147905cc69ca8d23ceb240d1c0\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac93c7f5e634f2bb5a4c72004cb6aa8f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\048055b1c844a0d4657ea5603727f4a7\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac808904cd7e03e33d69da647d3102c\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b487c08a660904bdeeea450e14c67b6\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\73d2850eb1f9692bf7f675809cde6554\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5b690fe5611e1aebf99a0fd20c574b4\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81c1d2dadc3ad1df6d9d5ca28e1dad\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\baa02ee2b336dd28d751a84382ff3f88\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\414810306ab42cdbd7aeac81ddbd0d14\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b54f3439f91d18bcb99cb17e6de28ef4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\180ea994d7df7386e51a57698ad3d488\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba1416b4e6ca07f65a70639c28a97d1c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\9b29633514210382aa5f3cfd66de980e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e720598323011a970a0e06f1d41965f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\89e596ebf43b1dbbbb42cb44a2aa0f1e\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9bae62f918c79a50a2afe899642c7b83\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\433a447b623505e7bfb53d51b85c55a2\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e4d68544ef3b568b5636125eb743808\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e60b9f8b65ff5d0d3690c967a8fc459b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a0131a85ed38d09f0f70b41b154b388\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c1617a116917964ea202899a142ae129\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\4c23147b0932287a7a28448a50f1785d\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\58db176b82f12f7c23b8b67cf9baa82c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1b07acaf295c6d81572c63d8d6b44936\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb7245843a35542f1c46cc4bcc038ae\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9faddc12b4375d81ca9618be2077d4eb\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5c7cd714341ca1665b7498b28d2d66b\transformed\fragment-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2e564b0fdf56bef138528d62a64ff42\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76241a0f367aa123de6300cf3b364aa4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3cd0824ea124b81ca8f547ab361f990c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\972fbbbbddcb6b5d4c6e1f7bf21bdacf\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\820e46c87cf5b0c2078bc5e577f87474\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a1227758cacd92d107d7b31106cf4cf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f8cdd8f36058e9a21e710b4a674385d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4dcfd1e981820e4c55bf21c2bfd2941a\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3d0a96145352cfe522dbaa8a6897ab93\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c87ccc46a42e4b91a08431e11d609b1f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3944c43952836de719334faa2ffab8dc\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55f326db8455ef50bf388165c0a22c79\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4656a9a6a353c65596a09f3aa7c3e6b8\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6b63ab947e57e165706b4eb22843c474\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45393922257e8062269c4be3f99a3548\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\a94674afbe5a03fba673e15c1a34395c\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4784fee769f25faf6fa32e7878ad8538\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\4420005cd9cc8ccbfa28427f44f2a9c2\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\658f655ad56fdec7e516531202060d4b\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\4420005cd9cc8ccbfa28427f44f2a9c2\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\4420005cd9cc8ccbfa28427f44f2a9c2\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:6:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:22:5-72
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:22:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:7:22-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:8:22-64
uses-feature#android.hardware.fingerprint
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:5-90
	android:required
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:63-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:11:19-62
application
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:13:5-53:19
INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:13:5-53:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c377da76ffd722c11f7e2a995dabf1f9\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c377da76ffd722c11f7e2a995dabf1f9\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\4fbad6584b625f6cbde5b424d0ce76e0\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\4fbad6584b625f6cbde5b424d0ce76e0\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c31e469962acb98785db8c0d2a661168\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c31e469962acb98785db8c0d2a661168\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7fbf9f8dcec6439dc9000f6ca3bc4a6\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7fbf9f8dcec6439dc9000f6ca3bc4a6\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d6040b0339e72d27b1a9f3cbc100b37\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d6040b0339e72d27b1a9f3cbc100b37\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da59337531a415863d35241726a5e12a\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da59337531a415863d35241726a5e12a\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\743dfef3b5f1b030098eeeb08afd7f3a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\743dfef3b5f1b030098eeeb08afd7f3a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\9772ea6e98fb513fde374d6102af9be0\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:11:5-12:19
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\9772ea6e98fb513fde374d6102af9be0\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5b690fe5611e1aebf99a0fd20c574b4\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5b690fe5611e1aebf99a0fd20c574b4\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb7245843a35542f1c46cc4bcc038ae\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb7245843a35542f1c46cc4bcc038ae\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9faddc12b4375d81ca9618be2077d4eb\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9faddc12b4375d81ca9618be2077d4eb\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f8cdd8f36058e9a21e710b4a674385d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f8cdd8f36058e9a21e710b4a674385d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:20:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:19:9-41
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:22:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:17:9-36
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:21:9-53
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:15:9-65
activity#com.example.budgettracker.MainActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:23:9-32:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:26:13-57
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:27:13-31:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:28:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:30:27-74
activity#com.example.budgettracker.DashboardActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:35:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:34:13-46
activity#com.example.budgettracker.ExpensesActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:37:19-51
activity#com.example.budgettracker.TransactionsActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:9-82
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:57-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:38:19-55
activity#com.example.budgettracker.ProfileActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:9-77
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:52-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:39:19-50
activity#com.example.budgettracker.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:40:19-51
activity#com.example.budgettracker.LanguageActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:41:19-51
activity#com.example.budgettracker.NotificationActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:9-82
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:57-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:42:19-55
activity#com.example.budgettracker.RegisterActivity
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:43:19-51
service#com.example.budgettracker.MyFirebaseMessagingService
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:46:9-52:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:48:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:47:13-55
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:49:13-51:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml:50:25-75
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\87daeb89e7006dad3b2d70792f7dc363\transformed\jetified-viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\87daeb89e7006dad3b2d70792f7dc363\transformed\jetified-viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8cbcc578d44f3eb74db94fab2c6b909f\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8cbcc578d44f3eb74db94fab2c6b909f\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\34a0dba344bab218dae460ae09545d83\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\34a0dba344bab218dae460ae09545d83\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8faebcb1c3a5e3e4cbf4eebb68087685\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8faebcb1c3a5e3e4cbf4eebb68087685\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\transformed\navigation-ui-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\transformed\navigation-ui-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c377da76ffd722c11f7e2a995dabf1f9\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\c377da76ffd722c11f7e2a995dabf1f9\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\4fbad6584b625f6cbde5b424d0ce76e0\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\4fbad6584b625f6cbde5b424d0ce76e0\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\505f45660c762064795deae2c2e196b4\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\505f45660c762064795deae2c2e196b4\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8fe6d8e6447df98388c2372dd5db4cda\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8fe6d8e6447df98388c2372dd5db4cda\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c31e469962acb98785db8c0d2a661168\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c31e469962acb98785db8c0d2a661168\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ecc0e1a6fb1bf17d44fb5bd4b082ceae\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ecc0e1a6fb1bf17d44fb5bd4b082ceae\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\4909f730a9e7806222bd84df864833cf\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\4909f730a9e7806222bd84df864833cf\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\1c641e390a7be6a0bccc310f791f99a5\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\1c641e390a7be6a0bccc310f791f99a5\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6f8046b9f6ebb729ea86cd38f19e22c2\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6f8046b9f6ebb729ea86cd38f19e22c2\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7fbf9f8dcec6439dc9000f6ca3bc4a6\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7fbf9f8dcec6439dc9000f6ca3bc4a6\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d6040b0339e72d27b1a9f3cbc100b37\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d6040b0339e72d27b1a9f3cbc100b37\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\78f8bd9f21c9c9e350b0ae9463cb6254\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\78f8bd9f21c9c9e350b0ae9463cb6254\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da59337531a415863d35241726a5e12a\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\da59337531a415863d35241726a5e12a\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\4eee9fd69195cf7da3f5523c2ce9c927\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\4eee9fd69195cf7da3f5523c2ce9c927\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\743dfef3b5f1b030098eeeb08afd7f3a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\743dfef3b5f1b030098eeeb08afd7f3a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.contentpager:contentpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f71eadc1b8e90646e5ad874b5903c79\transformed\contentpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.contentpager:contentpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4f71eadc1b8e90646e5ad874b5903c79\transformed\contentpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d3d90c764242f30fac5fefd7ddbf3ef\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d3d90c764242f30fac5fefd7ddbf3ef\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\9772ea6e98fb513fde374d6102af9be0\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\9772ea6e98fb513fde374d6102af9be0\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1e820dea617353e0956c65165b54c8b\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1e820dea617353e0956c65165b54c8b\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea784ed1012bd601aa8faf8751b2d9b8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea784ed1012bd601aa8faf8751b2d9b8\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ef09979244db1040cf88eeabe9be1cb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ef09979244db1040cf88eeabe9be1cb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\51674a575b03a3f252817171cb48cb1e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\51674a575b03a3f252817171cb48cb1e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2a3b35c4940c1c2d53fd392389e37fd7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2a3b35c4940c1c2d53fd392389e37fd7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c390d111e0296cb277f7457d019cd712\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c390d111e0296cb277f7457d019cd712\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\db4986a1ffdc5cf11da22a86fc7d5e8c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\db4986a1ffdc5cf11da22a86fc7d5e8c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba365e17b0c92088e8b876778529c00e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba365e17b0c92088e8b876778529c00e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f45219147905cc69ca8d23ceb240d1c0\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f45219147905cc69ca8d23ceb240d1c0\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac93c7f5e634f2bb5a4c72004cb6aa8f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac93c7f5e634f2bb5a4c72004cb6aa8f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\048055b1c844a0d4657ea5603727f4a7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\048055b1c844a0d4657ea5603727f4a7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac808904cd7e03e33d69da647d3102c\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac808904cd7e03e33d69da647d3102c\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b487c08a660904bdeeea450e14c67b6\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b487c08a660904bdeeea450e14c67b6\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\73d2850eb1f9692bf7f675809cde6554\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\73d2850eb1f9692bf7f675809cde6554\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5b690fe5611e1aebf99a0fd20c574b4\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5b690fe5611e1aebf99a0fd20c574b4\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81c1d2dadc3ad1df6d9d5ca28e1dad\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81c1d2dadc3ad1df6d9d5ca28e1dad\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\baa02ee2b336dd28d751a84382ff3f88\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\baa02ee2b336dd28d751a84382ff3f88\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\414810306ab42cdbd7aeac81ddbd0d14\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\414810306ab42cdbd7aeac81ddbd0d14\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b54f3439f91d18bcb99cb17e6de28ef4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b54f3439f91d18bcb99cb17e6de28ef4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\180ea994d7df7386e51a57698ad3d488\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\180ea994d7df7386e51a57698ad3d488\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba1416b4e6ca07f65a70639c28a97d1c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba1416b4e6ca07f65a70639c28a97d1c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\9b29633514210382aa5f3cfd66de980e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\9b29633514210382aa5f3cfd66de980e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e720598323011a970a0e06f1d41965f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e720598323011a970a0e06f1d41965f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\89e596ebf43b1dbbbb42cb44a2aa0f1e\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\89e596ebf43b1dbbbb42cb44a2aa0f1e\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9bae62f918c79a50a2afe899642c7b83\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9bae62f918c79a50a2afe899642c7b83\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\433a447b623505e7bfb53d51b85c55a2\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\433a447b623505e7bfb53d51b85c55a2\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e4d68544ef3b568b5636125eb743808\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e4d68544ef3b568b5636125eb743808\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e60b9f8b65ff5d0d3690c967a8fc459b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e60b9f8b65ff5d0d3690c967a8fc459b\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a0131a85ed38d09f0f70b41b154b388\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a0131a85ed38d09f0f70b41b154b388\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c1617a116917964ea202899a142ae129\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c1617a116917964ea202899a142ae129\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\4c23147b0932287a7a28448a50f1785d\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\4c23147b0932287a7a28448a50f1785d\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\58db176b82f12f7c23b8b67cf9baa82c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\58db176b82f12f7c23b8b67cf9baa82c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1b07acaf295c6d81572c63d8d6b44936\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1b07acaf295c6d81572c63d8d6b44936\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb7245843a35542f1c46cc4bcc038ae\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8eb7245843a35542f1c46cc4bcc038ae\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9faddc12b4375d81ca9618be2077d4eb\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9faddc12b4375d81ca9618be2077d4eb\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5c7cd714341ca1665b7498b28d2d66b\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5c7cd714341ca1665b7498b28d2d66b\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2e564b0fdf56bef138528d62a64ff42\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2e564b0fdf56bef138528d62a64ff42\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76241a0f367aa123de6300cf3b364aa4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76241a0f367aa123de6300cf3b364aa4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3cd0824ea124b81ca8f547ab361f990c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3cd0824ea124b81ca8f547ab361f990c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\972fbbbbddcb6b5d4c6e1f7bf21bdacf\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\972fbbbbddcb6b5d4c6e1f7bf21bdacf\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\820e46c87cf5b0c2078bc5e577f87474\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\820e46c87cf5b0c2078bc5e577f87474\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a1227758cacd92d107d7b31106cf4cf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a1227758cacd92d107d7b31106cf4cf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f8cdd8f36058e9a21e710b4a674385d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f8cdd8f36058e9a21e710b4a674385d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4dcfd1e981820e4c55bf21c2bfd2941a\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4dcfd1e981820e4c55bf21c2bfd2941a\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3d0a96145352cfe522dbaa8a6897ab93\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3d0a96145352cfe522dbaa8a6897ab93\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c87ccc46a42e4b91a08431e11d609b1f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c87ccc46a42e4b91a08431e11d609b1f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3944c43952836de719334faa2ffab8dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3944c43952836de719334faa2ffab8dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55f326db8455ef50bf388165c0a22c79\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55f326db8455ef50bf388165c0a22c79\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4656a9a6a353c65596a09f3aa7c3e6b8\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4656a9a6a353c65596a09f3aa7c3e6b8\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6b63ab947e57e165706b4eb22843c474\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6b63ab947e57e165706b4eb22843c474\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45393922257e8062269c4be3f99a3548\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45393922257e8062269c4be3f99a3548\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\a94674afbe5a03fba673e15c1a34395c\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\a94674afbe5a03fba673e15c1a34395c\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4784fee769f25faf6fa32e7878ad8538\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4784fee769f25faf6fa32e7878ad8538\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\4420005cd9cc8ccbfa28427f44f2a9c2\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\4420005cd9cc8ccbfa28427f44f2a9c2\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\658f655ad56fdec7e516531202060d4b\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\658f655ad56fdec7e516531202060d4b\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\New folder\BudgetTracker\app\src\main\AndroidManifest.xml
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\68670c8646fcaa8caaac8087bdb8b46a\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:22-71
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a56e92f8de50bf09dc18d8b3a8699c01\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e5d6ed39f0437b9e2a4f0fc42b646b95\transformed\jetified-firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1e848c391980576a782a6cf8e6d2d645\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2699a329bcfc4e2352aad3337847a1e7\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45702e0de63464fe5255bf12bd3b9c5b\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8444237135ed47778dcec606f5a35290\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7815e567730fd73855a7ee5dc5a11459\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a832c32936695b30afd15e34313bad76\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\774cb13b4894098db0cc22d575e830f2\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:25-85
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\a05b3df24fdad4adf6500e1e168a44f1\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ea258434737f5fd79e78b777593fbf\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc2e842892f65ccd3bc06aa032016c19\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\24da3e80982647016081752b11f5cf0e\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2e06efe9c3a48810da60dfd783c009ad\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1d850f11dc4549357b4bbae32a9ea9a\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\5e6cb0677104d92c5efdb2f091984aa9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f7072ebdd5cd7e112e6eb4fca418b446\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c481deb13e95af8c1cf10d239f737420\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d818f76de2980d2affef70b926d025\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\589cd360cb3cd94886a25769e80f8db6\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfa5f7d410dd049d55ecf9d5f6d4aadb\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\1ffe2ec03d9778dd24a99acf4ca73e8b\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\a02329c85762e49c24a8109a8f6b0383\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
