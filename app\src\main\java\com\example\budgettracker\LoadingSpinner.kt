package com.example.budgettracker

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout

class LoadingSpinner(context: Context, private val rootLayout: RelativeLayout) {
    private val spinnerView: View

    init {
        val inflater = LayoutInflater.from(context)
        spinnerView = inflater.inflate(R.layout.spinner, rootLayout, false)
        rootLayout.addView(spinnerView)
        spinnerView.visibility = View.GONE
    }

    fun show() {
        spinnerView.visibility = View.VISIBLE
        setAllViewsEnabled(rootLayout, false)
    }

    fun hide() {
        spinnerView.visibility = View.GONE
        setAllViewsEnabled(rootLayout, true)
    }

    private fun setAllViewsEnabled(view: View, enabled: Boolean) {
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val child = view.getChildAt(i)
                child.isEnabled = enabled
                if (child is ViewGroup) {
                    setAllViewsEnabled(child, enabled)
                }
            }
        } else {
            view.isEnabled = enabled
        }
    }
}
