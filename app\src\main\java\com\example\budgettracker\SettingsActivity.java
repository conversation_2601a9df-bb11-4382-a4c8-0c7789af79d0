package com.example.budgettracker;
import android.annotation.SuppressLint;
import android.app.DatePickerDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.os.Bundle;
import android.util.*;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.database.sqlite.SQLiteDatabase;
import android.content.ContentValues;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Calendar;
import java.util.Map;

import retrofit2.Call;

public class SettingsActivity extends AppCompatActivity{
    private TextView dateTextView, balanceTextView, targetTextView, budgetTextView;
    private DatabaseHelper dbHelper;
    private EditText editText4;
    private SQLiteDatabase database;
    private Calendar calendar;
    private SimpleDateFormat dateFormat;
    public String user_id;
    private LoadingSpinner loadingSpinner;

    public void setupNavigationButtons() {
        ImageButton homeButton = findViewById(R.id.button_home);
        ImageButton expensesButton = findViewById(R.id.button_expenses);
        ImageButton transactionsButton = findViewById(R.id.button_transactions);
        ImageButton profileButton = findViewById(R.id.button_profile);
        ImageButton NotificationButton = findViewById(R.id.button_notification);


        homeButton.setOnClickListener(v -> navigateTo(DashboardActivity.class));
        expensesButton.setOnClickListener(v -> navigateTo(ExpensesActivity.class));
        transactionsButton.setOnClickListener(v -> navigateTo(TransactionsActivity.class));
        profileButton.setOnClickListener(v -> navigateTo(SettingsActivity.class));
        NotificationButton.setOnClickListener(v -> navigateTo(NotificationActivity.class));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.settings);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);
        loadingSpinner = new LoadingSpinner(this, rootLayout);

        dbHelper = new DatabaseHelper(this);
        SessionManager sessionManager = new SessionManager(this);
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid();
        }
        editText4 = findViewById(R.id.editText4);
        Button saveButton = findViewById(R.id.save_button);
        Button datePickerButton = findViewById(R.id.date_picker_button);
        Button clearDataButton = findViewById(R.id.buttonClearUserData);
        dateTextView = findViewById(R.id.title_date);
        balanceTextView = findViewById(R.id.trans_balance);
        targetTextView = findViewById(R.id.trans_target);
        budgetTextView = findViewById(R.id.trans_budget);
        @SuppressLint("CutPasteId") Button languageSettingsButton = findViewById(R.id.buttonLanguageSettings);
        @SuppressLint("CutPasteId") Button clearUserDataButton = findViewById(R.id.buttonClearUserData);

        calendar = Calendar.getInstance();
        dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        updateDateLabel();
        if (NetworkUtils.isInternetAvailable(this)) {fetchDataForDatOnline(dateFormat.format(calendar.getTime()));
        }else{fetchDataForDate(dateFormat.format(calendar.getTime()));}

        datePickerButton.setOnClickListener(v -> showDatePickerDialog());

        languageSettingsButton.setOnClickListener(v -> {Intent intent = new Intent(SettingsActivity.this, LanguageActivity.class);startActivity(intent);});

        // Setup clear user data button
        clearUserDataButton.setOnClickListener(v -> showClearDataConfirmation());

        // Setup navigation buttons
        setupNavigationButtons();

        ImageButton backButton = findViewById(R.id.back_button);
        backButton.setOnClickListener(v -> {
            Intent intent = new Intent(SettingsActivity.this, DashboardActivity.class);
            startActivity(intent);
        });

        saveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                handleSaveAction();
            }
        });
        editText4.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN) {
                    v.onKeyDown(keyCode, event);
                    handleSaveAction();
                    return true;
                }
                return false;
            }
        });
    }
    private void navigateTo(Class<?> destination) {

        Intent intent = new Intent(SettingsActivity.this, destination);
        startActivity(intent);
        finish();
    }
    private void showDatePickerDialog() {
        new DatePickerDialog(SettingsActivity.this, (view, year, month, dayOfMonth) -> {
            calendar.set(year, month, dayOfMonth);
            updateDateLabel();

            if (NetworkUtils.isInternetAvailable(this)) {
                fetchDataForDatOnline(dateFormat.format(calendar.getTime()));
            }else{
                fetchDataForDate(dateFormat.format(calendar.getTime()));
            }
        }, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH)).show();
    }
    private void fetchDataForDate(String date) {
        SQLiteDatabase db = dbHelper.getReadableDatabase(); // Use getReadableDatabase() for read operations
        Cursor cursor = null;
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM", Locale.getDefault());
            Date parsedDate = inputFormat.parse(date);
            String monthYear = outputFormat.format(parsedDate);

            cursor = db.rawQuery(
                    "SELECT trans_balance, trans_target, trans_budget FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?",
                    new String[]{monthYear, String.valueOf(user_id)}
            );
            if (cursor.moveToFirst()) {
                balanceTextView.setText(String.format(Locale.getDefault(), "%.2f", cursor.getDouble(0)));
                targetTextView.setText(String.format(Locale.getDefault(), "%.2f", cursor.getDouble(1)));
                budgetTextView.setText(String.format(Locale.getDefault(), "%.2f", cursor.getDouble(2)));
            } else {
                balanceTextView.setText("0.00");
                targetTextView.setText("0.00");
                budgetTextView.setText("0.00");
            }
        } catch (ParseException e) {
            Log.w("DatabaseHelper", "Error parsing date: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }
    @SuppressLint("SetTextI18n")
    private void fetchDataForDatOnline(String date) {
        loadingSpinner.show();
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM", Locale.getDefault());
        Date parsedDate;
        try {
            parsedDate = inputFormat.parse(date);
            if (parsedDate != null) {
                String monthYear = outputFormat.format(parsedDate);
                Call<List<Record>> call = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));
                call.enqueue(new retrofit2.Callback<List<Record>>() {
                    @Override
                    public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            List<Record> records = response.body();
                            double balance = 0.00;
                            double target = 0.00;
                            double budget = 0.00;

                            for (Record record : records) {
                                if (record instanceof Transaction) {
                                    Transaction transaction = (Transaction) record;
                                    balance = transaction.getBalance();
                                    target = transaction.getTransTarget();
                                    budget = transaction.getTotal();
                                    break;
                                }}
                            balanceTextView.setText(String.format(Locale.getDefault(), "%.2f", balance));
                            targetTextView.setText(String.format(Locale.getDefault(), "%.2f", target));
                            budgetTextView.setText(String.format(Locale.getDefault(), "%.2f", budget));
                            loadingSpinner.hide();
                        } else {
                            balanceTextView.setText("0.00");
                            targetTextView.setText("0.00");
                            budgetTextView.setText("0.00");
                            loadingSpinner.hide();
                            Toast.makeText(SettingsActivity.this, "No data found", Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                        loadingSpinner.hide();
                        Toast.makeText(SettingsActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                });
            }
        } catch (ParseException e) {
            loadingSpinner.hide();
            balanceTextView.setText("0.00");
            targetTextView.setText("0.00");
            budgetTextView.setText("0.00");
        }
    }




    @Override
    public boolean onKeyDown(int keyCode, @NonNull KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            startActivity(new Intent(SettingsActivity.this, MainActivity.class));
            finish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
    private void updateDateLabel() {
        dateTextView.setText(dateFormat.format(calendar.getTime()));
    }
     private void handleSaveAction() {

         String targetStr = editText4.getText().toString();
         int targetAmount;
         if (targetStr.isEmpty()) {Toast.makeText(SettingsActivity.this, "Please enter a target amount", Toast.LENGTH_SHORT).show();return;}
//         final int targetAmount = Integer.parseInt(targetStr);
         try {targetAmount = Integer.parseInt(targetStr);
         } catch (NumberFormatException e) {Toast.makeText(SettingsActivity.this, "Invalid target amount. Please enter a valid number.", Toast.LENGTH_SHORT).show();return;}

         String monthYear = new SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(calendar.getTime());
         if (NetworkUtils.isInternetAvailable(this)) { checkExistingOnline(monthYear, targetAmount);
         }else{checkExistingTarget(monthYear, targetAmount);}

     }

     private void checkExistingTarget(final String monthYear, final int newTargetAmount) {
         SQLiteDatabase db = dbHelper.getReadableDatabase();
         String query = "SELECT COUNT(*) FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?";
         Cursor cursor = db.rawQuery(query, new String[]{monthYear, String.valueOf(user_id)});
         if (cursor.moveToFirst()) {
             int count = cursor.getInt(0);
             cursor.close();
             if (count > 0) {
                 new AlertDialog.Builder(this)
                         .setTitle("Confirmation")
                         .setMessage("A target for this month already exists. Do you want to remove the existing target and add the new one?")
                         .setPositiveButton("Yes", (dialog, which) -> updateTarget(monthYear, newTargetAmount))
                         .setNegativeButton("No", null)
                         .show();
             } else {
                 updateTarget(monthYear, newTargetAmount);
             }
         }
     }

    private void checkExistingOnline(final String monthYear, final int newTargetAmount) {
        loadingSpinner.show();
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        Call<List<Record>> call = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));
        call.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Record> records = response.body();
                    boolean targetExists = false;

                    for (Record record : records) {
                        if (record instanceof Transaction) {
                            targetExists = true;
                            break;
                        }
                    }

                    if (targetExists) {
                        new AlertDialog.Builder(SettingsActivity.this)
                                .setTitle("Confirmation")
                                .setMessage("A target for this month already exists. Do you want to remove the existing target and add the new one?")
                                .setPositiveButton("Yes", (dialog, which) -> updateTargetOnilne(monthYear, newTargetAmount))
//                                .setNegativeButton("No", null)
                                .setNegativeButton("No", (dialog, which) -> {loadingSpinner.hide();})
                                .show();
                    } else {
                        updateTargetOnilne(monthYear, newTargetAmount);
                    }
                } else {
                    loadingSpinner.hide();
                    Toast.makeText(SettingsActivity.this, "Failed to check existing target", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                Log.e("Trialx", String.valueOf(t));
                Toast.makeText(SettingsActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }
     private void updateTarget(String monthYear, int newTargetAmount) {
         SQLiteDatabase db = dbHelper.getWritableDatabase();
         String date = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(calendar.getTime());
         ContentValues values = new ContentValues();
         values.put("trans_target", newTargetAmount);
         values.put("trans_month", date);
         int rowsUpdated = db.update("transactions", values, " strftime('%Y-%m', trans_month)  LIKE ? AND user_id = ?", new String[]{monthYear,String.valueOf(user_id)});
         if (rowsUpdated == 0) {
             values.put("user_id",user_id);
             long newRowId = db.insert("transactions", null, values);
             if (newRowId == -1) {
                 Toast.makeText(SettingsActivity.this, "Error adding new target", Toast.LENGTH_SHORT).show();
             } else {
                 Toast.makeText(SettingsActivity.this, "New target added successfully", Toast.LENGTH_SHORT).show();
             }
         } else {
             Toast.makeText(SettingsActivity.this, "Target updated successfully", Toast.LENGTH_SHORT).show();
         }
         Toast.makeText(SettingsActivity.this, "Target updated successfully"+ rowsUpdated, Toast.LENGTH_SHORT).show();
         db.close();
         loadingSpinner.hide();
         Intent intent = getIntent();
         finish();
         startActivity(intent);
     }
    private void updateTargetOnilne(String monthYear, int newTargetAmount) {
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        Map<String, Object> data = new HashMap<>();
        data.put("trans_target", newTargetAmount);
        data.put("user_id", user_id);
        Call<Void> updateCall = apiService.updateRecord("transactions",user_id,monthYear, data);
        updateCall.enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    loadingSpinner.hide();
                    Toast.makeText(SettingsActivity.this, "Target updated successfully", Toast.LENGTH_SHORT).show();
                }else if (response.code() == 404) {
                    insertNewTargetOnline(monthYear, newTargetAmount);
                } else {
                    loadingSpinner.hide();
                    Toast.makeText(SettingsActivity.this, "Error updating target: " + response.message(), Toast.LENGTH_SHORT).show();
                }

            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                loadingSpinner.hide();
                Toast.makeText(SettingsActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
        loadingSpinner.hide();
        Intent intent = getIntent();
        finish();
        startActivity(intent);
    }

    private void insertNewTargetOnline(String monthYear, int newTargetAmount) {
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        Map<String, Object> data = new HashMap<>();
        data.put("trans_target", newTargetAmount);
        data.put("trans_month", monthYear);
        data.put("user_id", user_id);
        Call<Void> insertCall = apiService.createRecord("transactions",data);
        insertCall.enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    Toast.makeText(SettingsActivity.this, "New target added successfully", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(SettingsActivity.this, "Error adding new target: " + response.message(), Toast.LENGTH_SHORT).show();
                }
                loadingSpinner.hide();
                Intent intent = getIntent();
                finish();
                startActivity(intent);
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                loadingSpinner.hide();
                Toast.makeText(SettingsActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });

    }

    private void showClearDataConfirmation() {
        new AlertDialog.Builder(this)
                .setTitle("Clear All User Data")
                .setMessage("This will permanently delete all your expenses, transactions, and settings. This action cannot be undone. Are you sure?")
                .setPositiveButton("Yes, Clear All", (dialog, which) -> clearAllUserData())
                .setNegativeButton("Cancel", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    private void clearAllUserData() {
        loadingSpinner.show();

        // Clear Firebase data if online and authenticated
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            FirebaseManager firebaseManager = new FirebaseManager();
            if (firebaseManager.isUserAuthenticated()) {
                clearFirebaseData(firebaseManager);
            } else {
                clearLocalDataOnly();
            }
        } else {
            clearLocalDataOnly();
        }
    }

    private void clearFirebaseData(FirebaseManager firebaseManager) {
        // Clear expenses from Firebase
        firebaseManager.clearAllExpenses(new FirebaseManager.FirebaseCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                Log.d("SettingsActivity", "Firebase expenses cleared");
                // Clear transactions from Firebase
                firebaseManager.clearAllTransactions(new FirebaseManager.FirebaseCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        Log.d("SettingsActivity", "Firebase transactions cleared");
                        // Clear notifications from Firebase
                        firebaseManager.clearAllNotifications(new FirebaseManager.FirebaseCallback<Void>() {
                            @Override
                            public void onSuccess(Void result) {
                                Log.d("SettingsActivity", "Firebase notifications cleared");
                                // Finally clear local data
                                clearLocalDataOnly();
                            }

                            @Override
                            public void onFailure(Exception e) {
                                Log.e("SettingsActivity", "Failed to clear Firebase notifications", e);
                                // Still clear local data even if Firebase fails
                                clearLocalDataOnly();
                            }
                        });
                    }

                    @Override
                    public void onFailure(Exception e) {
                        Log.e("SettingsActivity", "Failed to clear Firebase transactions", e);
                        // Still clear local data even if Firebase fails
                        clearLocalDataOnly();
                    }
                });
            }

            @Override
            public void onFailure(Exception e) {
                Log.e("SettingsActivity", "Failed to clear Firebase expenses", e);
                // Still clear local data even if Firebase fails
                clearLocalDataOnly();
            }
        });
    }

    private void clearLocalDataOnly() {
        try {
            // Clear SQLite database
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            // Delete all user data from all tables
            db.delete("expenses", "user_id = ?", new String[]{user_id});
            db.delete("budget", "user_id = ?", new String[]{user_id});
            db.delete("transactions", "user_id = ?", new String[]{user_id});
            db.delete("user", "user_id = ?", new String[]{user_id});

            db.close();

            // Clear session data
            SessionManager sessionManager = new SessionManager(this);
            sessionManager.logoutUser();

            loadingSpinner.hide();

            // Show success message and redirect to login
            Toast.makeText(this, "All user data has been cleared successfully", Toast.LENGTH_LONG).show();

            // Redirect to login screen
            Intent intent = new Intent(SettingsActivity.this, MainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            startActivity(intent);
            finish();

        } catch (Exception e) {
            loadingSpinner.hide();
            Log.e("SettingsActivity", "Error clearing local data", e);
            Toast.makeText(this, "Error clearing data: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

}
