<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="base" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\base.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/base_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="75" endOffset="14"/></Target><Target id="@+id/button_home" view="ImageButton"><Expressions/><location startLine="11" startOffset="4" endLine="21" endOffset="32"/></Target><Target id="@+id/button_expenses" view="ImageButton"><Expressions/><location startLine="24" startOffset="4" endLine="34" endOffset="32"/></Target><Target id="@+id/button_transactions" view="ImageButton"><Expressions/><location startLine="37" startOffset="4" endLine="47" endOffset="32"/></Target><Target id="@+id/button_notification" view="ImageButton"><Expressions/><location startLine="50" startOffset="4" endLine="60" endOffset="32"/></Target><Target id="@+id/button_profile" view="ImageButton"><Expressions/><location startLine="63" startOffset="4" endLine="73" endOffset="32"/></Target></Targets></Layout>