<lint-module
    format="1"
    dir="C:\Users\<USER>\Desktop\New folder\BudgetTracker\app"
    name=":app"
    type="APP"
    maven="BudgetTracker:app:unspecified"
    agpVersion="8.5.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
