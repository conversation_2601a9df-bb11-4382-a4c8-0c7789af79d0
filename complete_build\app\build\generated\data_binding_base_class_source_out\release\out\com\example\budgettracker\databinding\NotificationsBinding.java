// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NotificationsBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final Button clearAllButton;

  @NonNull
  public final LinearLayout emptyState;

  @NonNull
  public final BaseBinding footer;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final RecyclerView notificationsRecyclerView;

  private NotificationsBinding(@NonNull RelativeLayout rootView, @NonNull ImageButton backButton,
      @NonNull Button clearAllButton, @NonNull LinearLayout emptyState, @NonNull BaseBinding footer,
      @NonNull LinearLayout headerLayout, @NonNull RecyclerView notificationsRecyclerView) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.clearAllButton = clearAllButton;
    this.emptyState = emptyState;
    this.footer = footer;
    this.headerLayout = headerLayout;
    this.notificationsRecyclerView = notificationsRecyclerView;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static NotificationsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NotificationsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.notifications, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NotificationsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.clear_all_button;
      Button clearAllButton = ViewBindings.findChildViewById(rootView, id);
      if (clearAllButton == null) {
        break missingId;
      }

      id = R.id.empty_state;
      LinearLayout emptyState = ViewBindings.findChildViewById(rootView, id);
      if (emptyState == null) {
        break missingId;
      }

      id = R.id.footer;
      View footer = ViewBindings.findChildViewById(rootView, id);
      if (footer == null) {
        break missingId;
      }
      BaseBinding binding_footer = BaseBinding.bind(footer);

      id = R.id.header_layout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.notifications_recycler_view;
      RecyclerView notificationsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (notificationsRecyclerView == null) {
        break missingId;
      }

      return new NotificationsBinding((RelativeLayout) rootView, backButton, clearAllButton,
          emptyState, binding_footer, headerLayout, notificationsRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
