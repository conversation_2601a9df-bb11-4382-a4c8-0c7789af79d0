package com.example.budgettracker

import android.content.ContentValues
import android.content.Intent
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import java.text.SimpleDateFormat
import java.util.*

class RegisterActivity : AppCompatActivity() {

    private lateinit var emailEditText: EditText
    private lateinit var passwordEditText: EditText
    private lateinit var confirmPasswordEditText: EditText
    private lateinit var usernameEdit: EditText
    private lateinit var applyButton: Button
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var mAuth: FirebaseAuth
    private lateinit var firebaseManager: FirebaseManager
    private var isPasswordVisible = false
    private lateinit var loadingSpinner: LoadingSpinner

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.register)
        
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        dbHelper = DatabaseHelper(this)
        mAuth = FirebaseAuth.getInstance()
        firebaseManager = FirebaseManager()

        emailEditText = findViewById(R.id.email_input)
        passwordEditText = findViewById(R.id.password_input)
        confirmPasswordEditText = findViewById(R.id.password_input_confrim)
        usernameEdit = findViewById(R.id.username_input)
        val passwordEyeImageView = findViewById<ImageView>(R.id.password_eye)
        val confirmPasswordEyeImageView = findViewById<ImageView>(R.id.password_eye_confrim)
        applyButton = findViewById(R.id.signup_button)
        
        applyButton.setOnClickListener {
            registerUser()
//            Handler(Looper.getMainLooper()).postDelayed({ loadingSpinner.hide() }, 2000)
        }

        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@RegisterActivity, MainActivity::class.java)
            startActivity(intent)
        }

        passwordEyeImageView.setOnClickListener { togglePasswordVisibility(passwordEditText, passwordEyeImageView) }
        confirmPasswordEyeImageView.setOnClickListener { togglePasswordVisibility(confirmPasswordEditText, confirmPasswordEyeImageView) }
    }

    private fun togglePasswordVisibility(passwordEditText: EditText, eyeImageView: ImageView) {
        if (isPasswordVisible) {
            passwordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
            eyeImageView.setImageResource(R.drawable.eye_close)
        } else {
            passwordEditText.transformationMethod = HideReturnsTransformationMethod.getInstance()
            eyeImageView.setImageResource(R.drawable.eye_icon)
        }
        isPasswordVisible = !isPasswordVisible
        passwordEditText.setSelection(passwordEditText.text.length) // Move cursor to end
    }

    private fun registerUser() {
        val email = emailEditText.text.toString().trim()
        val password = passwordEditText.text.toString().trim()
        val confirmPassword = confirmPasswordEditText.text.toString().trim()
        val username = usernameEdit.text.toString().trim()
        
        if (TextUtils.isEmpty(email)) {
            emailEditText.error = "Email is required"
            return
        }
        if (TextUtils.isEmpty(username)) {
            usernameEdit.error = "Username is required"
            return
        }
        if (TextUtils.isEmpty(password)) {
            passwordEditText.error = "Password is required"
            return
        }
        if (password != confirmPassword) {
            confirmPasswordEditText.error = "Passwords do not match"
            return
        }

        mAuth.createUserWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    val user: FirebaseUser? = mAuth.currentUser
                    if (user != null) {
                        if (NetworkUtils.isInternetAvailable(this)) {
                            saveUserFirebase(email, username, user.uid)
                        } else {
                            saveUserData(email, username, user.uid)
                        }
                        Handler(Looper.getMainLooper()).postDelayed({
                            val intent = Intent(this@RegisterActivity, MainActivity::class.java)
                            startActivity(intent)
                            finish()
                        }, 2000)
                    }
                } else {
                    Toast.makeText(this@RegisterActivity, "Registration failed: ${task.exception?.message}", Toast.LENGTH_SHORT).show()
                    loadingSpinner.hide()
                }
            }
    }

    private fun saveUserData(email: String, username: String, uid: String) {
        val db: SQLiteDatabase = dbHelper.writableDatabase
        val values = ContentValues().apply {
            put("user_email", email)
            put("user_name", username)
            put("user_id", uid)
            put("user_status", 0)
        }

        val newRowId = db.insert("user", null, values)
        if (newRowId == -1L) {
            loadingSpinner.hide()
            Toast.makeText(this@RegisterActivity, "Error saving user data", Toast.LENGTH_SHORT).show()
        } else {
            loadingSpinner.hide()
            Toast.makeText(this@RegisterActivity, "User data saved successfully", Toast.LENGTH_SHORT).show()
        }
        db.close()
    }

    private fun saveUserFirebase(email: String, username: String, uid: String) {
        firebaseManager.saveUser(email, username, uid, object : FirebaseManager.FirebaseCallback<Void> {
            override fun onSuccess(result: Void?) {
                // Also save locally for offline access
                saveUserData(email, username, uid)
                Toast.makeText(this@RegisterActivity, "Account created successfully", Toast.LENGTH_SHORT).show()
                loadingSpinner.hide()
            }

            override fun onFailure(e: Exception) {
                // Fallback to local storage
                saveUserData(email, username, uid)
                Toast.makeText(this@RegisterActivity, "Account created. Will sync when online.", Toast.LENGTH_SHORT).show()
                loadingSpinner.hide()
            }
        })
    }
}
