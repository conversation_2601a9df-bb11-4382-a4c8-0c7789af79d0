<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="notifications" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\notifications.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/notifications_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="16"/></Target><Target id="@+id/footer" tag="layout/notifications_0" include="base"><Expressions/><location startLine="112" startOffset="4" endLine="117" endOffset="49"/></Target><Target id="@+id/header_layout" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="49" endOffset="18"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="19" startOffset="8" endLine="27" endOffset="45"/></Target><Target id="@+id/clear_all_button" view="Button"><Expressions/><location startLine="40" startOffset="8" endLine="47" endOffset="54"/></Target><Target id="@+id/notifications_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="66" startOffset="12" endLine="70" endOffset="52"/></Target><Target id="@+id/empty_state" view="LinearLayout"><Expressions/><location startLine="73" startOffset="12" endLine="107" endOffset="26"/></Target></Targets></Layout>