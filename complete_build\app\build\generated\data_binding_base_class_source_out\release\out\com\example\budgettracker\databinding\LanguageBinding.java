// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LanguageBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final BaseBinding footer;

  @NonNull
  public final RadioButton radioAfrikaans;

  @NonNull
  public final RadioButton radioEnglish;

  @NonNull
  public final RadioGroup radioGroup;

  @NonNull
  public final RadioButton radioZulu;

  private LanguageBinding(@NonNull RelativeLayout rootView, @NonNull ImageButton backButton,
      @NonNull BaseBinding footer, @NonNull RadioButton radioAfrikaans,
      @NonNull RadioButton radioEnglish, @NonNull RadioGroup radioGroup,
      @NonNull RadioButton radioZulu) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.footer = footer;
    this.radioAfrikaans = radioAfrikaans;
    this.radioEnglish = radioEnglish;
    this.radioGroup = radioGroup;
    this.radioZulu = radioZulu;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LanguageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LanguageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.language, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LanguageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.footer;
      View footer = ViewBindings.findChildViewById(rootView, id);
      if (footer == null) {
        break missingId;
      }
      BaseBinding binding_footer = BaseBinding.bind(footer);

      id = R.id.radio_afrikaans;
      RadioButton radioAfrikaans = ViewBindings.findChildViewById(rootView, id);
      if (radioAfrikaans == null) {
        break missingId;
      }

      id = R.id.radio_english;
      RadioButton radioEnglish = ViewBindings.findChildViewById(rootView, id);
      if (radioEnglish == null) {
        break missingId;
      }

      id = R.id.radio_group;
      RadioGroup radioGroup = ViewBindings.findChildViewById(rootView, id);
      if (radioGroup == null) {
        break missingId;
      }

      id = R.id.radio_zulu;
      RadioButton radioZulu = ViewBindings.findChildViewById(rootView, id);
      if (radioZulu == null) {
        break missingId;
      }

      return new LanguageBinding((RelativeLayout) rootView, backButton, binding_footer,
          radioAfrikaans, radioEnglish, radioGroup, radioZulu);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
