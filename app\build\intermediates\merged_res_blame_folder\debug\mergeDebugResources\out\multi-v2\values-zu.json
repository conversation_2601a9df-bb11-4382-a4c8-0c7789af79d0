{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-58:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "93,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "7937,8862,8970,9082", "endColumns": "111,107,111,111", "endOffsets": "8044,8965,9077,9189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "16276,16389", "endColumns": "112,120", "endOffsets": "16384,16505"}}, {"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-zu\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,100,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,536,576,613,648,683,718,753,788,3516,3577,3636,3675,3747,3802,3853,4138,4186,4935,4998,5286,5339,5469,8049,8207,8252,8307,8433,10165,10667,11151,11207,11397,11440,11508,11576,11617,12666,12704,16510,16554,16604,16751,16798,16858,17027,17162,17229,17288,17441,17483,17521,17582,17632,17696,18563,18620,18686,18751,18828,18946", "endLines": "8,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "endColumns": "19,39,36,34,34,34,34,34,41,60,58,38,71,54,50,47,47,50,62,49,52,48,68,62,44,54,58,57,99,65,55,51,42,67,67,40,48,37,57,43,49,64,46,59,38,52,66,58,51,41,37,60,49,63,39,56,65,64,76,117,85", "endOffsets": "360,571,608,643,678,713,748,783,825,3572,3631,3670,3742,3797,3848,3896,4181,4232,4993,5043,5334,5383,5533,8107,8247,8302,8361,8486,10260,10728,11202,11254,11435,11503,11571,11612,11661,12699,12757,16549,16599,16664,16793,16853,16892,17075,17224,17283,17335,17478,17516,17577,17627,17691,17731,18615,18681,18746,18823,18941,19027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "17340", "endColumns": "100", "endOffsets": "17436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zu\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "6595", "endColumns": "131", "endOffsets": "6722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "9,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "365,4521,4599,4676,4753,4847,5048,5160,5388,8366,8491,11259,11334,11666,11758,11829,11894,11961,12033,12105,12159,12280,12339,12403,12457,12534,12762,12847,12928,13077,13164,13247,13389,13481,13559,13615,13673,13739,13811,13888,13979,14062,14142,14221,14296,14375,14479,14569,14642,14736,14833,14907,14980,15079,15134,15218,15286,15374,15463,15525,15589,15652,15723,15832,15943,16046,16154,16214,16669", "endLines": "12,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "531,4594,4671,4748,4842,4930,5155,5281,5464,8428,8589,11329,11392,11753,11824,11889,11956,12028,12100,12154,12275,12334,12398,12452,12529,12661,12842,12923,13072,13159,13242,13384,13476,13554,13610,13668,13734,13806,13883,13974,14057,14137,14216,14291,14370,14474,14564,14637,14731,14828,14902,14975,15074,15129,15213,15281,15369,15458,15520,15584,15647,15718,15827,15938,16041,16149,16209,16271,16746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zu\\values-zu.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "55,56", "startColumns": "4,4", "startOffsets": "3901,4015", "endColumns": "113,122", "endOffsets": "4010,4133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-zu\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5538,5649,5825,5960,6064,6232,6361,6485,6727,6887,6997,7162,7293,7451,7608,7669,7738", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "5644,5820,5955,6059,6227,6356,6480,6590,6882,6992,7157,7288,7446,7603,7664,7733,7818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "830,938,1045,1157,1245,1348,1463,1542,1619,1710,1803,1898,1992,2092,2185,2280,2374,2465,2558,2639,2743,2846,2944,3051,3158,3263,3420,17080", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "933,1040,1152,1240,1343,1458,1537,1614,1705,1798,1893,1987,2087,2180,2275,2369,2460,2553,2634,2738,2841,2939,3046,3153,3258,3415,3511,17157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-zu\\values-zu.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "59,60,92,95,102,103,107,108,109,110,111,112,113,115,116,117,119,120,121,197,210,211,212,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4237,4395,7823,8112,8594,8747,9194,9320,9443,9599,9736,9889,10021,10265,10359,10532,10733,10883,11029,16897,17736,17837,17976,18065,18198,18307,18454", "endColumns": "157,125,113,94,152,114,125,122,155,136,152,131,143,93,172,134,149,145,121,129,100,138,88,132,108,146,108", "endOffsets": "4390,4516,7932,8202,8742,8857,9315,9438,9594,9731,9884,10016,10160,10354,10527,10662,10878,11024,11146,17022,17832,17971,18060,18193,18302,18449,18558"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.example.budgettracker.app-mergeDebugResources-58:\\values-zu\\values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "93,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "7942,8867,8975,9087", "endColumns": "111,107,111,111", "endOffsets": "8049,8970,9082,9194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,120", "endOffsets": "163,284"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "16281,16394", "endColumns": "112,120", "endOffsets": "16389,16510"}}, {"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-zu\\strings.xml", "from": {"startLines": "62,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3403,1440,1753,1791,1863,1827,1935,1899,1641,1579,111,2862,745,1324,2902,3213,57,2712,2254,818,453,2511,3076,2318,3262,995,1051,560,2561,171,2764,507,3308,3007,1684,1111,2662,1971,2010,306,3352,1153,351,684,1481,399,238,1380,2954,1281,869,1219,2382,619,2821,1521,3146,2069,2433,2135,908", "endLines": "68,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "endColumns": "19,39,36,34,34,34,34,34,41,60,58,38,71,54,50,47,52,50,62,49,52,48,68,62,44,54,58,57,99,65,55,51,42,67,67,40,48,37,57,43,49,64,46,59,38,52,66,58,51,41,37,60,49,63,39,56,65,64,76,117,85", "endOffsets": "3619,1475,1785,1821,1893,1857,1965,1929,1678,1635,165,2896,812,1374,2948,3256,105,2758,2312,863,501,2555,3140,2376,3302,1045,1105,613,2656,232,2815,554,3346,3070,1747,1147,2706,2004,2063,345,3397,1213,393,739,1515,447,300,1434,3001,1318,902,1275,2427,678,2856,1573,3207,2129,2505,2248,989"}, "to": {"startLines": "2,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,536,576,613,648,683,718,753,788,3516,3577,3636,3675,3747,3802,3853,4138,4191,4940,5003,5291,5344,5474,8054,8212,8257,8312,8438,10170,10672,11156,11212,11402,11445,11513,11581,11622,12671,12709,16515,16559,16609,16756,16803,16863,17032,17167,17234,17293,17446,17488,17526,17587,17637,17701,18568,18625,18691,18756,18833,18951", "endLines": "8,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "endColumns": "19,39,36,34,34,34,34,34,41,60,58,38,71,54,50,47,52,50,62,49,52,48,68,62,44,54,58,57,99,65,55,51,42,67,67,40,48,37,57,43,49,64,46,59,38,52,66,58,51,41,37,60,49,63,39,56,65,64,76,117,85", "endOffsets": "360,571,608,643,678,713,748,783,825,3572,3631,3670,3742,3797,3848,3896,4186,4237,4998,5048,5339,5388,5538,8112,8252,8307,8366,8491,10265,10733,11207,11259,11440,11508,11576,11617,11666,12704,12762,16554,16604,16669,16798,16858,16897,17080,17229,17288,17340,17483,17521,17582,17632,17696,17736,18620,18686,18751,18828,18946,19032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "17345", "endColumns": "100", "endOffsets": "17441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "6600", "endColumns": "131", "endOffsets": "6727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1071,1174,1249,1312,1404,1475,1540,1607,1679,1751,1805,1926,1985,2049,2103,2180,2312,2397,2478,2627,2714,2797,2939,3031,3109,3165,3223,3289,3361,3438,3529,3612,3692,3771,3846,3925,4029,4119,4192,4286,4383,4457,4530,4629,4684,4768,4836,4924,5013,5075,5139,5202,5273,5382,5493,5596,5704,5764,5826", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "266,344,421,498,592,680,792,918,999,1066,1169,1244,1307,1399,1470,1535,1602,1674,1746,1800,1921,1980,2044,2098,2175,2307,2392,2473,2622,2709,2792,2934,3026,3104,3160,3218,3284,3356,3433,3524,3607,3687,3766,3841,3920,4024,4114,4187,4281,4378,4452,4525,4624,4679,4763,4831,4919,5008,5070,5134,5197,5268,5377,5488,5591,5699,5759,5821,5903"}, "to": {"startLines": "9,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "365,4526,4604,4681,4758,4852,5053,5165,5393,8371,8496,11264,11339,11671,11763,11834,11899,11966,12038,12110,12164,12285,12344,12408,12462,12539,12767,12852,12933,13082,13169,13252,13394,13486,13564,13620,13678,13744,13816,13893,13984,14067,14147,14226,14301,14380,14484,14574,14647,14741,14838,14912,14985,15084,15139,15223,15291,15379,15468,15530,15594,15657,15728,15837,15948,16051,16159,16219,16674", "endLines": "12,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "531,4599,4676,4753,4847,4935,5160,5286,5469,8433,8594,11334,11397,11758,11829,11894,11961,12033,12105,12159,12280,12339,12403,12457,12534,12666,12847,12928,13077,13164,13247,13389,13481,13559,13615,13673,13739,13811,13888,13979,14062,14142,14221,14296,14375,14479,14569,14642,14736,14833,14907,14980,15079,15134,15218,15286,15374,15463,15525,15589,15652,15723,15832,15943,16046,16154,16214,16276,16751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "55,56", "startColumns": "4,4", "startOffsets": "3901,4015", "endColumns": "113,122", "endOffsets": "4010,4133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5543,5654,5830,5965,6069,6237,6366,6490,6732,6892,7002,7167,7298,7456,7613,7674,7743", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "5649,5825,5960,6064,6232,6361,6485,6595,6887,6997,7162,7293,7451,7608,7669,7738,7823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "830,938,1045,1157,1245,1348,1463,1542,1619,1710,1803,1898,1992,2092,2185,2280,2374,2465,2558,2639,2743,2846,2944,3051,3158,3263,3420,17085", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "933,1040,1152,1240,1343,1458,1537,1614,1705,1798,1893,1987,2087,2180,2275,2369,2460,2553,2634,2738,2841,2939,3046,3153,3258,3415,3511,17162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,339,453,548,701,816,942,1065,1221,1358,1511,1643,1787,1881,2054,2189,2339,2485,2607,2737,2838,2977,3066,3199,3308,3455", "endColumns": "157,125,113,94,152,114,125,122,155,136,152,131,143,93,172,134,149,145,121,129,100,138,88,132,108,146,108", "endOffsets": "208,334,448,543,696,811,937,1060,1216,1353,1506,1638,1782,1876,2049,2184,2334,2480,2602,2732,2833,2972,3061,3194,3303,3450,3559"}, "to": {"startLines": "59,60,92,95,102,103,107,108,109,110,111,112,113,115,116,117,119,120,121,197,210,211,212,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4242,4400,7828,8117,8599,8752,9199,9325,9448,9604,9741,9894,10026,10270,10364,10537,10738,10888,11034,16902,17741,17842,17981,18070,18203,18312,18459", "endColumns": "157,125,113,94,152,114,125,122,155,136,152,131,143,93,172,134,149,145,121,129,100,138,88,132,108,146,108", "endOffsets": "4395,4521,7937,8207,8747,8862,9320,9443,9599,9736,9889,10021,10165,10359,10532,10667,10883,11029,11151,17027,17837,17976,18065,18198,18307,18454,18563"}}]}]}