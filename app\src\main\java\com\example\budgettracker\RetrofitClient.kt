package com.example.budgettracker

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

object RetrofitClient {
    private var retrofit: Retrofit? = null
    private const val BASE_URL = "https://backend-budget-tracket-v2.vercel.app"

    fun getClient(): Retrofit {
        if (retrofit == null) {
            // Initialize Gson instance
            val gson = GsonBuilder()
                .registerTypeAdapter(Record::class.java, JsonDeserializer<Record> { json, typeOfT, context ->
                    val jsonObject = json.asJsonObject
                    when {
                        json.asJsonObject.has("trans_month") -> {
                            Gson().fromJson(json, Transaction::class.java)
                        }
                        json.asJsonObject.has("note") -> {
                            Gson().fromJson(json, Expenses::class.java)
                        }
                        jsonObject.has("user_email") -> {
                            Gson().fromJson(json, User::class.java)
                        }
                        else -> {
                            Gson().fromJson(json, Budgets::class.java)
                        }
                    }
                })
                .create()

            // Initialize Retrofit instance
            retrofit = Retrofit.Builder()
                .baseUrl(BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build()
        }
        return retrofit!!
    }
}
