package com.example.budgettracker;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;

import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.jjoe64.graphview.DefaultLabelFormatter;
import com.jjoe64.graphview.GraphView;
import com.jjoe64.graphview.GridLabelRenderer;
import com.jjoe64.graphview.series.LineGraphSeries;
import com.jjoe64.graphview.series.DataPoint;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import retrofit2.Call;
import retrofit2.Response;

public class DashboardActivity extends AppCompatActivity {
    private ApiService apiService;
    private LinearLayout listContainer;
    private RecyclerView recyclerView;
    private ItemAdapter itemAdapter;
    private DatabaseHelper dbHelper;
    public String user_id;
     float totalBudget = 0;
     float balance = 0;
     float trans_target = 0;
    private LoadingSpinner loadingSpinner;

    // FAB variables
    private FloatingActionButton fabMain, fabAddExpense, fabAddBudget;
    private TextView fabLabelExpense, fabLabelBudget;
    private boolean isFabMenuOpen = false;

    // Time period filter variables
    private Button btn1d, btn1w, btn1m, btn3m, btn1y;
    private Button currentSelectedButton;
    private String currentTimePeriod = "1M"; // Default to 1 month

    // Session and UI variables
    private SessionManager sessionManager;
    private TextView secondTextView;
    @SuppressLint("WrongViewCast")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dashboard);
        dbHelper = new DatabaseHelper(this);
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        sessionManager = new SessionManager(this);
        secondTextView = findViewById(R.id.secondTextView);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);
        loadingSpinner = new LoadingSpinner(this,rootLayout);
        Map<String, String> sessionDetails = sessionManager.getAllSessionDetails();
        Log.w("MainActivity", "Session Details: " + sessionDetails.toString());
        if (sessionManager.isLoggedIn()) {
            String displayName = sessionManager.getUserName();
            Log.w("DashboardActivity", "Retrieved username from session: " + displayName);
            if (displayName != null && !displayName.trim().isEmpty()) {
                secondTextView.setText(displayName);
                Log.w("DashboardActivity", "Username set to TextView: " + displayName);
            } else {
                Log.w("DashboardActivity", "Username is null or empty, trying to fetch from database");
                // Try to get username from local database or Firebase
                fetchAndSetUsername();
            }
            user_id = sessionManager.getUserUid();

        }else{
            Intent intent = new Intent(DashboardActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        }


        setupNavigationButtons();

        // Initialize with 1M (current month) data by default
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        String monthYear = String.format(Locale.getDefault(), "%d-%02d", year, month);

        // Load default 1M data
        refreshDataForPeriod("1M");

        ImageButton  logoutButton = findViewById(R.id.button_logout);
        logoutButton.setOnClickListener(v -> {
            sessionManager.logoutUser();
            Intent intent = new Intent(DashboardActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        });

        // Setup FloatingActionButton menu
        setupFabMenu();

        // Setup time period filter buttons
        setupTimePeriodButtons();

        // Check for sync when app starts and user is online
        checkAndSyncData();
    }

    private void setupTimePeriodButtons() {
        // Initialize button references
        btn1d = findViewById(R.id.btn_1d);
        btn1w = findViewById(R.id.btn_1w);
        btn1m = findViewById(R.id.btn_1m);
        btn3m = findViewById(R.id.btn_3m);
        btn1y = findViewById(R.id.btn_1y);

        // Set default selected button (1M)
        currentSelectedButton = btn1m;

        // Set click listeners
        btn1d.setOnClickListener(v -> onTimePeriodSelected("1D", btn1d));
        btn1w.setOnClickListener(v -> onTimePeriodSelected("1W", btn1w));
        btn1m.setOnClickListener(v -> onTimePeriodSelected("1M", btn1m));
        btn3m.setOnClickListener(v -> onTimePeriodSelected("3M", btn3m));
        btn1y.setOnClickListener(v -> onTimePeriodSelected("1Y", btn1y));
    }

    private void onTimePeriodSelected(String period, Button selectedButton) {
        // Update visual state
        updateButtonSelection(selectedButton);

        // Update current period
        currentTimePeriod = period;

        // Refresh data based on selected period
        refreshDataForPeriod(period);
    }

    private void updateButtonSelection(Button selectedButton) {
        // Reset all buttons to unselected state
        resetButtonState(btn1d);
        resetButtonState(btn1w);
        resetButtonState(btn1m);
        resetButtonState(btn3m);
        resetButtonState(btn1y);

        // Set selected button state
        selectedButton.setBackgroundResource(R.drawable.small_chip_selected_background);
        selectedButton.setTextColor(getResources().getColor(R.color.md_theme_light_onPrimary, getTheme()));

        currentSelectedButton = selectedButton;
    }

    private void resetButtonState(Button button) {
        button.setBackgroundResource(R.drawable.small_chip_background);
        button.setTextColor(getResources().getColor(R.color.md_theme_light_onSurface, getTheme()));
    }

    private void refreshDataForPeriod(String period) {
        String dateFilter = calculateDateFilter(period);

        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            fetchAndSetOnlineForPeriod(dateFilter, period);
        } else {
            fetchAndSetDataForPeriod(dateFilter, period);
        }

        calculateBalanceForPeriod(dateFilter, period);
    }

    private String calculateDateFilter(String period) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

        switch (period) {
            case "1D":
                // Today only
                return sdf.format(calendar.getTime());

            case "1W":
                // Last 7 days
                calendar.add(Calendar.DAY_OF_MONTH, -7);
                return sdf.format(calendar.getTime());

            case "1M":
                // Current month
                int year = calendar.get(Calendar.YEAR);
                int month = calendar.get(Calendar.MONTH) + 1;
                return String.format(Locale.getDefault(), "%d-%02d", year, month);

            case "3M":
                // Last 3 months
                calendar.add(Calendar.MONTH, -3);
                return sdf.format(calendar.getTime());

            case "1Y":
                // Current year
                return String.valueOf(calendar.get(Calendar.YEAR));

            default:
                // Default to current month
                int defaultYear = calendar.get(Calendar.YEAR);
                int defaultMonth = calendar.get(Calendar.MONTH) + 1;
                return String.format(Locale.getDefault(), "%d-%02d", defaultYear, defaultMonth);
        }
    }

    public void setupNavigationButtons() {
        ImageButton homeButton = findViewById(R.id.button_home);
        ImageButton expensesButton = findViewById(R.id.button_expenses);
        ImageButton transactionsButton = findViewById(R.id.button_transactions);
        ImageButton profileButton = findViewById(R.id.button_profile);
        ImageButton NotificationButton = findViewById(R.id.button_notification);


        homeButton.setOnClickListener(v -> navigateTo(DashboardActivity.class));
        expensesButton.setOnClickListener(v -> navigateTo(ExpensesActivity.class));
        transactionsButton.setOnClickListener(v -> navigateTo(TransactionsActivity.class));
        profileButton.setOnClickListener(v -> navigateTo(SettingsActivity.class));
        NotificationButton.setOnClickListener(v -> navigateTo(NotificationActivity.class));
    }

    private void calculateBalanceForMonth(String monthYear) {

        if (NetworkUtils.isInternetAvailable(this)){
            ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
            Call<List<Record>> call = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));

            call.enqueue(new retrofit2.Callback<List<Record>>() {
                @Override
                public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
//                    Log.w("MainActivity", "Session Details: " + response);
                    if (response.isSuccessful() && response.body() != null && !response.body().isEmpty()) {
                        for (Record record : response.body()) {
                            if (record instanceof Transaction) {
                                Transaction transaction = (Transaction) record;
                                totalBudget = (float) transaction.getTotal();
                                balance = (float) transaction.getBalance();
                                trans_target = (float) transaction.getTransTarget();
                                break;
                            }}
                        updateUI();
                    }else{
                        Log.e("TransactionsActivity", "Response not successful or empty.");
                    }
                }
                @Override
                public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                    Toast.makeText(DashboardActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                }
            });
        }else {
            BalanceResult result = fetchDataFromLocalDatabase(monthYear);
            balance = result.getBalance();
            totalBudget = result.getTotalBudget();
            trans_target = result.getTransTarget();
            updateUI();

        }
//        Log.e("TransactionsActivity", String.valueOf(balance));
//        Log.e("TransactionsActivity", String.valueOf(totalBudget));
//        Log.e("TransactionsActivity", String.valueOf(trans_target));
//        TextView budgetTextView = findViewById(R.id.budget_id);
//        @SuppressLint("DefaultLocale") String budgetInfo = String.format("$%.2f", balance);
//        budgetTextView.setText(budgetInfo);
//        calculateAndDisplayPercentage(balance, trans_target);
    }

    private void updateUI() {
        TextView budgetTextView = findViewById(R.id.budget_id);
        @SuppressLint("DefaultLocale") String budgetInfo = String.format("$%.2f", balance);
        budgetTextView.setText(budgetInfo);

        // Calculate spending (expenses only, not including transactions)
        // Since transactions only add to balance, not budget, we need to calculate expenses differently
        float totalSpent = Math.max(0, totalBudget - balance); // Ensure non-negative

        // For a more accurate calculation, let's get the actual expenses from the database
        calculateActualExpensesAndUpdateUI();
    }

    private void calculateActualExpensesAndUpdateUI() {
        // Get current month for expense calculation
        Calendar cal = Calendar.getInstance();
        String currentMonth = String.format(Locale.getDefault(), "%d-%02d", cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1);

        DatabaseHelper dbHelper = new DatabaseHelper(this);
        SQLiteDatabase db = dbHelper.getReadableDatabase();

        // Get actual expenses for current month
        String expensesQuery = "SELECT SUM(amount) FROM expenses WHERE strftime('%Y-%m', date) = ? AND user_id = ?";
        Cursor expensesCursor = db.rawQuery(expensesQuery, new String[]{currentMonth, String.valueOf(user_id)});

        float actualExpenses = 0;
        if (expensesCursor.moveToFirst()) {
            actualExpenses = expensesCursor.getFloat(0);
        }
        expensesCursor.close();
        db.close();

        // Update all the new UI elements with actual expenses
        updateSpendingVsTarget(actualExpenses, trans_target);
        updateBalanceVsBudget(balance, totalBudget);
        updateTotalExpenditure(actualExpenses);
    }

    private BalanceResult fetchDataFromLocalDatabase(String monthYear) {
        DatabaseHelper dbHelper = new DatabaseHelper(this);
        SQLiteDatabase db = dbHelper.getReadableDatabase();

        // Get transaction summary (initial budget and target)
        String transactionsQuery = "SELECT trans_budget, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?";
        Cursor transactionsCursor = db.rawQuery(transactionsQuery, new String[]{monthYear, String.valueOf(user_id)});

        float initialBudget = 0;
        if (transactionsCursor.moveToFirst()) {
            initialBudget = transactionsCursor.getFloat(0);  // This is the starting budget
            trans_target = transactionsCursor.getFloat(1);
        }
        transactionsCursor.close();

        // Get current balance directly from transactions table (it already includes all transactions)
        String balanceQuery = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?";
        Cursor balanceCursor = db.rawQuery(balanceQuery, new String[]{monthYear, String.valueOf(user_id)});

        float currentBalance = initialBudget; // Default to initial budget if no transactions record
        if (balanceCursor.moveToFirst()) {
            currentBalance = balanceCursor.getFloat(0);
        }
        balanceCursor.close();

        // Get all expenses for the month
        String expensesQuery = "SELECT SUM(amount) FROM expenses WHERE strftime('%Y-%m', date) = ? AND user_id = ?";
        Cursor expensesCursor = db.rawQuery(expensesQuery, new String[]{monthYear, String.valueOf(user_id)});

        float totalExpenses = 0;
        if (expensesCursor.moveToFirst()) {
            totalExpenses = expensesCursor.getFloat(0);
        }
        expensesCursor.close();

        // Calculate final balance: current balance (includes transactions) - total expenses
        balance = currentBalance - totalExpenses;
        totalBudget = initialBudget;  // Budget stays the same, only balance changes

        Log.d("DashboardActivity", "Balance calculation: Initial Budget=" + initialBudget + ", Current Balance (with transactions)=" + currentBalance + ", Expenses=" + totalExpenses + ", Final Balance=" + balance);

        db.close();
        return new BalanceResult(totalBudget, balance, trans_target);
    }

    @SuppressLint("SetTextI18n")
    private void updateSpendingVsTarget(float spentAmount, float targetAmount) {
        TextView spendingVsTargetTextView = findViewById(R.id.spending_vs_target);

        if (targetAmount != 0) {
            float spendingPercentage = (spentAmount / targetAmount) * 100;
            String result = String.format(Locale.getDefault(), "%.1f%% of target", spendingPercentage);

            // Color coding based on spending percentage
            if (spendingPercentage >= 100) {
                spendingVsTargetTextView.setTextColor(Color.RED);  // Over budget - red
                result = String.format(Locale.getDefault(), "%.1f%% - OVER!", spendingPercentage);
            } else if (spendingPercentage >= 80) {
                spendingVsTargetTextView.setTextColor(Color.parseColor("#FF8C00"));  // Warning - orange
            } else if (spendingPercentage >= 50) {
                spendingVsTargetTextView.setTextColor(Color.parseColor("#FFD700"));  // Caution - yellow/gold
            } else {
                spendingVsTargetTextView.setTextColor(Color.GREEN);  // Good - green
            }
            spendingVsTargetTextView.setText(result);
        } else {
            spendingVsTargetTextView.setText("No target set");
            spendingVsTargetTextView.setTextColor(Color.GRAY);
        }
    }

    @SuppressLint("SetTextI18n")
    private void updateBalanceVsBudget(float currentBalance, float totalBudget) {
        TextView balanceVsBudgetTextView = findViewById(R.id.balance_vs_budget);

        if (totalBudget != 0) {
            float balancePercentage = (currentBalance / totalBudget) * 100;
            String result = String.format(Locale.getDefault(), "%.1f%% remaining", balancePercentage);

            // Color coding based on remaining balance percentage
            if (balancePercentage <= 10) {
                balanceVsBudgetTextView.setTextColor(Color.RED);  // Very low - red
            } else if (balancePercentage <= 25) {
                balanceVsBudgetTextView.setTextColor(Color.parseColor("#FF8C00"));  // Low - orange
            } else if (balancePercentage <= 50) {
                balanceVsBudgetTextView.setTextColor(Color.parseColor("#FFD700"));  // Medium - yellow/gold
            } else {
                balanceVsBudgetTextView.setTextColor(Color.GREEN);  // Good - green
            }
            balanceVsBudgetTextView.setText(result);
        } else {
            balanceVsBudgetTextView.setText("No budget set");
            balanceVsBudgetTextView.setTextColor(Color.GRAY);
        }
    }

    @SuppressLint("SetTextI18n")
    private void updateTotalExpenditure(float totalSpent) {
        TextView totalExpenditureTextView = findViewById(R.id.total_expenditure);
        String result = String.format(Locale.getDefault(), "$%.2f", totalSpent);
        totalExpenditureTextView.setText(result);
        totalExpenditureTextView.setTextColor(Color.WHITE);
    }


    private void navigateTo(Class<?> destination) {

        Intent intent = new Intent(DashboardActivity.this, destination);
        startActivity(intent);
        finish();
    }

    // Parse items JSON
    private List<Expenses> parseItems(String json) {
        Gson gson = new Gson();
        Type expensesListType = new TypeToken<List<Expenses>>() {}.getType();
        return gson.fromJson(json, expensesListType);
    }

    // Parse chart data JSON
    private List<ChartData> parseChartData(String json) {
        Gson gson = new Gson();
        Type chartDataListType = new TypeToken<List<ChartData>>() {}.getType();
        return gson.fromJson(json, chartDataListType);
    }


    private void setupChart(List<Integer> amounts) {
        GraphView graph = findViewById(R.id.graph);

        // Clear any existing series
        graph.removeAllSeries();

        if (amounts.isEmpty()) {
            return;
        }

        // Create a meaningful chart with daily spending pattern
        LineGraphSeries<DataPoint> dailySpendingSeries = new LineGraphSeries<>();
        LineGraphSeries<DataPoint> cumulativeSpendingSeries = new LineGraphSeries<>();

        // Calculate both daily and cumulative spending
        double cumulativeSpending = 0;
        for (int i = 0; i < amounts.size(); i++) {
            // Daily spending (individual expenses)
            dailySpendingSeries.appendData(new DataPoint(i + 1, amounts.get(i)), true, amounts.size());

            // Cumulative spending
            cumulativeSpending += amounts.get(i);
            cumulativeSpendingSeries.appendData(new DataPoint(i + 1, cumulativeSpending), true, amounts.size());
        }

        // Style the daily spending series with fallback colors
        try {
            dailySpendingSeries.setColor(getResources().getColor(R.color.md_theme_light_secondary, getTheme()));
        } catch (Exception e) {
            dailySpendingSeries.setColor(Color.BLUE); // Fallback color
        }
        dailySpendingSeries.setThickness(3);
        dailySpendingSeries.setDrawDataPoints(true);
        dailySpendingSeries.setDataPointsRadius(5);
        dailySpendingSeries.setTitle("Daily Spending");

        // Style the cumulative spending series with fallback colors
        try {
            cumulativeSpendingSeries.setColor(getResources().getColor(R.color.md_theme_light_primary, getTheme()));
        } catch (Exception e) {
            cumulativeSpendingSeries.setColor(Color.RED); // Fallback color
        }
        cumulativeSpendingSeries.setThickness(4);
        cumulativeSpendingSeries.setDrawDataPoints(true);
        cumulativeSpendingSeries.setDataPointsRadius(6);
        cumulativeSpendingSeries.setTitle("Cumulative Spending");

        // Add both series to the graph
        graph.addSeries(dailySpendingSeries);
        graph.addSeries(cumulativeSpendingSeries);

        // Configure graph appearance
        graph.setBackgroundColor(Color.TRANSPARENT);
        graph.getGridLabelRenderer().setGridStyle(GridLabelRenderer.GridStyle.HORIZONTAL);
        try {
            graph.getGridLabelRenderer().setGridColor(getResources().getColor(R.color.md_theme_light_outline, getTheme()));
        } catch (Exception e) {
            graph.getGridLabelRenderer().setGridColor(Color.LTGRAY); // Fallback color
        }
        graph.getGridLabelRenderer().setHorizontalLabelsVisible(true);
        graph.getGridLabelRenderer().setVerticalLabelsVisible(true);
        graph.getGridLabelRenderer().setTextSize(28f);
        graph.getGridLabelRenderer().setLabelsSpace(10);

        // Custom label formatter
        graph.getGridLabelRenderer().setLabelFormatter(new DefaultLabelFormatter() {
            @Override
            public String formatLabel(double value, boolean isValueX) {
                if (isValueX) {
                    return "Day " + (int) value;
                } else {
                    return "$" + (int) value;
                }
            }
        });

        // Configure viewport
        graph.getViewport().setScalable(true);
        graph.getViewport().setScrollable(true);
        graph.getViewport().setScalableY(true);
        graph.getViewport().setScrollableY(true);

        // Set bounds
        graph.getViewport().setXAxisBoundsManual(true);
        graph.getViewport().setMinX(0.5);
        graph.getViewport().setMaxX(amounts.size() + 0.5);

        graph.getViewport().setYAxisBoundsManual(true);
        graph.getViewport().setMinY(0);
        double maxValue = Math.max(getMaxValue(amounts), cumulativeSpending);
        graph.getViewport().setMaxY(maxValue * 1.1); // Add 10% padding

        // Add legend (simplified to avoid import issues)
        graph.getLegendRenderer().setVisible(true);
        graph.getLegendRenderer().setBackgroundColor(Color.TRANSPARENT);
        graph.getLegendRenderer().setTextSize(24f);

        graph.invalidate();
    }
    // Helper method to get max value from the list
    private static double getMaxValue(List<Integer> values) {
        double max = 0;
        for (int value : values) {
            if (value > max) {
                max = value;
            }
        }
        return max;
    }

    private void fetchAndSetData(String monthyear) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        String[] projection = {"name","note","date","amount","user_id"};
////        String selection = "user_id = ?";
        String selection = "user_id = ? AND strftime('%Y-%m', date) = ?";
        String[] selectionArgs = {String.valueOf(user_id), monthyear};
        Cursor cursor = db.query(
                "expenses",
                projection,
                selection,
                selectionArgs,
                null,
                null,
                null
        );
        List<Expenses> expensesList = new ArrayList<>();
        List<Integer> amounts = new ArrayList<>();
        while (cursor.moveToNext()) {
            String name = cursor.getString(cursor.getColumnIndexOrThrow("name"));
            String note = cursor.getString(cursor.getColumnIndexOrThrow("note"));
            String date = cursor.getString(cursor.getColumnIndexOrThrow("date"));
            int amount = cursor.getInt(cursor.getColumnIndexOrThrow("amount"));
            String user_id = String.valueOf(cursor.getInt(cursor.getColumnIndexOrThrow("user_id")));
            Expenses expense = new Expenses(name,date,note,amount,user_id);
            expensesList.add(expense);
            amounts.add(amount);
        }
        cursor.close();
        Gson gson = new Gson();

        String jsonItems = gson.toJson(expensesList);
        if (!amounts.isEmpty()) {
            setupChart(amounts);
        } else {
            GraphView graph = findViewById(R.id.graph);
            graph.removeAllSeries();
        }
        Type listType = new TypeToken<List<Expenses>>() {}.getType();
        List<Expenses> items = gson.fromJson(jsonItems, listType);
        ItemAdapter itemAdapter = new ItemAdapter(items);
        recyclerView.setAdapter(itemAdapter);
    }
    private void fetchAndSetOnline(String monthyear) {
        loadingSpinner.show();
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        Call<List<Record>> call = apiService.getRecords("expenses", monthyear, String.valueOf(user_id));
        call.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Expenses> items = getExpenses(response);
                    if (items != null) {
                        for (Expenses expense : items) {expense.setDate(trimDate(expense.getDate()));}
                        ItemAdapter itemAdapter = new ItemAdapter(items);
                        recyclerView.setAdapter(itemAdapter);
                        loadingSpinner.hide();
                    } else {Toast.makeText(getApplicationContext(), "No expenses found", Toast.LENGTH_SHORT).show();}
                } else {
                    loadingSpinner.hide();
                    Toast.makeText(getApplicationContext(), "Failed to fetch data", Toast.LENGTH_SHORT).show();}}
            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {

                Toast.makeText(getApplicationContext(), "Error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                loadingSpinner.hide();
            }

        });

    }
    private String trimDate(String dateTime) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            return outputFormat.format(inputFormat.parse(dateTime));
        } catch (Exception e) {
            e.printStackTrace();
            return dateTime; // Return the original if parsing fails
        }
    }
    private @Nullable List<Expenses> getExpenses(@NonNull Response<List<Record>> response) {
        List<Record> records = response.body();
        List<Expenses> expensesList = new ArrayList<>();
        List<Integer> amounts = new ArrayList<>();
        for (Record record : records) {
            if (record instanceof Expenses) {
                Expenses expense = (Expenses) record;
                int  amount = expense.getAmount();
                expensesList.add(expense);
                amounts.add(amount);
            }
        }
        Gson gson = new Gson();
        String jsonItems = gson.toJson(expensesList);
        if (!amounts.isEmpty()) {
            setupChart(amounts);
        } else {
            GraphView graph = findViewById(R.id.graph);
            graph.removeAllSeries();
        }

        Type listType = new TypeToken<List<Expenses>>() {}.getType();
        return gson.fromJson(jsonItems, listType);
    }

    private void fetchAndSetDataForPeriod(String dateFilter, String period) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        String[] projection = {"name","note","date","amount","user_id"};
        String selection;
        String[] selectionArgs;

        switch (period) {
            case "1D":
                // Today only
                selection = "user_id = ? AND date = ?";
                selectionArgs = new String[]{String.valueOf(user_id), dateFilter};
                break;

            case "1W":
                // Last 7 days
                selection = "user_id = ? AND date >= ?";
                selectionArgs = new String[]{String.valueOf(user_id), dateFilter};
                break;

            case "1M":
                // Current month
                selection = "user_id = ? AND strftime('%Y-%m', date) = ?";
                selectionArgs = new String[]{String.valueOf(user_id), dateFilter};
                break;

            case "3M":
                // Last 3 months
                selection = "user_id = ? AND date >= ?";
                selectionArgs = new String[]{String.valueOf(user_id), dateFilter};
                break;

            case "1Y":
                // Current year
                selection = "user_id = ? AND strftime('%Y', date) = ?";
                selectionArgs = new String[]{String.valueOf(user_id), dateFilter};
                break;

            default:
                // Default to current month
                selection = "user_id = ? AND strftime('%Y-%m', date) = ?";
                selectionArgs = new String[]{String.valueOf(user_id), dateFilter};
                break;
        }

        Cursor cursor = db.query("expenses", projection, selection, selectionArgs, null, null, "date DESC");

        List<Expenses> expensesList = new ArrayList<>();
        List<Integer> amounts = new ArrayList<>();

        while (cursor.moveToNext()) {
            String name = cursor.getString(cursor.getColumnIndexOrThrow("name"));
            String note = cursor.getString(cursor.getColumnIndexOrThrow("note"));
            String date = cursor.getString(cursor.getColumnIndexOrThrow("date"));
            int amount = cursor.getInt(cursor.getColumnIndexOrThrow("amount"));
            String user_id = String.valueOf(cursor.getInt(cursor.getColumnIndexOrThrow("user_id")));

            Expenses expense = new Expenses(name, date, note, amount, user_id);
            expensesList.add(expense);
            amounts.add(amount);
        }
        cursor.close();

        // Update chart and list
        if (!amounts.isEmpty()) {
            setupChart(amounts);
        } else {
            GraphView graph = findViewById(R.id.graph);
            graph.removeAllSeries();
        }

        ItemAdapter itemAdapter = new ItemAdapter(expensesList);
        recyclerView.setAdapter(itemAdapter);
    }

    private void fetchAndSetOnlineForPeriod(String dateFilter, String period) {
        loadingSpinner.show();
        FirebaseManager firebaseManager = new FirebaseManager();

        // Fetch expenses based on period
        switch (period) {
            case "1M":
                // For monthly data, use the existing month-based method
                firebaseManager.getExpensesForMonth(dateFilter, new FirebaseManager.FirebaseCallback<List<Map<String, Object>>>() {
                    @Override
                    public void onSuccess(List<Map<String, Object>> expenses) {
                        processFirebaseExpenses(expenses);
                    }

                    @Override
                    public void onFailure(Exception e) {
                        Log.e("DashboardActivity", "Failed to fetch Firebase expenses", e);
                        loadingSpinner.hide();
                        Toast.makeText(DashboardActivity.this, "Failed to load data from cloud", Toast.LENGTH_SHORT).show();
                    }
                });
                break;

            default:
                // For other periods, get all expenses and filter
                firebaseManager.getExpenses(new FirebaseManager.FirebaseCallback<List<Map<String, Object>>>() {
                    @Override
                    public void onSuccess(List<Map<String, Object>> expenses) {
                        List<Map<String, Object>> filteredExpenses = filterExpensesByPeriod(expenses, period, dateFilter);
                        processFirebaseExpenses(filteredExpenses);
                    }

                    @Override
                    public void onFailure(Exception e) {
                        Log.e("DashboardActivity", "Failed to fetch Firebase expenses", e);
                        loadingSpinner.hide();
                        Toast.makeText(DashboardActivity.this, "Failed to load data from cloud", Toast.LENGTH_SHORT).show();
                    }
                });
                break;
        }
    }

    private void processFirebaseExpenses(List<Map<String, Object>> firebaseExpenses) {
        Log.d("DashboardActivity", "Processing " + firebaseExpenses.size() + " Firebase expenses");

        List<Expenses> expensesList = new ArrayList<>();
        List<Integer> amounts = new ArrayList<>();

        for (Map<String, Object> expenseData : firebaseExpenses) {
            try {
                String name = (String) expenseData.get("name");
                String note = (String) expenseData.get("note");
                String date = (String) expenseData.get("date");
                Object amountObj = expenseData.get("amount");
                String userId = (String) expenseData.get("user_id");

                // Handle amount conversion (could be Long or Integer from Firebase)
                int amount = 0;
                if (amountObj instanceof Long) {
                    amount = ((Long) amountObj).intValue();
                } else if (amountObj instanceof Integer) {
                    amount = (Integer) amountObj;
                } else if (amountObj instanceof Double) {
                    amount = ((Double) amountObj).intValue();
                }

                if (name != null && date != null) {
                    Expenses expense = new Expenses(name, trimDate(date), note != null ? note : "", amount, userId);
                    expensesList.add(expense);
                    amounts.add(amount);
                }
            } catch (Exception e) {
                Log.e("DashboardActivity", "Error processing expense data", e);
            }
        }

        // Update UI on main thread
        runOnUiThread(() -> {
            // Update chart
            if (!amounts.isEmpty()) {
                setupChart(amounts);
            } else {
                GraphView graph = findViewById(R.id.graph);
                graph.removeAllSeries();
            }

            // Update list
            ItemAdapter itemAdapter = new ItemAdapter(expensesList);
            recyclerView.setAdapter(itemAdapter);

            loadingSpinner.hide();
            Log.d("DashboardActivity", "Updated UI with " + expensesList.size() + " expenses");
        });
    }

    private List<Map<String, Object>> filterExpensesByPeriod(List<Map<String, Object>> expenses, String period, String dateFilter) {
        List<Map<String, Object>> filtered = new ArrayList<>();

        try {
            Calendar filterDate = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

            for (Map<String, Object> expense : expenses) {
                String dateStr = (String) expense.get("date");
                if (dateStr == null) continue;

                Date expenseDate = sdf.parse(dateStr);
                Calendar expenseCal = Calendar.getInstance();
                expenseCal.setTime(expenseDate);

                boolean include = false;

                switch (period) {
                    case "1D":
                        Date filterDateObj = sdf.parse(dateFilter);
                        include = sdf.format(expenseDate).equals(dateFilter);
                        break;

                    case "1W":
                        Date weekStart = sdf.parse(dateFilter);
                        include = expenseDate.after(weekStart) || expenseDate.equals(weekStart);
                        break;

                    case "3M":
                        Date threeMonthsAgo = sdf.parse(dateFilter);
                        include = expenseDate.after(threeMonthsAgo) || expenseDate.equals(threeMonthsAgo);
                        break;

                    case "1Y":
                        int year = Integer.parseInt(dateFilter);
                        include = expenseCal.get(Calendar.YEAR) == year;
                        break;
                }

                if (include) {
                    filtered.add(expense);
                }
            }
        } catch (Exception e) {
            Log.e("DashboardActivity", "Error filtering expenses by period", e);
            return expenses; // Return unfiltered if error
        }

        return filtered;
    }

    private void calculateBalanceForPeriod(String dateFilter, String period) {
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            calculateBalanceForMonth(dateFilter);
        } else {
            BalanceResult result = fetchDataFromLocalDatabaseForPeriod(dateFilter, period);
            balance = result.getBalance();
            totalBudget = result.getTotalBudget();
            trans_target = result.getTransTarget();
            updateUI();
        }
    }

    private BalanceResult fetchDataFromLocalDatabaseForPeriod(String dateFilter, String period) {
        // For monthly data, use the proper calculation method
        if ("1M".equals(period)) {
            return fetchDataFromLocalDatabase(dateFilter);
        }

        DatabaseHelper dbHelper = new DatabaseHelper(this);
        SQLiteDatabase db = dbHelper.getReadableDatabase();

        // Get current month's transaction summary for targets
        Calendar cal = Calendar.getInstance();
        String currentMonth = String.format(Locale.getDefault(), "%d-%02d", cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1);

        String transactionsQuery = "SELECT trans_budget, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?";
        Cursor transactionsCursor = db.rawQuery(transactionsQuery, new String[]{currentMonth, String.valueOf(user_id)});

        float initialBudget = 0;
        float periodTarget = 0;

        if (transactionsCursor.moveToFirst()) {
            initialBudget = transactionsCursor.getFloat(0);
            periodTarget = transactionsCursor.getFloat(1);
        }
        transactionsCursor.close();

        // Get current balance from transactions table (includes all transactions up to current month)
        String balanceQuery = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?";
        Cursor balanceCursor = db.rawQuery(balanceQuery, new String[]{currentMonth, String.valueOf(user_id)});

        float currentBalance = initialBudget; // Default to initial budget if no transactions record
        if (balanceCursor.moveToFirst()) {
            currentBalance = balanceCursor.getFloat(0);
        }
        balanceCursor.close();

        // Get expenses for the specific period
        String expenseQuery = getExpenseQueryForPeriod(period);
        String[] expenseArgs = getSelectionArgsForPeriod(dateFilter, period);
        Cursor expensesCursor = db.rawQuery(expenseQuery, expenseArgs);

        float totalExpenses = 0;
        if (expensesCursor.moveToFirst()) {
            totalExpenses = expensesCursor.getFloat(0);
        }
        expensesCursor.close();

        // For periods other than 1M, we need to calculate differently
        float periodBalance;
        if ("1M".equals(period)) {
            // For monthly view, use current balance minus expenses
            periodBalance = currentBalance - totalExpenses;
        } else {
            // For other periods, show current balance (transactions are cumulative)
            periodBalance = currentBalance - totalExpenses;
        }

        float periodBudget = initialBudget; // Budget stays the same

        Log.d("DashboardActivity", "Period " + period + " calculation: Initial Budget=" + initialBudget + ", Current Balance=" + currentBalance + ", Period Expenses=" + totalExpenses + ", Final Balance=" + periodBalance);

        db.close();
        return new BalanceResult(periodBudget, periodBalance, periodTarget);
    }

    private String getIncomeQueryForPeriod(String period) {
        switch (period) {
            case "1D":
                return "SELECT SUM(bud_amount) FROM budget WHERE bud_date = ? AND user_id = ?";
            case "1W":
                return "SELECT SUM(bud_amount) FROM budget WHERE bud_date >= ? AND user_id = ?";
            case "3M":
                return "SELECT SUM(bud_amount) FROM budget WHERE bud_date >= ? AND user_id = ?";
            case "1Y":
                return "SELECT SUM(bud_amount) FROM budget WHERE strftime('%Y', bud_date) = ? AND user_id = ?";
            default:
                return "SELECT SUM(bud_amount) FROM budget WHERE strftime('%Y-%m', bud_date) = ? AND user_id = ?";
        }
    }

    private String getExpenseQueryForPeriod(String period) {
        switch (period) {
            case "1D":
                return "SELECT SUM(amount) FROM expenses WHERE date = ? AND user_id = ?";
            case "1W":
                return "SELECT SUM(amount) FROM expenses WHERE date >= ? AND user_id = ?";
            case "3M":
                return "SELECT SUM(amount) FROM expenses WHERE date >= ? AND user_id = ?";
            case "1Y":
                return "SELECT SUM(amount) FROM expenses WHERE strftime('%Y', date) = ? AND user_id = ?";
            default:
                return "SELECT SUM(amount) FROM expenses WHERE strftime('%Y-%m', date) = ? AND user_id = ?";
        }
    }

    private String[] getSelectionArgsForPeriod(String dateFilter, String period) {
        return new String[]{dateFilter, String.valueOf(user_id)};
    }

    private void checkAndSyncData() {
        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            SyncService syncService = new SyncService(this);
            syncService.syncLocalDataToFirebase(new SyncService.SyncCallback() {
                @Override
                public void onSyncComplete(boolean success, String message) {
                    if (success) {
                        Log.d("DashboardActivity", "Sync completed: " + message);
                        // Refresh data after sync
                        refreshDataForPeriod(currentTimePeriod);
                    } else {
                        Log.e("DashboardActivity", "Sync failed: " + message);
                    }
                }
            });
        }
    }

    private void fetchAndSetUsername() {
        String userId = sessionManager.getUserUid();
        if (userId == null) {
            Log.e("DashboardActivity", "User ID is null, cannot fetch username");
            // Use email prefix as fallback
            String email = sessionManager.getUserEmail();
            if (email != null && !email.trim().isEmpty()) {
                String fallbackName = email.split("@")[0];
                secondTextView.setText(fallbackName);
                Log.w("DashboardActivity", "Using email prefix as fallback: " + fallbackName);
            } else {
                secondTextView.setText("User");
            }
            return;
        }

        Log.w("DashboardActivity", "Fetching username for user ID: " + userId);

        if (AppConfig.shouldUseFirebase(NetworkUtils.isInternetAvailable(this))) {
            // Try to fetch from Firebase first
            fetchUsernameFromFirebase(userId);
        } else {
            // Fetch from local database
            fetchUsernameFromLocal(userId);
        }
    }

    private void fetchUsernameFromFirebase(String userId) {
        FirebaseManager firebaseManager = new FirebaseManager();
        firebaseManager.getUserDisplayName(userId, new FirebaseManager.FirebaseCallback<String>() {
            @Override
            public void onSuccess(String displayName) {
                if (displayName != null && !displayName.trim().isEmpty()) {
                    Log.w("DashboardActivity", "Fetched username from Firebase: " + displayName);
                    secondTextView.setText(displayName);
                    // Update session with the fetched username
                    sessionManager.createLoginSession(sessionManager.getUserEmail(), displayName, userId);
                } else {
                    Log.w("DashboardActivity", "Firebase returned null/empty username, trying local database");
                    fetchUsernameFromLocal(userId);
                }
            }

            @Override
            public void onFailure(Exception e) {
                Log.e("DashboardActivity", "Failed to fetch username from Firebase", e);
                fetchUsernameFromLocal(userId);
            }
        });
    }

    private void fetchUsernameFromLocal(String userId) {
        DatabaseHelper dbHelper = new DatabaseHelper(this);
        SQLiteDatabase db = dbHelper.getReadableDatabase();

        String[] projection = {"user_name"};
        String selection = "user_id = ?";
        String[] selectionArgs = {userId};

        Cursor cursor = db.query("user", projection, selection, selectionArgs, null, null, null);

        if (cursor.moveToFirst()) {
            String displayName = cursor.getString(cursor.getColumnIndexOrThrow("user_name"));
            if (displayName != null && !displayName.trim().isEmpty()) {
                Log.w("DashboardActivity", "Fetched username from local database: " + displayName);
                secondTextView.setText(displayName);
                // Update session with the fetched username
                sessionManager.createLoginSession(sessionManager.getUserEmail(), displayName, userId);
            } else {
                Log.w("DashboardActivity", "Local database returned null/empty username");
                setFallbackUsername();
            }
        } else {
            Log.w("DashboardActivity", "No user found in local database with ID: " + userId);
            setFallbackUsername();
        }

        cursor.close();
        db.close();
    }

    private void setFallbackUsername() {
        String email = sessionManager.getUserEmail();
        if (email != null && !email.trim().isEmpty()) {
            String fallbackName = email.split("@")[0];
            secondTextView.setText(fallbackName);
            Log.w("DashboardActivity", "Using email prefix as fallback username: " + fallbackName);
            // Update session with fallback username
            sessionManager.createLoginSession(email, fallbackName, sessionManager.getUserUid());
        } else {
            secondTextView.setText("User");
            Log.w("DashboardActivity", "No email available, using 'User' as fallback");
        }
    }

    /**
     * Setup the FloatingActionButton menu with animations
     */
    private void setupFabMenu() {
        fabMain = findViewById(R.id.fab_add_transaction);
        fabAddExpense = findViewById(R.id.fab_add_expense);
        fabAddBudget = findViewById(R.id.fab_add_budget);
        fabLabelExpense = findViewById(R.id.fab_label_expense);
        fabLabelBudget = findViewById(R.id.fab_label_budget);

        // Set click listeners
        fabMain.setOnClickListener(v -> toggleFabMenu());

        fabAddExpense.setOnClickListener(v -> {
            // Navigate to Add Expense Activity
            Intent intent = new Intent(DashboardActivity.this, ExpensesActivity.class);
            startActivity(intent);
            closeFabMenu();
        });

        fabAddBudget.setOnClickListener(v -> {
            // Navigate to Add Transaction Activity
            Intent intent = new Intent(DashboardActivity.this, TransactionsActivity.class);
            startActivity(intent);
            closeFabMenu();
        });
    }

    /**
     * Toggle the FAB menu open/close
     */
    private void toggleFabMenu() {
        if (isFabMenuOpen) {
            closeFabMenu();
        } else {
            openFabMenu();
        }
    }

    /**
     * Open the FAB menu with animation
     */
    private void openFabMenu() {
        isFabMenuOpen = true;

        // Rotate main FAB
        fabMain.animate().rotation(45f).setDuration(300).start();

        // Show and animate sub FABs and labels
        fabAddExpense.setVisibility(View.VISIBLE);
        fabAddBudget.setVisibility(View.VISIBLE);
        fabLabelExpense.setVisibility(View.VISIBLE);
        fabLabelBudget.setVisibility(View.VISIBLE);

        fabAddExpense.animate()
                .translationY(-160f)
                .alpha(1f)
                .setDuration(300)
                .start();

        fabAddBudget.animate()
                .translationY(-110f)
                .alpha(1f)
                .setDuration(300)
                .start();

        fabLabelExpense.animate()
                .translationY(-160f)
                .alpha(1f)
                .setDuration(300)
                .start();

        fabLabelBudget.animate()
                .translationY(-110f)
                .alpha(1f)
                .setDuration(300)
                .start();
    }

    /**
     * Close the FAB menu with animation
     */
    private void closeFabMenu() {
        isFabMenuOpen = false;

        // Rotate main FAB back
        fabMain.animate().rotation(0f).setDuration(300).start();

        // Hide and animate sub FABs and labels
        fabAddExpense.animate()
                .translationY(0f)
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> fabAddExpense.setVisibility(View.GONE))
                .start();

        fabAddBudget.animate()
                .translationY(0f)
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> fabAddBudget.setVisibility(View.GONE))
                .start();

        fabLabelExpense.animate()
                .translationY(0f)
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> fabLabelExpense.setVisibility(View.GONE))
                .start();

        fabLabelBudget.animate()
                .translationY(0f)
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> fabLabelBudget.setVisibility(View.GONE))
                .start();
    }

    /**
     * Shows a dialog with options to add either an expense or transaction
     */
    private void showAddOptionsDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Add New Entry")
                .setMessage("What would you like to add?")
                .setPositiveButton("Add Expense", (dialog, which) -> {
                    // Navigate to Add Expense Activity
                    Intent intent = new Intent(DashboardActivity.this, ExpensesActivity.class);
                    startActivity(intent);
                })
                .setNegativeButton("Add Transaction", (dialog, which) -> {
                    // Navigate to Add Transaction Activity
                    Intent intent = new Intent(DashboardActivity.this, TransactionsActivity.class);
                    startActivity(intent);
                })
                .setNeutralButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }

    @Override
    public void onBackPressed() {
        if (isFabMenuOpen) {
            closeFabMenu();
        } else {
            super.onBackPressed();
        }
    }

}
