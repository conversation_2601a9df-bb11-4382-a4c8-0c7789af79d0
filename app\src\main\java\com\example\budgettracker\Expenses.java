package com.example.budgettracker;

import androidx.annotation.NonNull;

public class Expenses extends Record{
    private String name;
    private String date;
    private String note; // Added field for 'note'
    private int amount;

    public Expenses(String name, String date, String note, int amount,String user_id) {
        super(user_id);
        this.name = name;
        this.date = date;
        this.note = note;
        this.amount = amount;
    }



    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

//    @NonNull
//    @Override
//    public String toString() {
//        return name + " - " + amount;
//    }
}
