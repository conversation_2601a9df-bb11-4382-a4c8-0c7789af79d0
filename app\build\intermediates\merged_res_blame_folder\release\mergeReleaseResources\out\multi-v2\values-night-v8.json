{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.example.budgettracker.app-mergeReleaseResources-58:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2455,2530,2641,2730,2831,2938,3045,3144,3251,3354,3442,3566,3668,3770,3886,3988,4102,4230,4346,4468,4604,4724,4858,4978,5090,5305,5422,5546,5676,5798,5936,6070,6186", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2525,2636,2725,2826,2933,3040,3139,3246,3349,3437,3561,3663,3765,3881,3983,4097,4225,4341,4463,4599,4719,4853,4973,5085,5211,5417,5541,5671,5793,5931,6065,6181,6301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "49,50,51,52,53,54,55,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1823,1893,1977,2061,2157,2259,2361,5216", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1888,1972,2056,2152,2254,2356,2450,5300"}}, {"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "116", "endLines": "48", "endColumns": "12", "endOffsets": "2148"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "48", "endColumns": "12", "endOffsets": "1818"}}]}, {"outputFile": "com.example.budgettracker.app-mergeReleaseResources-58:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2452,2527,2638,2727,2828,2935,3042,3141,3248,3351,3439,3563,3665,3767,3883,3985,4099,4227,4343,4465,4601,4721,4855,4975,5087,5302,5419,5543,5673,5795,5933,6067,6183", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2522,2633,2722,2823,2930,3037,3136,3243,3346,3434,3558,3660,3762,3878,3980,4094,4222,4338,4460,4596,4716,4850,4970,5082,5208,5414,5538,5668,5790,5928,6062,6178,6298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "49,50,51,52,53,54,55,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1820,1890,1974,2058,2154,2256,2358,5213", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1885,1969,2053,2149,2251,2353,2447,5297"}}, {"source": "C:\\Users\\<USER>\\Desktop\\New folder\\BudgetTracker\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "116", "endLines": "48", "endColumns": "12", "endOffsets": "2145"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "48", "endColumns": "12", "endOffsets": "1815"}}]}]}