package com.example.budgettracker;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class SyncService {
    private static final String TAG = "SyncService";
    private DatabaseHelper dbHelper;
    private FirebaseManager firebaseManager;
    private Context context;

    public SyncService(Context context) {
        this.context = context;
        this.dbHelper = new DatabaseHelper(context);
        this.firebaseManager = new FirebaseManager();
    }

    public interface SyncCallback {
        void onSyncComplete(boolean success, String message);
    }

    public void syncLocalDataToFirebase(SyncCallback callback) {
        if (!firebaseManager.isUserAuthenticated()) {
            callback.onSyncComplete(false, "User not authenticated");
            return;
        }

        Log.d(TAG, "Starting sync of local data to Firebase");
        
        // Sync expenses first, then transactions
        syncExpensesToFirebase(new SyncCallback() {
            @Override
            public void onSyncComplete(boolean success, String message) {
                if (success) {
                    syncTransactionsToFirebase(callback);
                } else {
                    callback.onSyncComplete(false, "Failed to sync expenses: " + message);
                }
            }
        });
    }

    private void syncExpensesToFirebase(SyncCallback callback) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        // Get all unsynced expenses (we'll add a sync flag later)
        String query = "SELECT name, note, date, amount, user_id FROM expenses WHERE synced = 0 OR synced IS NULL";
        Cursor cursor = db.rawQuery(query, null);
        
        List<ExpenseData> unsyncedExpenses = new ArrayList<>();
        
        while (cursor.moveToNext()) {
            ExpenseData expense = new ExpenseData();
            expense.name = cursor.getString(0);
            expense.note = cursor.getString(1);
            expense.date = cursor.getString(2);
            expense.amount = cursor.getInt(3);
            expense.userId = cursor.getString(4);
            unsyncedExpenses.add(expense);
        }
        cursor.close();
        
        if (unsyncedExpenses.isEmpty()) {
            Log.d(TAG, "No expenses to sync");
            callback.onSyncComplete(true, "No expenses to sync");
            return;
        }

        Log.d(TAG, "Syncing " + unsyncedExpenses.size() + " expenses");
        syncExpensesBatch(unsyncedExpenses, 0, callback);
    }

    private void syncExpensesBatch(List<ExpenseData> expenses, int index, SyncCallback callback) {
        if (index >= expenses.size()) {
            // All expenses synced successfully
            markExpensesAsSynced();
            callback.onSyncComplete(true, "All expenses synced successfully");
            return;
        }

        ExpenseData expense = expenses.get(index);
        firebaseManager.saveExpense(expense.name, expense.note, expense.date, expense.amount, 
            new FirebaseManager.FirebaseCallback<Void>() {
                @Override
                public void onSuccess(Void result) {
                    Log.d(TAG, "Synced expense: " + expense.name);
                    // Continue with next expense
                    syncExpensesBatch(expenses, index + 1, callback);
                }

                @Override
                public void onFailure(Exception e) {
                    Log.e(TAG, "Failed to sync expense: " + expense.name, e);
                    callback.onSyncComplete(false, "Failed to sync expense: " + expense.name);
                }
            });
    }

    private void syncTransactionsToFirebase(SyncCallback callback) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        // Get all unsynced transactions
        String query = "SELECT bud_name, bud_date, bud_amount, user_id FROM budget WHERE synced = 0 OR synced IS NULL";
        Cursor cursor = db.rawQuery(query, null);
        
        List<TransactionData> unsyncedTransactions = new ArrayList<>();
        
        while (cursor.moveToNext()) {
            TransactionData transaction = new TransactionData();
            transaction.name = cursor.getString(0);
            transaction.date = cursor.getString(1);
            transaction.amount = cursor.getInt(2);
            transaction.userId = cursor.getString(3);
            unsyncedTransactions.add(transaction);
        }
        cursor.close();
        
        if (unsyncedTransactions.isEmpty()) {
            Log.d(TAG, "No transactions to sync");
            callback.onSyncComplete(true, "Sync completed successfully");
            return;
        }

        Log.d(TAG, "Syncing " + unsyncedTransactions.size() + " transactions");
        syncTransactionsBatch(unsyncedTransactions, 0, callback);
    }

    private void syncTransactionsBatch(List<TransactionData> transactions, int index, SyncCallback callback) {
        if (index >= transactions.size()) {
            // All transactions synced successfully
            markTransactionsAsSynced();
            callback.onSyncComplete(true, "Sync completed successfully");
            
            // Show notification
            NotificationHelper.sendNotification(context, "Sync Complete", 
                "All local data has been synced to the cloud");
            return;
        }

        TransactionData transaction = transactions.get(index);
        firebaseManager.saveTransaction(transaction.name, transaction.date, transaction.amount, 
            new FirebaseManager.FirebaseCallback<Void>() {
                @Override
                public void onSuccess(Void result) {
                    Log.d(TAG, "Synced transaction: " + transaction.name);
                    // Continue with next transaction
                    syncTransactionsBatch(transactions, index + 1, callback);
                }

                @Override
                public void onFailure(Exception e) {
                    Log.e(TAG, "Failed to sync transaction: " + transaction.name, e);
                    callback.onSyncComplete(false, "Failed to sync transaction: " + transaction.name);
                }
            });
    }

    private void markExpensesAsSynced() {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        db.execSQL("UPDATE expenses SET synced = 1 WHERE synced = 0 OR synced IS NULL");
        db.close();
    }

    private void markTransactionsAsSynced() {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        db.execSQL("UPDATE budget SET synced = 1 WHERE synced = 0 OR synced IS NULL");
        db.close();
    }

    // Helper classes for data transfer
    private static class ExpenseData {
        String name, note, date, userId;
        int amount;
    }

    private static class TransactionData {
        String name, date, userId;
        int amount;
    }
}
