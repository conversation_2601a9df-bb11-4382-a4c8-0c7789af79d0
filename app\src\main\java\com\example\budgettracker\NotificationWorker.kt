package com.example.budgettracker

import android.content.Context
import android.util.Log
import androidx.work.Worker
import androidx.work.WorkerParameters
import java.text.SimpleDateFormat
import java.util.*

class NotificationWorker(context: Context, workerParams: WorkerParameters) : Worker(context, workerParams) {
    
    companion object {
        private const val TAG = "NotificationWorker"
    }
    
    private val firebaseManager = FirebaseManager()

    override fun doWork(): Result {
        Log.d(TAG, "Starting periodic notification check")
        
        return try {
            checkNotificationConditions()
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Error during notification check", e)
            Result.retry()
        }
    }

    private fun checkNotificationConditions() {
        if (!firebaseManager.isUserAuthenticated()) {
            Log.d(TAG, "User not authenticated, skipping notification check")
            return
        }

        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)

        // Check budget conditions
        checkBudgetConditions(monthYear)
    }

    private fun checkBudgetConditions(monthYear: String) {
        firebaseManager.getOrCreateTransactionSummary(monthYear, object : FirebaseManager.FirebaseCallback<Map<String, Any>> {
            override fun onSuccess(result: Map<String, Any>?) {
                if (result == null) {
                    Log.e(TAG, "Budget conditions result is null")
                    return
                }

                var balance = 0.0
                var target = 0.0
                var budget = 0.0

                if (result.containsKey("trans_balance")) {
                    balance = (result["trans_balance"] as Number).toDouble()
                }
                if (result.containsKey("trans_target")) {
                    target = (result["trans_target"] as Number).toDouble()
                }
                if (result.containsKey("trans_budget")) {
                    budget = (result["trans_budget"] as Number).toDouble()
                }

                // Check for budget alerts (approaching 80% or exceeded)
                if (target > 0) {
                    val percentage = (balance / target) * 100
                    
                    when {
                        percentage >= 90 && percentage < 100 -> {
                            // Critical budget warning
                            createBudgetWarningNotification(percentage, "critical")
                        }
                        percentage >= 80 && percentage < 90 -> {
                            // Regular budget warning
                            createBudgetWarningNotification(percentage, "warning")
                        }
                        percentage >= 100 -> {
                            // Budget exceeded
                            createBudgetExceededNotification(balance - target)
                        }
                    }
                }

                // Check for low balance
                if (balance <= 100.0 && balance > 0) {
                    createLowBalanceNotification(balance)
                }

                // Check for goal achievement
                if (target > 0 && balance >= target) {
                    createGoalAchievementNotification()
                }
            }

            override fun onFailure(e: Exception) {
                Log.e(TAG, "Failed to check budget conditions", e)
            }
        })
    }

    private fun createBudgetWarningNotification(percentage: Double, severity: String) {
        val title = if (severity == "critical") "Critical Budget Alert!" else "Budget Warning"
        val message = String.format("You've spent %.0f%% of your budget this month", percentage)
        
        firebaseManager.saveNotification(title, message, "BUDGET_WARNING", object : FirebaseManager.FirebaseCallback<Void> {
            override fun onSuccess(result: Void?) {
                Log.d(TAG, "Budget warning notification created")
                // Also show local notification
                NotificationHelper.sendNotification(applicationContext, title, message)
            }

            override fun onFailure(e: Exception) {
                Log.e(TAG, "Failed to create budget warning notification", e)
            }
        })
    }

    private fun createBudgetExceededNotification(exceededAmount: Double) {
        val title = "Budget Exceeded!"
        val message = String.format("You've exceeded your budget by $%.2f this month", exceededAmount)
        
        firebaseManager.saveNotification(title, message, "BUDGET_EXCEEDED", object : FirebaseManager.FirebaseCallback<Void> {
            override fun onSuccess(result: Void?) {
                Log.d(TAG, "Budget exceeded notification created")
                NotificationHelper.sendNotification(applicationContext, title, message)
            }

            override fun onFailure(e: Exception) {
                Log.e(TAG, "Failed to create budget exceeded notification", e)
            }
        })
    }

    private fun createLowBalanceNotification(balance: Double) {
        val title = "Low Balance Alert"
        val message = String.format("Your current balance is low: $%.2f. Consider adding funds.", balance)
        
        firebaseManager.saveNotification(title, message, "LOW_BALANCE", object : FirebaseManager.FirebaseCallback<Void> {
            override fun onSuccess(result: Void?) {
                Log.d(TAG, "Low balance notification created")
                NotificationHelper.sendNotification(applicationContext, title, message)
            }

            override fun onFailure(e: Exception) {
                Log.e(TAG, "Failed to create low balance notification", e)
            }
        })
    }

    private fun createGoalAchievementNotification() {
        val title = "Goal Achieved!"
        val message = "Congratulations! You've reached your savings goal for this month."
        
        firebaseManager.saveNotification(title, message, "GOAL_ACHIEVED", object : FirebaseManager.FirebaseCallback<Void> {
            override fun onSuccess(result: Void?) {
                Log.d(TAG, "Goal achievement notification created")
                NotificationHelper.sendNotification(applicationContext, title, message)
            }

            override fun onFailure(e: Exception) {
                Log.e(TAG, "Failed to create goal achievement notification", e)
            }
        })
    }
}
