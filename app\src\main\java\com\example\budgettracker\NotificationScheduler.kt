package com.example.budgettracker

import android.content.Context
import android.util.Log
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.PeriodicWorkRequest
import androidx.work.WorkManager
import java.util.concurrent.TimeUnit

object NotificationScheduler {
    private const val TAG = "NotificationScheduler"
    private const val NOTIFICATION_WORK_NAME = "budget_notification_check"
    
    fun schedulePeriodicNotificationChecks(context: Context) {
        // Create constraints for the work
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED) // Require internet connection
            .setRequiresBatteryNotLow(true) // Don't run when battery is low
            .build()

        // Create periodic work request (runs every 6 hours)
        val notificationWorkRequest = PeriodicWorkRequest.Builder(
            NotificationWorker::class.java,
            6, TimeUnit.HOURS) // Check every 6 hours
            .setConstraints(constraints)
            .addTag("notification_check")
            .build()

        // Schedule the work
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            NOTIFICATION_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP, // Keep existing work if already scheduled
            notificationWorkRequest
        )

        Log.d(TAG, "Periodic notification checks scheduled")
    }

    fun cancelPeriodicNotificationChecks(context: Context) {
        WorkManager.getInstance(context).cancelUniqueWork(NOTIFICATION_WORK_NAME)
        Log.d(TAG, "Periodic notification checks cancelled")
    }

    fun scheduleImmediateNotificationCheck(context: Context) {
        // Create constraints for immediate work
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        // Create one-time work request for immediate execution
        val immediateWorkRequest = OneTimeWorkRequest.Builder(NotificationWorker::class.java)
            .setConstraints(constraints)
            .addTag("immediate_notification_check")
            .build()

        WorkManager.getInstance(context).enqueue(immediateWorkRequest)
        Log.d(TAG, "Immediate notification check scheduled")
    }
}
