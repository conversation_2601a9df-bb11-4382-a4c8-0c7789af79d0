package com.example.budgettracker.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.budgettracker.R
import com.example.budgettracker.models.NotificationItem

class NotificationAdapter : RecyclerView.Adapter<NotificationAdapter.NotificationViewHolder> {
    private var notifications: List<NotificationItem>
    private var clickListener: OnNotificationClickListener? = null

    interface OnNotificationClickListener {
        fun onNotificationClick(notification: NotificationItem, position: Int)
    }

    constructor(notifications: List<NotificationItem>) {
        this.notifications = notifications
    }

    constructor(notifications: List<NotificationItem>, clickListener: OnNotificationClickListener) {
        this.notifications = notifications
        this.clickListener = clickListener
    }

    override fun onCreateViewHolder(parent: View<PERSON>roup, viewType: Int): NotificationViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_notification, parent, false)
        return NotificationViewHolder(view)
    }

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        val notification = notifications[position]

        holder.titleTextView.text = notification.getTitle()
        holder.messageTextView.text = notification.getMessage()
        holder.timestampTextView.text = notification.getTimestamp()

        // Set icon and background based on notification type
        when (notification.getType()) {
            NotificationItem.NotificationType.BUDGET_WARNING,
            NotificationItem.NotificationType.BUDGET_EXCEEDED -> {
                holder.iconImageView.setImageResource(R.drawable.warning_24px)
                holder.iconBackground.setBackgroundResource(R.drawable.notification_warning_background)
            }
            NotificationItem.NotificationType.TRANSACTION_ADDED,
            NotificationItem.NotificationType.EXPENSE_ADDED -> {
                holder.iconImageView.setImageResource(R.drawable.attach_money)
                holder.iconBackground.setBackgroundResource(R.drawable.notification_success_background)
            }
            NotificationItem.NotificationType.GOAL_ACHIEVED -> {
                holder.iconImageView.setImageResource(R.drawable.star_24px)
                holder.iconBackground.setBackgroundResource(R.drawable.notification_info_background)
            }
            NotificationItem.NotificationType.LOW_BALANCE -> {
                holder.iconImageView.setImageResource(R.drawable.warning_24px)
                holder.iconBackground.setBackgroundResource(R.drawable.notification_warning_background)
            }
        }

        // Set read/unread state
        if (notification.isRead()) {
            holder.itemView.alpha = 0.7f
        } else {
            holder.itemView.alpha = 1.0f
        }

        // Set click listener
        holder.itemView.setOnClickListener {
            clickListener?.onNotificationClick(notification, position)
        }
    }

    override fun getItemCount(): Int {
        return notifications.size
    }

    fun updateNotifications(newNotifications: List<NotificationItem>) {
        this.notifications = newNotifications
        notifyDataSetChanged()
    }

    class NotificationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val titleTextView: TextView = itemView.findViewById(R.id.notification_title)
        val messageTextView: TextView = itemView.findViewById(R.id.notification_message)
        val timestampTextView: TextView = itemView.findViewById(R.id.notification_timestamp)
        val iconImageView: ImageView = itemView.findViewById(R.id.notification_icon)
        val iconBackground: LinearLayout = itemView.findViewById(R.id.notification_icon_background)
    }
}
