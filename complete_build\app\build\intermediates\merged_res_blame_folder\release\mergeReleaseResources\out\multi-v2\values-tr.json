{"logs": [{"outputFile": "com.example.budgettracker.app-mergeReleaseResources-58:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7ac808904cd7e03e33d69da647d3102c\\transformed\\browser-1.4.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6612,7217,7323,7430", "endColumns": "99,105,106,105", "endOffsets": "6707,7318,7425,7531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\68670c8646fcaa8caaac8087bdb8b46a\\transformed\\biometric-1.2.0-alpha05\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,366,472,564,709,822,935,1054,1183,1308,1452,1569,1712,1807,1966,2092,2216,2357,2495,2620,2715,2843,2934,3058,3156,3287", "endColumns": "173,136,105,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "224,361,467,559,704,817,930,1049,1178,1303,1447,1564,1707,1802,1961,2087,2211,2352,2490,2615,2710,2838,2929,3053,3151,3282,3382"}, "to": {"startLines": "35,36,63,65,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,146,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3237,3411,6506,6712,6959,7104,7536,7649,7768,7897,8022,8166,8283,8426,8521,8680,8806,8930,9071,13967,14273,14368,14496,14587,14711,14809,14940", "endColumns": "173,136,105,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "3406,3543,6607,6799,7099,7212,7644,7763,7892,8017,8161,8278,8421,8516,8675,8801,8925,9066,9204,14087,14363,14491,14582,14706,14804,14935,15035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a0f7f4f98ac1f62eb9bfd0e305d4a8e3\\transformed\\navigation-ui-2.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "13666,13771", "endColumns": "104,116", "endOffsets": "13766,13883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8fe6d8e6447df98388c2372dd5db4cda\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,14092", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,14167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\24da3e80982647016081752b11f5cf0e\\transformed\\jetified-play-services-base-18.0.1\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4255,4365,4520,4656,4761,4908,5038,5165,5418,5590,5697,5854,5988,6133,6300,6362,6426", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "4360,4515,4651,4756,4903,5033,5160,5266,5585,5692,5849,5983,6128,6295,6357,6421,6501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c481deb13e95af8c1cf10d239f737420\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5271", "endColumns": "146", "endOffsets": "5413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c377da76ffd722c11f7e2a995dabf1f9\\transformed\\material-1.10.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2802,2889,2965,3018,3072,3138,3208,3285,3368,3448,3519,3594,3672,3743,3844,3929,4018,4113,4206,4278,4350,4446,4498,4584,4651,4735,4825,4887,4951,5014,5084,5178,5280,5369,5469,5526,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2797,2884,2960,3013,3067,3133,3203,3280,3363,3443,3514,3589,3667,3738,3839,3924,4013,4108,4201,4273,4345,4441,4493,4579,4646,4730,4820,4882,4946,5009,5079,5173,5275,5364,5464,5521,5579,5658"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3548,3623,3698,3775,3874,3965,4061,4173,6804,6868,9209,9286,9347,9438,9501,9564,9623,9692,9755,9809,9917,9975,10037,10091,10164,10285,10369,10460,10600,10677,10753,10884,10971,11047,11100,11154,11220,11290,11367,11450,11530,11601,11676,11754,11825,11926,12011,12100,12195,12288,12360,12432,12528,12580,12666,12733,12817,12907,12969,13033,13096,13166,13260,13362,13451,13551,13608,13888", "endLines": "5,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "310,3618,3693,3770,3869,3960,4056,4168,4250,6863,6954,9281,9342,9433,9496,9559,9618,9687,9750,9804,9912,9970,10032,10086,10159,10280,10364,10455,10595,10672,10748,10879,10966,11042,11095,11149,11215,11285,11362,11445,11525,11596,11671,11749,11820,11921,12006,12095,12190,12283,12355,12427,12523,12575,12661,12728,12812,12902,12964,13028,13091,13161,13255,13357,13446,13546,13603,13661,13962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5e6cb0677104d92c5efdb2f091984aa9\\transformed\\core-1.9.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "14172", "endColumns": "100", "endOffsets": "14268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1c641e390a7be6a0bccc310f791f99a5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,120", "endOffsets": "159,280"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3007,3116", "endColumns": "108,120", "endOffsets": "3111,3232"}}]}]}