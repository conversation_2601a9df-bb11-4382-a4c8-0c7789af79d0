<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:id="@+id/root_layout">

    <!-- Modern Header -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:scaleType="fitCenter"
            android:tint="?attr/colorOnSurface" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Create Account"
            android:textColor="?attr/colorOnSurface"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_layout"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Welcome Message -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <ImageView
                    android:id="@+id/register_logo"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/logo"
                    android:scaleType="fitCenter"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Join Budget Tracker"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Create your account to start managing your finances"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="14sp"
                    android:alpha="0.7"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Registration Form Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- Username Input -->
                    <LinearLayout
                        android:id="@+id/username_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Username"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp">

                            <EditText
                                android:id="@+id/username_input"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="Enter your username"
                                android:background="@android:color/transparent"
                                android:inputType="text"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Email Input -->
                    <LinearLayout
                        android:id="@+id/email_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Email Address"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp">

                            <EditText
                                android:id="@+id/email_input"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="Enter your email"
                                android:background="@android:color/transparent"
                                android:inputType="textEmailAddress"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Password Input -->
                    <LinearLayout
                        android:id="@+id/password_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Password"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/password_input"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="Enter your password"
                                android:background="@android:color/transparent"
                                android:inputType="textPassword"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                            <ImageView
                                android:id="@+id/password_eye"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center_vertical"
                                android:contentDescription="Toggle password visibility"
                                android:src="@drawable/eye_icon"
                                android:tint="?attr/colorOnSurface" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Confirm Password Input -->
                    <LinearLayout
                        android:id="@+id/password_container_confrim"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="24dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Confirm Password"
                            android:textColor="?attr/colorOnSurface"
                            android:textSize="14sp"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/edittext_background"
                            android:padding="16dp"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/password_input_confrim"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="Confirm your password"
                                android:background="@android:color/transparent"
                                android:inputType="textPassword"
                                android:textColor="?attr/colorOnSurface"
                                android:textColorHint="?attr/colorOnSurface"
                                android:textSize="16sp" />

                            <ImageView
                                android:id="@+id/password_eye_confrim"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center_vertical"
                                android:contentDescription="Toggle password visibility"
                                android:src="@drawable/eye_icon"
                                android:tint="?attr/colorOnSurface" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Sign Up Button -->
                    <Button
                        android:id="@+id/signup_button"
                        style="@style/MediumButton"
                        android:text="Create Account" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Terms and Privacy -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="By creating an account, you agree to our Terms of Service and Privacy Policy"
                android:textColor="?attr/colorOnSurface"
                android:textSize="12sp"
                android:alpha="0.7"
                android:gravity="center"
                android:layout_marginBottom="80dp" />

        </LinearLayout>

    </ScrollView>

    <!-- Loading Spinner Overlay -->
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center"
        android:background="@color/scrim_overlay">

        <include layout="@layout/spinner" />

    </RelativeLayout>

</RelativeLayout>
