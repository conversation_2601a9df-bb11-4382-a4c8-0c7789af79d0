package com.example.budgettracker;

import java.util.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface ApiService {
    // General GET request
    @GET("/api/{table}")
    Call<List<Record>> getRecords(
            @Path("table") String table,
            @Query("monthyear") String monthyear,
            @Query("user_id") String user_id
    );

    // General PUT request
    @PUT("/api/{table}")
    Call<Void> updateRecord(
            @Path("table") String table,
            @Query("user_id") String user_id,
            @Query("date") String date,
            @Body Map<String, Object> data
    );

    // General DELETE request
    @DELETE("/api/{table}/{id}")
    Call<Void> deleteRecord(
            @Path("table") String table,
            @Path("id") String id
    );

    // General POST request
    @POST("/api/{table}")
    Call<Void> createRecord(
            @Path("table") String table,
//            @Body Record record
            @Body Map<String, Object> data
    );


    @POST("/api/all/sync")
    Call<Void> syncAllData(@Body Map<String, Object> data);
}
